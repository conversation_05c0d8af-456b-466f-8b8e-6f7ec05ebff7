package data.keyless.utils

import core.http.client.HttpHeader
import core.http.client.HttpRequest
import data.common.preferences.Preferences
import data.common.utils.ensureStartsWith
import kotlinx.serialization.json.JsonElement

internal fun get(url: String, authentication: String? = null) = HttpRequest(
    url = url,
    method = HttpRequest.Method.GET,
    headers = listOf(
        HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
        HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication ?: "")
    )
)

internal fun post(url: String, body: JsonElement?, authentication: String? = null) = HttpRequest(
    url = url,
    method = HttpRequest.Method.POST,
    headers = listOf(
        HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
        HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication ?: "")
    ),
    body = body
)

internal fun patch(url: String, body: JsonElement?, authentication: String? = null) = HttpRequest(
    url = url,
    method = HttpRequest.Method.PATCH,
    headers = listOf(
        HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
        HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication ?: "")
    ),
    body = body
)

internal fun delete(url: String, authentication: String? = null) = HttpRequest(
    url = url,
    method = HttpRequest.Method.DELETE,
    headers = listOf(
        HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
        HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication ?: "")
    )
)

internal fun bearer(authentication: String) = authentication.ensureStartsWith("Bearer ")

internal fun authentication() = bearer(Preferences.authenticationToken.get())