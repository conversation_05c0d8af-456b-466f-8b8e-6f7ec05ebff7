package data.keyless.home

import core.common.serialization.json
import data.common.isAfter
import data.common.lastServerTime
import data.common.now
import data.common.preferences.Preferences
import data.common.preferences.Roles
import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UserHomePostRequest(
    val uid: String,
    @SerialName("is_admin")
    val isAdmin: Boolean
)

@Serializable
data class UserHomeResponse(
    val locks: List<LockSummary> = ArrayList(),
    val properties: List<PropertySummary> = ArrayList(),
    val success: Boolean = false,
    val message: String = "",
    @SerialName("assignment_id")
    val assignmentId: String = "",
    val logout: Boolean = false,
    @SerialName("is_paid")
    val isPaid: Boolean = false,
    val totalUnreadNotification: Int = 0
) {
    companion object {
        fun fromDto(dto: String): UserHomeResponse = json.decodeFromString(dto)
    }
}

@Serializable
data class PropertySummary(
    @SerialName("_id")
    val id: String = "",
    @SerialName("building_name")
    val buildingName: String = "",
    val icon: List<Icon> = ArrayList(),
    val count: Int = 0
)

@Serializable
data class LockSummary(
    val assignment: Assignment? = Assignment(),
    val lock: LockDetails = LockDetails(),
    @SerialName("owner_id")
    val ownerId: String = "",
    @SerialName("unit_id")
    val unitId: String = "",
    @SerialName("booking_number")
    val bookingNumber: String = "",
    val companyCheckin: Boolean = false,
    val checkin: Boolean = false,
    val totalCheckins: Int = 0,
    @SerialName("property_details")
    val propertyDetails: PropertyDetails = PropertyDetails(),
    val passcode: String = ""
) {
    val isAssignmentTimeLimitedRole get() = Preferences.isGuest() || Roles.isLimitedTimeAccessRoles(Preferences.role.get())
    val isLockActiveForUser: Boolean
        get() {
            if (!isAssignmentTimeLimitedRole) return true
            val validFrom = assignment?.assignmentData?.validFromDateTime ?: return false
            val serverTime = lastServerTime()

            if (serverTime != null && !validFrom.isAfter(serverTime)) return false

            return validFrom.isAfter(now())
        }


}

@Serializable
data class PropertyDetails(
    val id: String = "",
    val floor: String = "",
    val latitude: String = "",
    val longitude: String = "",
    val name: String = "",
    @SerialName("room_number")
    val roomNumber: String = "",
    @SerialName("laundary_number")
    val laundaryNumber: String = "",
    @SerialName("grocery_number")
    val groceryNumber: String = "",
    @SerialName("appartment_number")
    val appartmentNumber: String = "",
    @SerialName("support_call_number")
    val supportCallNumber: String = "",
    @SerialName("support_whatsapp_number")
    val supportWhatsappNumber: String = "",
    @SerialName("map_id")
    val mapId: String = "",
    val area: String = "",
    @SerialName("building_name")
    val buildingName: String = "",
    val icon: List<Icon> = ArrayList()
)

@Serializable
data class LockDetails(
    @SerialName("_id")
    val id: String = "",
    @SerialName("access_key")
    val accessKey: String = "",
    @SerialName("battery_level")
    val batteryLevel: Int = -1,
    @SerialName("lock_uid")
    val lockUid: String = "",
    val name: String = "",
    val provider: String = "",
    @SerialName("unique_key")
    val uniqueKey: String = "",
    @SerialName("unit_id")
    val unitId: String = "",
    @SerialName("internal_id")
    val internalId: String = "",
    @SerialName("encrypted_key")
    val encryptedKey: String = "",
    @SerialName("privacy_mode")
    val privacyMode: Boolean = false,
    val primary: Boolean = false,
    @SerialName("privacy_permission")
    val privacyPermission: Boolean = false,
    @SerialName("privacy_owner")
    val privacyOwner: Boolean = false,
    val icon: List<Icon> = ArrayList(),
    @SerialName("property_details")
    val propertyDetails: PropertyDetails = PropertyDetails(),
    val firmwareUpdated: Boolean = false,
    val firmwareVersion: String = "",
    val firmwareAvailableVersion: String = ""
)

@Serializable
data class Icon(
    @SerialName("_id")
    val id: String = "",
    val name: String = "",
    val icon: String = "",
    val type: String = "",
    val createdAt: String = ""
)

@Serializable
data class Assignment(
    @SerialName("assignment_data")
    val assignmentData: AssignmentData = AssignmentData(),
    @SerialName("time_ranges")
    val timeRanges: List<TimeRange> = ArrayList()
)

@Serializable
data class TimeRange(
    @SerialName("allowed_days")
    val allowedDays: List<Int> = ArrayList(),
    @SerialName("time_slot")
    val timeSlot: TimeSlot = TimeSlot()
)

@Serializable
data class TimeSlot(
    @SerialName("start_hour")
    val startHour: Int = 0,
    @SerialName("start_min")
    val startMin: Int = 0,
    @SerialName("end_hour")
    val endHour: Int = 0,
    @SerialName("end_min")
    val endMin: Int = 0
)

@Serializable
data class AssignmentData(
    @SerialName("lock_id")
    val lockId: String = "",
    @SerialName("time_profile_id")
    val timeProfileId: TimeProfileId = TimeProfileId(),
    @SerialName("valid_from")
    val validFrom: String = "",
    @SerialName("valid_to")
    val validTo: String = ""
) {
    val validFromDateTime = Instant.parse(validFrom)
}

@Serializable
data class TimeProfileId(
    val name: String = ""
)