package data.keyless.home

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class HomeRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun fetch(request: UserHomePostRequest) = logger.async {
        val url = "$hostname/user/home"
        val request = post(url, json.encodeToJsonElement(request))
        val response = client.request(request).value(UserHomeResponse.serializer())

        Preferences.locksJson.set(json.encodeToString(response))

        return@async response
    }
}

val Preferences.locks: UserHomeResponse
    get() = json.decodeFromString(locksJson.get())