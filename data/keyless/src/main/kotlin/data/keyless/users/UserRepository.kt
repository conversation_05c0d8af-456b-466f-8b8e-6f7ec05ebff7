package data.keyless.users

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.users.models.CheckUserRequest
import data.keyless.users.models.CheckUserResponse
import data.keyless.utils.authentication
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class UserRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun check(body: CheckUserRequest) = logger.async {
        val url = "$hostname/user/user-loggedin"
        val request = post(url, json.encodeToJsonElement(body), authentication())

        return@async client.request(request).value(CheckUserResponse.serializer())
    }
}
