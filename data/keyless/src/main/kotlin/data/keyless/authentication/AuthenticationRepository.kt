package data.keyless.authentication

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.authentication.models.ChangePasswordRequest
import data.keyless.authentication.models.ChangePasswordResponse
import data.keyless.authentication.models.ClaimKeyRequest
import data.keyless.authentication.models.ClaimKeyResponse
import data.keyless.authentication.models.LoginRequest
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.ResendOtpRequest
import data.keyless.authentication.models.SendEmailOtpRequest
import data.keyless.authentication.models.SendOtpMobileRequest
import data.keyless.authentication.models.SendOtpResponse
import data.keyless.authentication.models.SignUpRequest
import data.keyless.authentication.models.SignupResponse
import data.keyless.authentication.models.VerifyEmailOtpRequest
import data.keyless.authentication.models.VerifyOtpRequest
import data.keyless.utils.bearer
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class AuthenticationRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun login(body: LoginRequest) = logger.async {
        val url = "$hostname/user/login"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(LoginResponse.serializer())
    }

    suspend fun sendOtpSms(body: SendOtpMobileRequest) = logger.async {
        val url = "$hostname/user/sendotp_mobile"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun sendOtpWhatsApp(body: SendOtpMobileRequest) = logger.async {
        val url = "$hostname/user/sendotp-whatsapp"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun sendOtpPhoneCall(body: SendOtpMobileRequest) = logger.async {
        val url = "$hostname/user/send-voice-otp"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun resendOtp(body: ResendOtpRequest) = logger.async {
        val url = "$hostname/user/resend_otp"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun verifyOtp(body: VerifyOtpRequest) = logger.async {
        val url = "$hostname/user/verifyLoginOTP"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun verifyEmailOtp(body: VerifyEmailOtpRequest) = logger.async {
        val url = "$hostname/user/verify_emailotp"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun sendEmailOtp(body: SendEmailOtpRequest) = logger.async {
        val url = "$hostname/user/sendotp_email"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SendOtpResponse.serializer())
    }

    suspend fun signup(body: SignUpRequest) = logger.async {
        val url = "$hostname/user/username"
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(SignupResponse.serializer())
    }

    suspend fun changePassword(body: ChangePasswordRequest, token: String) = logger.async {
        val url = "$hostname/user/change_password"
        val request = post(url, json.encodeToJsonElement(body), bearer(token))

        return@async client.request(request).value(ChangePasswordResponse.serializer())
    }

    suspend fun claimKey(retrievalCode: String) = logger.async {
        val url = "$hostname/user/claim-key-guest"
        val body = ClaimKeyRequest(bookingNumber = retrievalCode)
        val request = post(url, json.encodeToJsonElement(body))

        return@async client.request(request).value(ClaimKeyResponse.serializer())
    }
}