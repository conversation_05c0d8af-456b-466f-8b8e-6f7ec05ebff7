package data.keyless.authentication.models

import android.annotation.SuppressLint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class ChangePasswordRequest(
    @SerialName("old_password")
    val oldPassword: String,
    
    @SerialName("password")
    val password: String,
    
    @SerialName("confirm_password")
    val confirmPassword: String,
    
    @SerialName("user_id")
    val userId: String,
    
    @SerialName("is_admin")
    val isAdmin: Boolean
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class ChangePasswordResponse(
    val success: Boolean = false,
    val message: String = "",
    val id: String? = null
)
