package data.keyless.authentication.models

import android.annotation.SuppressLint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class SendOtpMobileRequest(
    @SerialName("country_code")
    val countryCode: String,
    @SerialName("mobile_number")
    val mobileNumber: String,
    @SerialName("user_type")
    val userType: String,
    @SerialName("user_id")
    val userId: String
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class SendOtpResponse(
    val message: String = "",
    val name: String = "",
    @SerialName("_id")
    private val _id: String = "",
    private val id: String = "",
    @SerialName("profile_status")
    private val profileStatus: Int = -1,
    private val success: Boolean = false,
    val token: String = "",
    @SerialName("user_type")
    val userType: String = "",
    @SerialName("first_name")
    val firstName: String = "",
    @SerialName("last_name")
    val lastName: String = "",
    @SerialName("company_name")
    val companyName: String = "",
    @SerialName("contact_person_name")
    val contactPersonName: String = "",
    val role: String = "",
    val data: String = "",
    private val status: Boolean = false
) {
    val isSuccess = status || success

    val userId: String = _id.ifEmpty { id }
    val profileCompletionStatus = ProfileStatus.fromInt(profileStatus)
}


@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class ResendOtpRequest(
    val method: String,
    @SerialName("userID")
    val userId: String,
    val uid: String,
    @SerialName("device_token")
    val deviceToken: String,
    @SerialName("country_code")
    val countryCode: String? = null,
    @SerialName("mobile_number")
    val mobileNumber: String? = null,
    val email: String? = null
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class VerifyOtpRequest(
    val otp: Int,
    @SerialName("userID")
    val userId: String,
    val method: String,
    @SerialName("device_type")
    val deviceType: String,
    val uid: String,
    @SerialName("device_token")
    val deviceToken: String,
    @SerialName("country_code")
    val countryCode: String,
    @SerialName("mobile_number")
    val mobileNumber: String
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class VerifyEmailOtpRequest(
    val otp: Int,
    @SerialName("userID")
    val userId: String,
    val uid: String,
    @SerialName("device_token")
    val deviceToken: String,
    val email: String
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class SendEmailOtpRequest(
    val email: String,
    @SerialName("userID")
    val userId: String
)
