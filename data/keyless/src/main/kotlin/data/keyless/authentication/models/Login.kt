package data.keyless.authentication.models

import android.annotation.SuppressLint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class LoginRequest(
    @SerialName("method")
    val method: String,
    val password: String,
    val uid: String,
    @SerialName("device_token")
    val deviceToken: String,
    val user: String,
    @SerialName("device_type")
    val deviceType: String = "android",
    @SerialName("device_model")
    val deviceModel: String
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class LoginResponse(
    val email: String = "",
    private val _id: String = "",
    private val id: String = "",
    val message: String = "",
    val name: String = "",
    @SerialName("profile_status")
    private val profileStatus: Int = -1,
    val success: Boolean = false,
    val token: String = "",
    @SerialName("user_type")
    val userType: String = "",
    @SerialName("first_name")
    val firstName: String = "",
    @SerialName("last_name")
    val lastName: String = "",
    @SerialName("company_name")
    val companyName: String = "",
    @SerialName("contact_person_name")
    val contactPersonName: String = "",
    val role: String = ""
) {
    val userId = _id.ifEmpty { id }
    val status = ProfileStatus.fromInt(profileStatus)
}

enum class ProfileStatus(val value: Int) {
    VERIFY_PHONE(0),
    VERIFY_EMAIL(1),
    COMPLETE(-1);

    companion object {
        fun fromInt(value: Int): ProfileStatus {
            return when (value) {
                0 -> VERIFY_PHONE
                1 -> VERIFY_EMAIL
                else -> COMPLETE
            }
        }
    }
}

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class SignUpRequest(
    @SerialName("first_name")
    val firstName: String,
    @SerialName("last_name")
    val lastName: String,
    val username: String,
    val password: String,
    @SerialName("confirmPassword")
    val confirmPassword: String,
    @SerialName("device_type")
    val deviceType: String,
    @SerialName("device_model")
    val deviceModel: String
)

@SuppressLint("UnsafeOptInUsageError")
@Serializable
data class SignupResponse(
    val message: String = "",
    val name: String = "",
    val id: String = "",
    @SerialName("profile_status")
    val profileStatus: ProfileStatus = ProfileStatus.COMPLETE,
    val success: Boolean = false,
    val token: String = "",
    @SerialName("user_type")
    val userType: String = ""
)