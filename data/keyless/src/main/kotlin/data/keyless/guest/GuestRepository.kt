package data.keyless.guest

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.utils.authentication
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class GuestRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun claimKey(request: ClaimKeyRequest) = logger.async {
        val url = "$hostname/user/claim-key-guest"
        val request = post(url, json.encodeToJsonElement(request))

        return@async client.request(request).value(ClaimKeyResponse.serializer())
    }

    suspend fun logService(request: LogServicePostRequest) = logger.async {
        val url = "$hostname/user/guest-services"
        val request = post(url, json.encodeToJsonElement(request), authentication())

        client.request(request)
    }
}