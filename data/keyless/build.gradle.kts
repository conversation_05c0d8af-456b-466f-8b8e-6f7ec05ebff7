plugins {
    id("keyless.android.library")
    id("kotlinx.serialization")
    id("config.values")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-ktor"))
    implementation(project(":core-monitoring-test"))
    implementation(project(":data-common"))
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val baseImageUrl = getLocalProperty("base.image.url") as String? ?: throw GradleException(
        "Missing base.image.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "baseImageUrl" to baseImageUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}