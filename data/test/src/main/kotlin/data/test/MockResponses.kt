package data.test

object MockResponses {
    val postUserLogin = ResourceLoader("<EMAIL>")
    val postUserLoginFail = ResourceLoader("<EMAIL>")

    val postSignup = ResourceLoader("<EMAIL>")
    val postSignupFail = ResourceLoader("<EMAIL>")

    val postUserSendOtpMobile = ResourceLoader("POST@user.sendotp_mobile.json")
    val postUserSendOtpWhatsapp = ResourceLoader("<EMAIL>")
    val postUserSendOtpCall = ResourceLoader("<EMAIL>")
    val postUserSendOtpCallFailWaitAMinute = ResourceLoader("<EMAIL>(wait_a_minute).json")

    val postUserSendOtpEmail = ResourceLoader("POST@user.sendotp_email.json")
    val postUserSendOtpEmailFailWaitAMinute = ResourceLoader("POST@user.sendotp_email.FAIL(wait_a_minute).json")

    val postVerifyOtp = ResourceLoader("<EMAIL>")
    val postVerifyOtpFail = ResourceLoader("<EMAIL>")

    val postVerifyEmailOtp = ResourceLoader("POST@user.verify_emailotp.json")
    val postVerifyEmailOtpFail = ResourceLoader("POST@user.verify_emailotp.FAIL.json")

    val postResendOtp = ResourceLoader("POST@user.resend_otp.json")

    val postChangePassword = ResourceLoader("POST@user.change_password.json")
    val postChangePasswordFail = ResourceLoader("POST@user.change_password.FAIL.json")

    val postUserHome = ResourceLoader("<EMAIL>")
}