{"success": true, "locks": [{"_id": "66a34628ad25d3eb76a90e42", "status": 1, "lock_id": "66a1f778f606d950676eb70d", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "time_profile_id": "63d8c179d032f69aee77dc85", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "company_id": "657bee62ef1b994aea3f8043", "booking_number": "", "passcode": "", "passcodeId": "", "checkin": false, "assigned_at": "2024-07-26T06:46:00.671Z", "__v": 0, "lock_id_string": "66a1f778f606d950676eb70d", "valid_from_date": "2021-01-01T20:55:26.453Z", "valid_to_date": "2121-01-01T20:55:26.453Z", "unit_id": "6626188af3934418e0062ee1", "lock": {"_id": "66a1f778f606d950676eb70d", "alloted": true, "status": 1, "name": "Elkhoudiry AirBnk", "image": "", "unique_key": "M501-2401713", "access_key": "QKVQ/gq+AAX29VALGbnvSg/ODGCI4IVUkSxBZ56sCWUumxoGoIkcfnz1Eme/TF2b/wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//280kizX2xxbsZqbKXzP4A2oUZMtHAdBvD1ovWr+3MBHLpl4WgHXen6QnP7wWV5Mwrwp++6XTAcupECUJbt9NgQ4qZlVxTNm7OgQ8TBSIt71KxQfNH0iMUHJ0KRMwBVXlysgpW7Tzt+sl3GoX3zF6n/BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR//8FWVldClKlHSU9aKvRUf//BVlZXQpSpR0lPWir0VH//wVZWV0KUqUdJT1oq9FR/+i/cVq1OcLiYGSr/pfPSVQ=2921afa5fea1c600fc62e42ef4243f61", "provider": "LockWise", "lock_uid": "", "battery_level": 3, "inventory_status": 1, "internal_id": "KL-170925", "size": "", "colour": "", "order_id": "", "icon_id": "6397163b4925f8fe1073d364", "messer_token": "", "messer_enddate": null, "installationId": "", "installationStatus": 0, "installationComment": "", "orderAssignType": 0, "extendedWarranty": 0, "warrantyStartDate": null, "listing_id": "", "firmwareId": "", "ojiLockId": "", "mac": "", "oji_battery_level": 0, "installationCompletedBy": "657bee62ef1b994aea3f8041", "createdAt": "2024-07-24T08:09:09.568Z", "__v": 0, "activation_date": "2024-07-26T06:46:00.676Z", "icon": [{"_id": "6397163b4925f8fe1073d364", "name": "Bathroom", "icon": "icons/Bathroom.png", "type": "lock", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "property_id": "657beed7ef1b994aea3f8092", "encrypted_key": "U2FsdGVkX19+KqkGfupowexyMH/uslkh4crKdQz43wBRVUtDpR36Xbox8Ns7EgHKlCY6tbzvNc2ROSZ9x4DOWmV6zBDIksWZx3q4COnWa8dv31mBKJDdxU4+TWoEzleDMelVrP2lz4/DoXH2Wv4bHzGm/NvOLr9xMlOzE5nG3tL47OOBxOM1cEoZ7BfKttR7aL4M4jzJybKS6VwZM0++YBDmpDJwv7UcxEFCFm3RlgdFeLVYQi7obX03reXCamijsUpyxs2gbDoB5lFFDX9eQ44XW9OG1GJdbojclF8QO3ai6FfOd9E/4pbggPYENrHhRxrOEqWFk6TDjhRzTsxwOub5tjETU6H/6W+BC5Uh0nyp9bmiHWNjpu4uFFClGpQyaekEszmp05JA8hjHCVoPjeFtnlR24VFmItD065aacO66zOpS1N4rQmsNUNfQt/LTsP2yJE4MFYWWeToXTcpr18YZNyC0uRcMqlgo+y3l1cUT0U9bxQPc5+Xn8M8nlZOyUbIH3VptsEryeax/1l6UKplRb9A/c1IYE6QXKqYGCtW8bJPXzRIKs6732X9uPJLtTOhVuyNm3Ia+SlSAlNcktPmt9vMO0uKF7S/ZEA6Zf5NzxwMLecrhUnDXBAgbvPCSBHmoHmN6182aX2BWqTDdEXXHVkI+jHQaA6oveHM0LuNx/lzQpCfVdlPYx5eka243ME2jvMPxaGLTxcL+iZv2ahBDcizS+yYIQCVr48YjHMLMzamz6qduOujPafe/OLS1fjmXky8C19MlWa2CeJvigAdnbEOtjM+Uu08IQsi/nxnjCvBDZOXZbEYKK4HtAPiUVGdRJu6trwHenB/4pQbE56HhhHvf+ZexgnCi3XxhbVTamYgLQGqzEFd8xFHpY6YDOURBBG3V6p/Mqjd6usrCh0KqN2Au95aXP+0MCvJJ64Gwd9iMJ5DD+rK53KAQCxH7IpwkliR/lZtva/kjDPtuPw==", "privacy_permission": true, "primary": true, "privacy_owner": false, "privacy_mode": false, "firmware_updated": false, "firmwareVersion": "", "firmwareAvailable": "latest-production-test"}, "dndlock": [], "allotment": {"_id": "66a34627ad25d3eb76a90e3a", "lock_type": "Lockwise", "lock_id": "66a1f778f606d950676eb70d", "alloted_by": "657bee62ef1b994aea3f8043", "manager_type": "person", "manager_id": "657bee62ef1b994aea3f8043", "property_id": "657beed7ef1b994aea3f8092", "status": 1, "created_at": "2024-07-26T06:45:59.873Z", "__v": 0}, "maplockproperty": {"_id": "66a34627ad25d3eb76a90e37", "property_id": "657beed7ef1b994aea3f8092", "lock_id": "66a1f778f606d950676eb70d", "floor_number": "3", "appartment_number": "321", "room_number": "321", "created_at": "2024-07-26T06:45:59.869Z", "__v": 0}, "property_id_object": "657beed7ef1b994aea3f8092", "property_details": {"_id": "657beed7ef1b994aea3f8092", "latitude": "25.25932689371647", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.41330356429146", "emirate": "Dubai", "area": "7C57+P79 - Tunis St - opposite to Etisalat Academy - Muhaisnah - Muhaisanah 2 - Dubai - United Arab Emirates", "building_name": "QA Test Office", "total_floors": 12, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2023-12-15T06:14:47.896Z", "__v": 0, "name": "QA Test Office", "floor_number": "3", "floor": "3", "map_id": "66a34627ad25d3eb76a90e37", "id": "657beed7ef1b994aea3f8092", "appartment_number": "321", "room_number": "321"}, "assigned_user": [], "assignment": {"time_ranges": [{"_id": "63d8c179d032f69aee77dc87", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "forever", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [1, 1, 1, 1, 1, 1, 1], "created_at": "2023-01-31T07:21:29.435Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc89", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.439Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8b", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.442Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8d", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.445Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8f", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.449Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc91", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.456Z", "__v": 0}], "assignment_data": {"time_profile_id": {"_id": "63d8c179d032f69aee77dc85", "iseo_id": 88, "created_by": "0", "status": true, "name": "forever_routine", "doors_linked": [], "created_at": "2023-01-31T07:21:29.430Z", "__v": 0}, "_id": "66a34628ad25d3eb76a90e42", "status": 1, "lock_id": "66a1f778f606d950676eb70d", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "assigned_at": "2024-07-26T06:46:00.671Z"}}, "checkins": [], "totalCheckins": 0, "owner_id": "657bee62ef1b994aea3f8043", "companyCheckin": true, "privacy": false, "privacy_changed": false}, {"_id": "66ffc9a543146d36c187e038", "status": 1, "lock_id": "65f81e5eba4c466f13501b93", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "time_profile_id": "63d8c179d032f69aee77dc85", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "company_id": "", "booking_number": "", "passcode": "", "passcodeId": "", "checkin": false, "assigned_at": "2024-10-04T10:55:33.280Z", "__v": 0, "lock_id_string": "65f81e5eba4c466f13501b93", "valid_from_date": "2021-01-01T20:55:26.453Z", "valid_to_date": "2121-01-01T20:55:26.453Z", "unit_id": "65f2f2048028212c5afb0d55", "lock": {"_id": "65f81e5eba4c466f13501b93", "alloted": true, "status": 1, "name": "Rayonica", "unique_key": "RC0800178560FEFACDF", "access_key": "ffMM", "provider": "Keyless", "lock_uid": "0", "battery_level": 2, "time_zone": "UTC+04:00", "inventory_status": 2, "internal_id": "KL-737310", "size": "40*40", "colour": "black", "order_id": "", "icon_id": "6397163b4925f8fe1073d366", "messer_token": "", "messer_enddate": null, "iseo_id": "", "installationId": "", "installationStatus": null, "installationComment": "", "orderAssignType": 0, "extendedWarranty": 0, "warrantyStartDate": "2024-04-22T08:40:46.278Z", "listing_id": "", "firmwareId": "673449410b02ccd62933a940", "ojiLockId": "", "mac": "", "oji_battery_level": 0, "installationCompletedBy": "657bee62ef1b994aea3f8041", "createdAt": "2024-03-18T10:58:38.347Z", "__v": 0, "installedDate": null, "activation_date": "2024-10-04T10:55:33.282Z", "tags": {}, "icon": [{"_id": "6397163b4925f8fe1073d366", "name": "Cabin", "icon": "icons/Cabin.png", "type": "lock", "createdAt": "2022-12-12T11:53:31.191Z", "__v": 0}], "property_id": "65950533d51e12540a653056", "encrypted_key": "U2FsdGVkX1/+iT8yNbVHDL0w5PLSZsHfqax94OEjYEw=", "privacy_permission": true, "primary": true, "privacy_owner": false, "privacy_mode": false, "firmware_updated": false, "firmwareVersion": "170520240100_1.22", "firmwareAvailable": "latest-production-test"}, "dndlock": [], "allotment": {"_id": "66ffc9a443146d36c187e030", "lock_type": "Keyless", "lock_id": "65f81e5eba4c466f13501b93", "alloted_by": "657bee62ef1b994aea3f8043", "manager_type": "person", "manager_id": "657bee62ef1b994aea3f8043", "property_id": "65950533d51e12540a653056", "status": 1, "created_at": "2024-10-04T10:55:32.537Z", "__v": 0}, "maplockproperty": {"_id": "66ffc9a443146d36c187e02d", "property_id": "65950533d51e12540a653056", "lock_id": "65f81e5eba4c466f13501b93", "floor_number": "5", "appartment_number": "65", "room_number": "65", "created_at": "2024-10-04T10:55:32.533Z", "__v": 0}, "property_id_object": "65950533d51e12540a653056", "property_details": {"_id": "65950533d51e12540a653056", "latitude": "25.259302041998286", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.41351517848576", "emirate": "Dubai", "area": "7C57+P79 - Tunis St - opposite to Etisalat Academy - Muhaisnah - Muhaisanah 2 - Dubai - United Arab Emirates", "building_name": "Etisalat academy", "total_floors": 3, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-01-03T06:56:51.244Z", "__v": 0, "name": "Etisalat academy", "floor_number": "5", "floor": "5", "map_id": "66ffc9a443146d36c187e02d", "id": "65950533d51e12540a653056", "appartment_number": "65", "room_number": "65"}, "assigned_user": [], "assignment": {"time_ranges": [{"_id": "63d8c179d032f69aee77dc87", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "forever", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [1, 1, 1, 1, 1, 1, 1], "created_at": "2023-01-31T07:21:29.435Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc89", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.439Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8b", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.442Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8d", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.445Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8f", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.449Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc91", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.456Z", "__v": 0}], "assignment_data": {"time_profile_id": {"_id": "63d8c179d032f69aee77dc85", "iseo_id": 88, "created_by": "0", "status": true, "name": "forever_routine", "doors_linked": [], "created_at": "2023-01-31T07:21:29.430Z", "__v": 0}, "_id": "66ffc9a543146d36c187e038", "status": 1, "lock_id": "65f81e5eba4c466f13501b93", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "assigned_at": "2024-10-04T10:55:33.280Z"}}, "checkins": [], "totalCheckins": 0, "owner_id": "657bee62ef1b994aea3f8043", "companyCheckin": false, "privacy": false, "privacy_changed": false}, {"_id": "67237ee847642ec95519f75d", "status": 1, "lock_id": "66753f4be6c840be2bb7a706", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "time_profile_id": "63d8c179d032f69aee77dc85", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "company_id": "", "booking_number": "", "passcode": "", "passcodeId": "", "checkin": false, "assigned_at": "2024-10-31T12:58:16.435Z", "__v": 0, "lock_id_string": "66753f4be6c840be2bb7a706", "valid_from_date": "2021-01-01T20:55:26.453Z", "valid_to_date": "2121-01-01T20:55:26.453Z", "unit_id": "65f2f2048028212c5afb0d55", "lock": {"_id": "66753f4be6c840be2bb7a706", "alloted": true, "status": 1, "name": "LINKEY ", "image": "", "unique_key": "BM1002_dee04f", "access_key": "IBomeLwTYLxSmnaNhxbLBlA7BEwnsC+JYQyRPUjv6t/QA7Cc9/zfGnFCj6I2oZcvcLV3uGsFQA9GQdAkKN34l8AKwVDFUVm6xK/sJVRwPyQBUsdVxsTIgdzB5AuZKStyLa3sUyUR96bR8VBSVWAYS8h3ZvyJobXuR0miAhoqaR++tmj4ZzNBxoIrmPqPFkpyxHEf8biyX4IEno5c8dN5mbCtWuQ9ktODn1xOGBpHOB5olI3rLfK+7ifKjRUYkz+ouRABtADfLa3N2s/V2sTz6S8WAQxyoP1pkC2PB0MKXGvgtMGLUvklscrRlyKRizOpIfYHNVjxcsR705C9Ju4jb6oiQFcpO5gE9+I63SiPcevcy3Qr5d2FsSWPn0kxhY/Y0/Mt9YZedwtIgKChMkJQloDeAvl/oEJxbXZZb1vctOntVUY0qwizE9AXd6nllrAZYGmzM308AUMXxw+KHMCDg9GGd09+5d7hjHF00kU6Vrp85IKUltZWIIXIQ4JPYyhk/5IYojBsnqEKSQHdgjgmdDDdXoDTyFHTyzQuQbhvYuCzMEM+9R9ruUvnGAPXTb3fLCwjJVJ/D9c7OF3+eKVo6kIBbLgK6JlprSwFVZwtS80EDcEamq79KQqIJ3LKveWWSs6jpzxnZsqezsosXc/gp6dWPFGQ9XQn+ZkSi7I8uEGc8Cy3I40Nd2Pxi5KHRzOPonAyykAatMORi72FXIOcNHDYty6VgeBZ6T8ACnbq9Wrx1Eil4UDv46+ogBZoHEeAgwT3eFRSO492WuiIKMGr4YiBp4+hAQb2K6U92nePmUWsI8Q7YGvmjSQcrREUVL9Yzw7dafoJx0CmhNCZdfmMO+4vq0WvttOIDub/vYxL3ntY+oZNDQ+/qXenURESInay62KTP+2VWID8FgOHH+VXdEFt6ZL7smkqwxi21+Ab2cUwexTpaQ+4ec5cy/lxqp9D/7qcnRWPTpjYOo7mSL3X5ZOMkgP3NE21KKSILekD1aftxRcY1GpOEP4nf3hh8R5GjuSfW4osEDm5Xfjp3PHxpMKej2E3NW8gVSgZgnf8BLWnFhHcPhT47TMSUzOUXhL3mjQekxuSalEYZuYvhmppOfHS6e8Aaj+ZuI7UGRk95tomyn22x+3PQSpjvv/nbTdnYBjP0viPzPoAII64KAshj+qjYcxp/daIAHeV8ewXVomqC2lApAQ21gdxCyzAHFrYQIqzAfg5wTSJF0jDrQq1352PEdo2Bg0YA/yVpBoiRoVaPGejvxEthXqZEEnAWEhBOYxCQvoyZFDgeDh6hSA/8cwSYnnlm2bqqk7tkFwk+5/i/fQCN6TltSHF9tsWphn7g624E1oLDXQdZksZhmJ2XaYpbJrCsQD7tWyPShJwpv/fiKyJyzcE3CZwJjpFQYxvFZhhSrWW9oW4Jc+Nn2erOygmvw4IlOOyXsAhbgDSRhy85zW3wJJ9/LZvrYEaDtso1QP8oDhj5JzkdPEcjlriE15VUW78FyzbU2euTXdr+P7N727OTAo2bui6I60GGEUOA+6WQg09Ic9ixJa6+9kzrKUCKhFkNSqdi8jFzlMH5DWminDgdiNMVHjGGSPHbVyuXDLIZNgWsewYLkCeJDzLXHu0Jq1dwJmC82PtxTLdxExQwJ1Tox7ePUEJgpQ2YGwWT2vOE430zKyO45yk5SWUkzmfp2u5KWE/2AC+FMHfX6LBRemqQXJPgTPXCZSQvcuiRiroIt6Ou2kWWeHP2+KOD02/8wP73s3rt+Wq1ECWQI05z0EfDEmU7EKqgUNoYDsOzSFBLloRXBpKbfET/2wksCAI/c3DUnXDkaYIchCcGIe3VJCKy0JhZ+fCJSobyoqQbMjW33qjf42mo/5xcfd1EBZoKpUrEl+olGH5RzKaFKo4ydATSCY9rPVS7b4owyQ7FRgj3OD1GvhiWtPj/s3ttWjbOzjfNXbv+AHElNNtYVe40A+g415mWCio078Gb+jvLO/pgdcfvdQYOB0T6FSn2WilfiMUlT4BzPwiCJ8vue6YTFq/1DeZha+rJChlmmE9wdknPAf8qzCHOp+VKaYr2lOJr4KYiF3Rspy0O6PDLUaQAiaKpxzOt4twmtDv1+pLo1IGU6VtSGZ9iXuK2DNWCdaicypngiVXl0PQOI85UlQA8wE4+fCNndWrJ40EA9kiJ8T30p0UHjsoQxJTkBjbMINGCXYmgG+Ef5EOFaCWkcjcPyNrDsEnrCtznFwDRCHsyxpORkSHy5ICOSBEkwtxLk7x4VAofJWQDKZFm9K4CQn/EKcgUIv+CZco05StA/frPuFcfNcTcFwJl9YqeKNUyu64Ex5QqzZusDsNg6z5awA5LYBwChWA7/mjzg7hswbJiVvUBDo8cADkWFVv4KCYkkUmRS3rCoWOsGd19RQ7Vdp7yfH5No9Uu3GFi69f0L//lids9+T3FW2JKALRIikqMksU43MisD4A/4kXR+7S3m3YyB1srhtowuxh2H/MkOZeD2PQrHirIY/iEiZmN1y/gHkpm+TDqBtW0+1lu0jjGITHx35P4N4=", "provider": "<PERSON><PERSON>", "lock_uid": "", "battery_level": 0, "inventory_status": 4, "internal_id": "KL-728534", "size": "35*35", "colour": "black", "order_id": "", "icon_id": "6397163b4925f8fe1073d366", "messer_token": "", "messer_enddate": null, "iseo_id": "", "installationId": "", "installationStatus": 0, "installationComment": "", "orderAssignType": 0, "extendedWarranty": 0, "warrantyStartDate": null, "listing_id": "", "firmwareId": "", "ojiLockId": "2", "linkoLockId": "", "mac": "C7:C7:7E:4F:E0:DE", "oji_battery_level": 21, "installationCompletedBy": "657bee62ef1b994aea3f8041", "createdAt": "2024-06-21T08:52:27.881Z", "__v": 0, "activation_date": "2024-10-31T12:58:16.437Z", "tags": {"SUPPORTS_PASSCODE": "Supports passcode1"}, "keyId": "2", "icon": [{"_id": "6397163b4925f8fe1073d366", "name": "Cabin", "icon": "icons/Cabin.png", "type": "lock", "createdAt": "2022-12-12T11:53:31.191Z", "__v": 0}], "property_id": "65950533d51e12540a653056", "encrypted_key": "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", "privacy_permission": true, "primary": true, "privacy_owner": false, "privacy_mode": false, "firmware_updated": false, "firmwareVersion": "", "firmwareAvailable": "latest-production-test"}, "dndlock": [], "allotment": {"_id": "67237ee647642ec95519f750", "lock_type": "<PERSON><PERSON>", "lock_id": "66753f4be6c840be2bb7a706", "alloted_by": "657bee62ef1b994aea3f8043", "manager_type": "person", "manager_id": "657bee62ef1b994aea3f8043", "property_id": "65950533d51e12540a653056", "status": 1, "created_at": "2024-10-31T12:58:14.208Z", "__v": 0}, "maplockproperty": {"_id": "67237ee647642ec95519f74d", "property_id": "65950533d51e12540a653056", "lock_id": "66753f4be6c840be2bb7a706", "floor_number": "242", "appartment_number": "65", "room_number": "65", "created_at": "2024-10-31T12:58:14.204Z", "__v": 0}, "property_id_object": "65950533d51e12540a653056", "property_details": {"_id": "65950533d51e12540a653056", "latitude": "25.259302041998286", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.41351517848576", "emirate": "Dubai", "area": "7C57+P79 - Tunis St - opposite to Etisalat Academy - Muhaisnah - Muhaisanah 2 - Dubai - United Arab Emirates", "building_name": "Etisalat academy", "total_floors": 3, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-01-03T06:56:51.244Z", "__v": 0, "name": "Etisalat academy", "floor_number": "242", "floor": "242", "map_id": "67237ee647642ec95519f74d", "id": "65950533d51e12540a653056", "appartment_number": "65", "room_number": "65"}, "assigned_user": [], "assignment": {"time_ranges": [{"_id": "63d8c179d032f69aee77dc87", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "forever", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [1, 1, 1, 1, 1, 1, 1], "created_at": "2023-01-31T07:21:29.435Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc89", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.439Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8b", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.442Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8d", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.445Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8f", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.449Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc91", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.456Z", "__v": 0}], "assignment_data": {"time_profile_id": {"_id": "63d8c179d032f69aee77dc85", "iseo_id": 88, "created_by": "0", "status": true, "name": "forever_routine", "doors_linked": [], "created_at": "2023-01-31T07:21:29.430Z", "__v": 0}, "_id": "67237ee847642ec95519f75d", "status": 1, "lock_id": "66753f4be6c840be2bb7a706", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "assigned_at": "2024-10-31T12:58:16.435Z"}}, "checkins": [], "totalCheckins": 0, "owner_id": "657bee62ef1b994aea3f8043", "companyCheckin": false, "privacy": false, "privacy_changed": false}, {"_id": "675fe46ffdf38410dc318986", "status": 1, "lock_id": "64461e5d6f20b4e440ce484f", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "time_profile_id": "63d8c179d032f69aee77dc85", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "company_id": "", "booking_number": "", "passcode": "", "passcodeId": "", "checkin": false, "assigned_at": "2024-12-16T08:27:27.894Z", "__v": 0, "lock_id_string": "64461e5d6f20b4e440ce484f", "valid_from_date": "2021-01-01T20:55:26.453Z", "valid_to_date": "2121-01-01T20:55:26.453Z", "unit_id": "65f2f2048028212c5afb0d55", "lock": {"_id": "64461e5d6f20b4e440ce484f", "alloted": true, "status": 1, "name": "<PERSON><PERSON>", "image": "", "unique_key": "Bg6ORL6c9AU=", "access_key": "", "provider": "ISEO", "lock_uid": "Bg6ORL6c9AU=", "battery_level": 3, "inventory_status": 4, "internal_id": "KL-683545", "size": "35*35", "colour": "black", "order_id": "Manual - Test assignment", "icon_id": "6397163b4925f8fe1073d366", "messer_token": "", "messer_enddate": null, "iseo_id": "2", "createdAt": "2023-04-24T06:14:53.279Z", "__v": 0, "activation_date": "2024-12-16T08:27:27.904Z", "extendedWarranty": 0, "warrantyStartDate": "2023-12-15T06:15:33.490Z", "orderAssignType": 1, "installationComment": "Installation Done", "installationId": "66ffb014a8b9bdf359592b23", "installationStatus": 1, "installedDate": "2024-10-09T12:59:12.620Z", "installationCompletedBy": "657bee62ef1b994aea3f8041", "tags": {}, "icon": [{"_id": "6397163b4925f8fe1073d366", "name": "Cabin", "icon": "icons/Cabin.png", "type": "lock", "createdAt": "2022-12-12T11:53:31.191Z", "__v": 0}], "property_id": "662618d7f3934418e0062f6f", "encrypted_key": "", "privacy_permission": true, "primary": true, "privacy_owner": false, "privacy_mode": false, "firmware_updated": false, "firmwareVersion": "", "firmwareAvailable": "latest-production-test"}, "dndlock": [], "allotment": {"_id": "675fe46ffdf38410dc31897e", "lock_type": "ISEO", "lock_id": "64461e5d6f20b4e440ce484f", "alloted_by": "657bee62ef1b994aea3f8043", "manager_type": "person", "manager_id": "657bee62ef1b994aea3f8043", "property_id": "662618d7f3934418e0062f6f", "status": 1, "created_at": "2024-12-16T08:27:27.578Z", "__v": 0}, "maplockproperty": {"_id": "675fe46ffdf38410dc31897b", "property_id": "662618d7f3934418e0062f6f", "lock_id": "64461e5d6f20b4e440ce484f", "floor_number": "2", "appartment_number": "65", "room_number": "65", "created_at": "2024-12-16T08:27:27.574Z", "__v": 0}, "property_id_object": "662618d7f3934418e0062f6f", "property_details": {"_id": "662618d7f3934418e0062f6f", "latitude": "25.204849", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.270782", "emirate": "Dubai", "area": "Dubai", "building_name": "wqe", "total_floors": 4, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-04-22T07:59:19.878Z", "__v": 0, "name": "wqe", "floor_number": "2", "floor": "2", "map_id": "675fe46ffdf38410dc31897b", "id": "662618d7f3934418e0062f6f", "appartment_number": "65", "room_number": "65"}, "assigned_user": [], "assignment": {"time_ranges": [{"_id": "63d8c179d032f69aee77dc87", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "forever", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [1, 1, 1, 1, 1, 1, 1], "created_at": "2023-01-31T07:21:29.435Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc89", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.439Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8b", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.442Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8d", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.445Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8f", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.449Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc91", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.456Z", "__v": 0}], "assignment_data": {"time_profile_id": {"_id": "63d8c179d032f69aee77dc85", "iseo_id": 88, "created_by": "0", "status": true, "name": "forever_routine", "doors_linked": [], "created_at": "2023-01-31T07:21:29.430Z", "__v": 0}, "_id": "675fe46ffdf38410dc318986", "status": 1, "lock_id": "64461e5d6f20b4e440ce484f", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "assigned_at": "2024-12-16T08:27:27.894Z"}}, "checkins": [], "totalCheckins": 0, "owner_id": "657bee62ef1b994aea3f8043", "companyCheckin": false, "privacy": false, "privacy_changed": false}, {"_id": "67921454f798e9b6336b781e", "status": 1, "lock_id": "677e1623818cd1bece57d339", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "time_profile_id": "63d8c179d032f69aee77dc85", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "company_id": "", "booking_number": "", "passcode": "", "passcodeId": "", "checkin": false, "assigned_at": "2025-01-23T10:05:08.214Z", "__v": 0, "lock_id_string": "677e1623818cd1bece57d339", "valid_from_date": "2021-01-01T20:55:26.453Z", "valid_to_date": "2121-01-01T20:55:26.453Z", "unit_id": "65f2f2048028212c5afb0d55", "lock": {"_id": "677e1623818cd1bece57d339", "alloted": true, "status": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "image": "", "unique_key": "23190208-030089", "access_key": "", "provider": "<PERSON><PERSON>", "lock_uid": "", "battery_level": 0, "inventory_status": 4, "internal_id": "KL-109910", "size": "35*35", "colour": "black", "order_id": "", "icon_id": "6397163b4925f8fe1073d365", "messer_token": "", "messer_enddate": null, "iseo_id": "", "installationId": "", "installationStatus": 0, "installationComment": "", "orderAssignType": 0, "extendedWarranty": 0, "warrantyStartDate": null, "listing_id": "", "firmwareId": "", "ojiLockId": "", "tedeeLockId": "170972", "mac": "00:00:00:00:00:00", "oji_battery_level": 0, "installationCompletedBy": "657bee62ef1b994aea3f8041", "createdAt": "2025-01-08T06:07:31.240Z", "__v": 0, "activation_date": "2025-01-23T10:05:08.218Z", "icon": [{"_id": "6397163b4925f8fe1073d365", "name": "Bedroom", "icon": "icons/Bedroom.png", "type": "lock", "createdAt": "2022-12-12T11:53:31.191Z", "__v": 0}], "property_id": "670df9f82a856bd318d62abb", "encrypted_key": "U2FsdGVkX1+cl58D6MiS1kmN+dGckTOLLZuPwmKqkDw=", "privacy_permission": true, "primary": true, "privacy_owner": false, "privacy_mode": false, "firmware_updated": false, "firmwareVersion": "", "firmwareAvailable": "latest-production-test"}, "dndlock": [], "allotment": {"_id": "67921453f798e9b6336b7816", "lock_type": "<PERSON><PERSON>", "lock_id": "677e1623818cd1bece57d339", "alloted_by": "657bee62ef1b994aea3f8043", "manager_type": "person", "manager_id": "657bee62ef1b994aea3f8043", "property_id": "670df9f82a856bd318d62abb", "status": 1, "created_at": "2025-01-23T10:05:07.515Z", "__v": 0}, "maplockproperty": {"_id": "67c83054fcd9da534d81ff65", "property_id": "670df9f82a856bd318d62abb", "lock_id": "677e1623818cd1bece57d339", "floor_number": "3", "appartment_number": "65", "room_number": "65", "created_at": "2025-03-05T11:07:00.258Z", "__v": 0}, "property_id_object": "670df9f82a856bd318d62abb", "property_details": {"_id": "670df9f82a856bd318d62abb", "latitude": "25.2547718", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.4009162", "emirate": "Dubai", "area": "27 28th St - Al QusaisAl Qusais 3 - اﻟﻘﺼﻴﺺ - دبي - United Arab Emirates", "building_name": "EM", "total_floors": 3, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-10-15T05:13:28.040Z", "__v": 0, "name": "EM", "floor_number": "3", "floor": "3", "map_id": "67c83054fcd9da534d81ff65", "id": "670df9f82a856bd318d62abb", "appartment_number": "65", "room_number": "65"}, "assigned_user": [], "assignment": {"time_ranges": [{"_id": "63d8c179d032f69aee77dc87", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "forever", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [1, 1, 1, 1, 1, 1, 1], "created_at": "2023-01-31T07:21:29.435Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc89", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.439Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8b", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.442Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8d", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.445Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc8f", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.449Z", "__v": 0}, {"_id": "63d8c179d032f69aee77dc91", "status": true, "routine_id": "63d8c179d032f69aee77dc85", "name": "never", "time_slot": {"start_hour": 0, "start_min": 0, "end_hour": 23, "end_min": 59}, "always_open": false, "holidays": false, "allowed_days": [0, 0, 0, 0, 0, 0, 0], "created_at": "2023-01-31T07:21:29.456Z", "__v": 0}], "assignment_data": {"time_profile_id": {"_id": "63d8c179d032f69aee77dc85", "iseo_id": 88, "created_by": "0", "status": true, "name": "forever_routine", "doors_linked": [], "created_at": "2023-01-31T07:21:29.430Z", "__v": 0}, "_id": "67921454f798e9b6336b781e", "status": 1, "lock_id": "677e1623818cd1bece57d339", "assigned_by": "657bee62ef1b994aea3f8043", "assigned_to": "657bee62ef1b994aea3f8041", "valid_from": "2021-01-01T21:55:26.4536687+01:00", "valid_to": "2121-01-01T21:55:26.4536687+01:00", "assigned_at": "2025-01-23T10:05:08.214Z"}}, "checkins": [], "totalCheckins": 0, "owner_id": "657bee62ef1b994aea3f8043", "companyCheckin": false, "privacy": false, "privacy_changed": false}], "properties": [{"_id": "679cdb333cd609d645f695a3", "latitude": "26.44626557769758", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "31.67150996625423", "emirate": "Abu Dhabi", "area": "CMWC+CP9, الظه<PERSON><PERSON> الصحراوى, Al Monshaah, Sohag Governorate 1646096, Egypt", "building_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "total_floors": 88, "laundary_number": "9718888888888", "grocery_number": "9718888888888", "support_call_number": "9718888888888", "support_whatsapp_number": "9718888888888", "icon_id": "6397163b4925f8fe1073d361", "created_at": "2025-01-31T14:16:19.201Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d361", "name": "Hotel", "icon": "icons/Hotel.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 0}, {"_id": "6780e333a1da832e71fbe900", "latitude": "26.444872986499682", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "31.671680286526676", "emirate": "<PERSON><PERSON><PERSON><PERSON>", "area": "CMVC+G7Q, Sohag Al Gadida City, Sohag Governorate 1646092, Egypt", "building_name": "<PERSON><PERSON><PERSON><PERSON>", "total_floors": 4, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d363", "created_at": "2025-01-10T09:06:59.071Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d363", "name": "Villa", "icon": "icons/Villa.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 1}, {"_id": "670df9f82a856bd318d62abb", "latitude": "25.2547718", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.4009162", "emirate": "Dubai", "area": "27 28th St - Al QusaisAl Qusais 3 - اﻟﻘﺼﻴﺺ - دبي - United Arab Emirates", "building_name": "EM", "total_floors": 3, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-10-15T05:13:28.040Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d362", "name": "Office", "icon": "icons/Office.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 2}, {"_id": "662618d7f3934418e0062f6f", "latitude": "25.204849", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.270782", "emirate": "Dubai", "area": "Dubai", "building_name": "wqe", "total_floors": 4, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-04-22T07:59:19.878Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d362", "name": "Office", "icon": "icons/Office.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 1}, {"_id": "65950533d51e12540a653056", "latitude": "25.259302041998286", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.41351517848576", "emirate": "Dubai", "area": "7C57+P79 - Tunis St - opposite to Etisalat Academy - Muhaisnah - Muhaisanah 2 - Dubai - United Arab Emirates", "building_name": "Etisalat academy", "total_floors": 3, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2024-01-03T06:56:51.244Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d362", "name": "Office", "icon": "icons/Office.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 2}, {"_id": "657beed7ef1b994aea3f8092", "latitude": "25.25932689371647", "manager_id": "657bee62ef1b994aea3f8043", "installer_id": "", "manager_type": "company", "longitude": "55.41330356429146", "emirate": "Dubai", "area": "7C57+P79 - Tunis St - opposite to Etisalat Academy - Muhaisnah - Muhaisanah 2 - Dubai - United Arab Emirates", "building_name": "QA Test Office", "total_floors": 12, "laundary_number": "", "grocery_number": "", "support_call_number": "", "support_whatsapp_number": "", "icon_id": "6397163b4925f8fe1073d362", "created_at": "2023-12-15T06:14:47.896Z", "__v": 0, "icon": [{"_id": "6397163b4925f8fe1073d362", "name": "Office", "icon": "icons/Office.png", "type": "property", "createdAt": "2022-12-12T11:53:31.190Z", "__v": 0}], "total_locks": 1}], "is_paid": true, "totalUnreadNotification": 0, "dateTime": "2025-06-12T06:35:47.7370000"}