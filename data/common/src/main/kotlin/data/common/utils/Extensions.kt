package data.common.utils

/* ###################################
######################################
############# String #################
######################################
######################################
 */
fun String.phoneNumberWithPlus() = if (startsWith("+")) this else "+$this"

fun String.isValidMobile(): Boolean = this.length in 7..15

fun String.ensureLength(length: Int, char: Char) = if (this.length < length) this.padEnd(length, char) else this

fun String.replaceOrAdd(char: Char, index: Int) = if (this.length > index) {
    replaceRange(index, index + 1, char.toString())
} else {
    this + char
}

fun String.replaceOrAdd(str: String, index: Int) = if (this.length > index) {
    replaceRange(index, index + 1, str)
} else {
    this + str
}

fun String.ensureStartsWith(other: String) = if (this.startsWith(other)) this else other + this

fun String.ensureEndsWith(other: String) = if (this.endsWith(other)) this else this + other

/* ###################################
######################################
############# Int ####################
######################################
######################################
 */
fun Int.toOnlySecondsTimerDisplay(): String {
    val mod = this % 60
    return if (mod < 10) "0$mod" else mod.toString()
}

fun Int.toOnlyMinutesTimerDisplay(): String {
    val div = this / 60
    return if (div < 10) "0$div" else div.toString()
}

fun Int.toTimerDisplay(): String {
    return "${toOnlyMinutesTimerDisplay()}:${toOnlySecondsTimerDisplay()}"
}