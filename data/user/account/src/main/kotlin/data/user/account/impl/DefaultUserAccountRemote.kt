package data.user.account.impl

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpRequestForm
import core.http.client.HttpRequestFormFileItem
import core.http.client.HttpRequestFormTextItem
import core.http.client.HttpResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.user.account.repositories.UserAccountRemote
import kotlinx.datetime.Clock
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class DefaultUserAccountRemote(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) : UserAccountRemote {

    override suspend fun sendEmailOtp(email: String, authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/user/send-otp-email"
        val body = buildJsonObject {
            put("email", email)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            body = body,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            )
        )

        client.request(request)
    }

    override suspend fun verifyEmail(
        email: String,
        otp: String,
        method: String,
        deviceType: String,
        userId: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/user/update-email"
        val body = buildJsonObject {
            put("email", email)
            put("otp", otp)
            put("method", method)
            put("device_type", deviceType)
            put("userID", userId)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            body = body,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            )
        )

        return@async client.request(request)
    }

    override suspend fun changePassword(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userId: String,
        isAdmin: Boolean,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/user/change_password"
        val body = buildJsonObject {
            put("old_password", oldPassword)
            put("password", newPassword)
            put("confirm_password", confirmPassword)
            put("user_id", userId)
            put("is_admin", isAdmin)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            body = body,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            )
        )

        return@async client.request(request)
    }

    override suspend fun updateProfile(
        firstName: String,
        lastName: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/user/profile"
        val body = HttpRequestForm(
            binaryData = true,
            listOf(
                HttpRequestFormTextItem("first_name", firstName),
                HttpRequestFormTextItem("last_name", lastName)
            )
        )
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            form = body,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        return@async client.request(request)
    }

    override suspend fun getUserProfile(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/user/profile"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        return@async client.request(request)
    }

    override suspend fun deleteAccount(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/user/delete"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        return@async client.request(request)
    }

    override suspend fun checkIn(
        name: String,
        bookingNumber: String,
        documentNumber: String,
        status: String,
        documentType: String,
        similarity: String,
        expiryDate: String,
        identity1: ByteArray,
        identity2: ByteArray?,
        documentImage: ByteArray,
        capturedImage: ByteArray,
        extra: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/user/checkin-data"
        val body = HttpRequestForm(
            binaryData = true,
            listOf(
                HttpRequestFormTextItem("name", name),
                HttpRequestFormTextItem("booking_number", bookingNumber),
                HttpRequestFormTextItem("document_number", documentNumber),
                HttpRequestFormTextItem("status", status),
                HttpRequestFormTextItem("document_type", documentType),
                HttpRequestFormTextItem("similarity", similarity),
                HttpRequestFormTextItem("expiry_date", expiryDate),
                HttpRequestFormFileItem("identity1", Clock.System.now().toString(), "image/png", identity1),
                HttpRequestFormFileItem("document_image", Clock.System.now().toString(), "image/png", documentImage),
                HttpRequestFormFileItem("captured_image", Clock.System.now().toString(), "image/png", capturedImage),
                HttpRequestFormTextItem("extra", extra)
            ) + if (identity2 != null) {
                listOf(HttpRequestFormFileItem("identity2", Clock.System.now().toString(), "image/png", identity2))
            } else {
                emptyList()
            }
        )
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            form = body,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        return@async client.request(request)
    }
}