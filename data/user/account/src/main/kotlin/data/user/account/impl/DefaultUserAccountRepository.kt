package data.user.account.impl

import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.ServerResponse
import data.common.http.unwrap
import data.common.preferences.Preferences
import data.user.account.models.SendOtpEmailResponse
import data.user.account.models.UserProfileResponse
import data.user.account.models.VerifyEmailResponse
import data.user.account.models.dtos.SendOtpEmailResponseDto
import data.user.account.models.dtos.UserProfileResponseDto
import data.user.account.models.dtos.VerifyEmailResponseDto
import data.user.account.repositories.UserAccountRemote
import data.user.account.repositories.UserAccountRepository

class DefaultUserAccountRepository(
    client: HttpClient,
    baseUrl: String,
    private val logger: Logger
) : UserAccountRepository {

    private val remote: UserAccountRemote = DefaultUserAccountRemote(
        client = client,
        baseUrl = baseUrl,
        logger = logger
    )

    override suspend fun sendOtpMail(email: String): SendOtpEmailResponse = logger.async() {
        return@async remote
            .sendEmailOtp(
                email = email,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<SendOtpEmailResponseDto>()
            .toModel()
    }

    override suspend fun verifyEmail(
        email: String,
        otp: String,
        method: String,
        deviceType: String,
        userId: String
    ): VerifyEmailResponse = logger.async() {
        return@async remote
            .verifyEmail(
                email = email,
                otp = otp,
                method = method,
                deviceType = deviceType,
                userId = userId,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<VerifyEmailResponseDto>()
            .toModel()
    }

    override suspend fun changePassword(
        oldPassword: String,
        newPassword: String,
        confirmPassword: String,
        userId: String,
        isAdmin: Boolean
    ): String = logger.async() {
        return@async remote
            .changePassword(
                oldPassword = oldPassword,
                newPassword = newPassword,
                confirmPassword = confirmPassword,
                userId = userId,
                isAdmin = isAdmin,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<ServerResponse>()
            .message
    }

    override suspend fun updateProfile(
        firstName: String,
        lastName: String
    ): String = logger.async() {
        return@async remote
            .updateProfile(
                firstName = firstName,
                lastName = lastName,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<ServerResponse>()
            .message
    }

    override suspend fun getUserProfile(): UserProfileResponse = logger.async() {
        return@async remote
            .getUserProfile(
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<UserProfileResponseDto>()
            .toModel()
    }

    override suspend fun checkIn(
        name: String,
        bookingNumber: String,
        documentNumber: String,
        status: String,
        documentType: String,
        similarity: String,
        expiryDate: String,
        identity1: ByteArray,
        identity2: ByteArray?,
        documentImage: ByteArray,
        capturedImage: ByteArray,
        extra: String
    ): String = logger.async() {
        return@async remote
            .checkIn(
                name = name,
                bookingNumber = bookingNumber,
                documentNumber = documentNumber,
                status = status,
                documentType = documentType,
                similarity = similarity,
                expiryDate = expiryDate,
                identity1 = identity1,
                identity2 = identity2,
                documentImage = documentImage,
                capturedImage = capturedImage,
                extra = extra,
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<ServerResponse>()
            .message
    }

    override suspend fun deleteAccount(): Unit = logger.async() {
        remote
            .deleteAccount(
                authentication = Preferences.authenticationToken.get()
            )
            .unwrap<ServerResponse>()
            .message
    }
}