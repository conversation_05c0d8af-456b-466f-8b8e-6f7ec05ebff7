package data.user.home.remote

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class UserHomeRemoteImpl(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) : UserHomeRemote {

    override suspend fun getLocksList(uid: String, isAdmin: <PERSON>olean, authToken: String) = logger.async() {
        val url = "$hostname/user/home"
        val body = buildJsonObject {
            put("uid", uid)
            put("is_admin", isAdmin)
        }
        val headers = listOf(
            HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authToken),
            HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
        )
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = headers,
            body = body
        )

        return@async client.request(request)
    }
}