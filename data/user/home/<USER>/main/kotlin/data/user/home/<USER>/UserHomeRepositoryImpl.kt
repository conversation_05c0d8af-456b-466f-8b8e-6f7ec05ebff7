package data.user.home.repositories

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.lock.common.lock.models.lock.Lock
import data.lock.common.lock.models.lock.UserHomeResponse
import data.user.home.local.UserHomeLocal
import data.user.home.models.dtos.UserHomeResponseDto
import data.user.home.remote.UserHomeRemote
import kotlinx.coroutines.Dispatchers

class UserHomeRepositoryImpl(
    private val remote: UserHomeRemote,
    private val local: UserHomeLocal,
    private val logger: Logger
) : UserHomeRepository {

    override suspend fun fetchUserHomeData(uid: String, isAdmin: Boolean, token: String) = logger.async(
        Dispatchers.IO
    ) {
        val response = remote.getLocksList(uid, isAdmin, token)
        val model = response.value(UserHomeResponseDto.serializer()).toModel()

        return@async insert(model, uid)!!
    }

    override suspend fun cachedLocks(uid: String): List<Lock> = logger.async(Dispatchers.IO) {
        val locks = local.get(userUid = uid)

        return@async locks?.locks ?: listOf()
    }

    private suspend fun insert(response: UserHomeResponse, uid: String) = logger.async(Dispatchers.IO) {
        local.insert(uid, response)
        local.get(uid)
    }
}