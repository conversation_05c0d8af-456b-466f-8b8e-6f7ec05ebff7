package data.user.home.local

import app.cash.sqldelight.ColumnAdapter
import app.cash.sqldelight.db.SqlDriver
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import data.database.queries.UserHomeResponseQueries
import data.database.schema.TimeRangeEntity
import data.lock.common.lock.models.lock.UserHomeResponse

class UserHomeLocalImpl(
    driver: SqlDriver,
    private val logger: Logger
) : UserHomeLocal {

    private val adapter = object : ColumnAdapter<List<Int>, String> {
        override fun decode(databaseValue: String): List<Int> {
            return if (databaseValue.isEmpty()) listOf() else databaseValue.split(",").map { it.toInt() }
        }

        override fun encode(value: List<Int>) = value.joinToString(separator = ",")
    }

    internal val queries = UserHomeResponseQueries(
        driver = driver,
        TimeRangeEntityAdapter = TimeRangeEntity.Adapter(adapter)
    )

    override fun get(userUid: String): UserHomeResponse? = logger.log {
        return@log getUserHomeResponse(userUid)
    }

    override suspend fun insert(userUid: String, response: UserHomeResponse) = logger.async() {
        response.insert(queries, userUid)
    }
}