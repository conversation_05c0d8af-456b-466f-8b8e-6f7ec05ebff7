package data.user.guest.repositories

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async

internal class GuestRemoteRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) {

    suspend fun get(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/user/guest-assignments"

        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            )
        )

        return@async client.request(request)
    }
}