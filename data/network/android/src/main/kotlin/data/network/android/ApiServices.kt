package data.network.android

import com.google.gson.JsonObject
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.SendOtpResponse
import data.keyless.authentication.models.SignupResponse
import data.network.android.models.ChangePassword
import data.network.android.models.CompanyProfileResponse
import data.network.android.models.CreateRoutineRequest
import data.network.android.models.ForgotPassword
import data.network.android.models.GetAdminUserModel
import data.network.android.models.GetAllIconsModel
import data.network.android.models.GetAssignedData
import data.network.android.models.GetPropertyResponse
import data.network.android.models.GetRoutineDeleteModel
import data.network.android.models.GetSingleResponse
import data.network.android.models.GetSizeColorModel
import data.network.android.models.GetSysCodeModel
import data.network.android.models.HistoryLockResponse
import data.network.android.models.LogsSendModel
import data.network.android.models.ManageStaffModel
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.ModelCheckInPm
import data.network.android.models.ModelConfigureCard
import data.network.android.models.ModelDfuBackendData
import data.network.android.models.ModelInstallerMaintenance
import data.network.android.models.ModelPlatformDetails
import data.network.android.models.ModelUnitsData
import data.network.android.models.NotificationModel
import data.network.android.models.PrivacyLockResponse
import data.network.android.models.PropertyAllResponse
import data.network.android.models.RevokeResponse
import data.network.android.models.SearchLocationResponse
import data.network.android.models.SeprateRoleApiModelMain
import data.network.android.models.UpdateCompanyProfileResponse
import data.network.android.models.UserProfileReponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import org.json.JSONArray
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface ApiServices {
    @POST(ApiConstant.ADD_USERNAME)
    suspend fun addUserApiRequest(
        @Body request: JsonObject
    ): SignupResponse

    @GET("autocomplete/json")
    suspend fun getLocation(
        @Query("key") key: String,
        @Query("input") input: String
    ): SearchLocationResponse?

    @GET("details/json")
    fun getPlaceDetails(
        @Query("key") key: String,
        @Query("placeid") placeid: String
    ): SearchLocationResponse?

    //
    @POST(ApiConstant.SEND_OTP_MOBILE)
    suspend fun sendOtpMobileApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    //
//
//    @POST(ApiConstant.VERIFY_MOBILE_OTP)
//    suspend fun verifyMobileOtpApiRequest(
//        @Body request: VerifyMobileOtpApiRequest
//    ): GetSendOtpMobileApiResponse
//
    @POST(ApiConstant.SEND_OTP_EMAIL)
    suspend fun sendOtpEmailApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    //
//
    @POST(ApiConstant.VERIFY_EMAIL_OTP)
    suspend fun verifyEmailOtpApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    //
//
    @POST(ApiConstant.RESEND_OTP)
    suspend fun resendOtpApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    @FormUrlEncoded
    @POST(ApiConstant.CREATE_PROPERTY)
    suspend fun propertyCreate(
        @Header("Authorization") token: String,
        @Field("latitude") latitude: String,
        @Field("longitude") longitude: String,
        @Field("emirate") emirate: String,
        @Field("building_name") building_name: String,
        @Field("total_floors") total_floors: String,
        @Field("area") area: String,
        @Field("laundary_number") laundary_number: String,
        @Field("grocery_number") grocery_number: String,
        @Field("icon_id") icon_id: String,
        @Field("support_call_number") support: String,
        @Field("support_whatsapp_number") whatsapp: String
    ): GetPropertyResponse

    @FormUrlEncoded
    @POST(ApiConstant.ADMIN_CREATE_PROPERTY)
    suspend fun hitCreateInstallerProperty(
        @Header("Authorization") token: String,
        @Field("latitude") latitude: String,
        @Field("longitude") longitude: String,
        @Field("emirate") emirate: String,
        @Field("building_name") building_name: String,
        @Field("total_floors") total_floors: Int,
        @Field("area") area: String,
        @Field("laundary_number") laundary_number: String,
        @Field("grocery_number") grocery_number: String,
        @Field("icon_id") icon_id: String,
        @Field("support_call_number") support: String,
        @Field("support_whatsapp_number") whatsapp: String,
        @Path("id") id: String
    ): GetPropertyResponse

    @FormUrlEncoded
    @POST(ApiConstant.STAFF)
    suspend fun addStaff(
        @Header("Authorization") token: String,
        @Field("country_code") country_code: String,
        @Field("mobile_number") mobile_number: String,
        @Field("email") email: String,
        @Field("username") username: String,
        @Field("first_name") first_name: String,
        @Field("last_name") last_name: String,
        @Field("role") role: String,
        @Field("passport_number") passport_number: String
    ): GetPropertyResponse

    @FormUrlEncoded
    @PATCH(ApiConstant.STAFF)
    suspend fun updateStaff(
        @Header("Authorization") token: String,
        @Field("country_code") country_code: String,
        @Field("mobile_number") mobile_number: String,
        @Field("email") email: String,
        @Field("username") username: String,
        @Field("first_name") first_name: String,
        @Field("last_name") last_name: String,
        @Field("role") role: String,
        @Field("passport_number") passport_number: String,
        @Field("staff_id") staff_id: String
    ): GetPropertyResponse

    @FormUrlEncoded
    @PATCH("company/property/update/{id}")
    suspend fun updateProperty(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Field("latitude") latitude: String,
        @Field("longitude") longitude: String,
        @Field("emirate") emirate: String,
        @Field("building_name") building_name: String,
        @Field("total_floors") total_floors: String,
        @Field("area") area: String,
        @Field("laundary_number") laundary_number: String,
        @Field("grocery_number") grocery_number: String,
        @Field("icon_id") icon_id: String,
        @Field("support_call_number") support: String,
        @Field("support_whatsapp_number") whatsapp: String
    ): GetPropertyResponse

    @POST(ApiConstant.LOGIN)
    suspend fun loginApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    //
    @POST(ApiConstant.LOGIN)
    suspend fun loginWithEmailApiRequest(
        @Body request: JsonObject
    ): LoginResponse

    //
//
    @POST(ApiConstant.VERIFY_LOGIN_OTP)
    suspend fun verifyLoginOtpApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    @POST(ApiConstant.FORGOT_PASSWORD)
    suspend fun forgotPassword(
        @Body req: JsonObject
    ): ForgotPassword.Res

    //
    @POST(ApiConstant.VERIFY_FORGEOT_OTP)
    suspend fun verifyForgotOtpApiRequest(
        @Body request: JsonObject
    ): SendOtpResponse

    @GET(ApiConstant.LOGOUT_ACCOUNT)
    suspend fun getLogoutAccount(
        @Header("Authorization") token: String
    ): JsonObject

    @GET(ApiConstant.GET_ALL_PROPERTY)
    suspend fun getAllProperty(
        @Header("Authorization") token: String
    ): GetPropertyResponse

    @GET(ApiConstant.COMPANY_PROFILE)
    suspend fun getCompanyProfile(@Header("Authorization") token: String): CompanyProfileResponse

    @DELETE(ApiConstant.DELETE_ACCOUNT)
    suspend fun deleteAccountApi(
        @Header("Authorization") token: String
//        @Path("id") id: String
    ): JsonObject

    @PATCH(ApiConstant.UPDATE_COMPANY_PROFILE)
    suspend fun updateCompanyProfile(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): UpdateCompanyProfileResponse

    @Multipart
    @PATCH(ApiConstant.USER_PROFILE)
    suspend fun editUserProfile(
        @Header("Authorization") token: String,
        @Part photo: MultipartBody.Part?,
        @Part("first_name") first_name: RequestBody,
        @Part("last_name") last_name: RequestBody
    ): UserProfileReponse

    @GET(ApiConstant.USER_PROFILE)
    suspend fun getUserProfile(
        @Header("Authorization") token: String
    ): UserProfileReponse

    @GET(ApiConstant.STAFF)
    suspend fun getStaff(
        @Header("Authorization") token: String
    ): ManageStaffModel

    @GET(ApiConstant.ROLES)
    suspend fun getRoleIDS(
        @Header("Authorization") token: String
    ): SeprateRoleApiModelMain

    @POST(ApiConstant.CHANGE_PASSWORD)
    suspend fun changePassword(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ChangePassword.Res

    @POST(ApiConstant.CHANGE_PASSWORD_FORGOT)
    suspend fun changePasswordForgot(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ChangePassword.Res

    @POST(ApiConstant.UPDATE_EMAIL)
    suspend fun hitEmailApiUpdate(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ChangePassword.Res

    @POST(ApiConstant.VERIFY_EMAIL)
    suspend fun verifyEmailOtp(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ChangePassword.Res

    @GET(ApiConstant.LOCK_DETAIL)
    suspend fun getKeyByInterId(
        @Header("Authorization") token: String,
        @Path("code") code: String
    ): GetAccessKeyModel

    @GET(ApiConstant.LOCK_ID)
    suspend fun getLockIdByScanKey(
        @Header("Authorization") token: String,
        @Path("code") code: String
    ): GetInternalLockId

    @GET(ApiConstant.ADMIN_LOCK_DETAIL)
    suspend fun getKeyByInterIdAdmin(
        @Header("Authorization") token: String,
        @Path("code") code: String,
        @Path("uid") uid: String
    ): GetAccessKeyModel

    @GET(ApiConstant.COMPANY_PROPERTY_ALL)
    suspend fun companyPropertyAll(
        @Header("Authorization") token: String
    ): PropertyAllResponse

    @GET(ApiConstant.COMMON_DETAILS)
    suspend fun getAllIcons(
        @Header("Authorization") token: String
    ): GetAllIconsModel

    @POST(ApiConstant.LOCKS_ASSIGN_LOCK)
    suspend fun hitAssignLockApi(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAssignedData

    @POST(ApiConstant.INSTALLATION_UPDATE_STATUS)
    suspend fun hitUpdateInstallationStatus(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAssignedData

    @POST(ApiConstant.ADMIN_LOCKS_ASSIGN_LOCK)
    suspend fun hitAssignLockApiInstaller(
        @Header("Authorization") token: String,
        @Body request: JsonObject,
        @Path("companyId") companyId: String
    ): GetAssignedData

    @POST(ApiConstant.Post_Lock_Shared_Log)
    suspend fun lockLogs(
        @Header("Authorization") token: String,
        @Body data: LogsSendModel
    ): HistoryLockResponse

    @POST(ApiConstant.Post_ACCESS_Shared_Log)
    suspend fun shareAccessLogs(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Path("lock_id") lock_id: String,
        @Path("status") status: String,
        @Path("device_model") device_model: String,
        @Path("mobile_id") mobile_id: String,
        @Path("valid_from") valid_from: String,
        @Path("valid_to") valid_to: String,
        @Path("assignment_id") assignment_id: String
    ): HistoryLockResponse

    @POST(ApiConstant.POST_BATTERY)
    suspend fun postBatteryPercentage(
        @Header("Authorization") token: String,
        @Path("count") count: Int,
        @Path("id") id: String
    ): HistoryLockResponse

    @POST(ApiConstant.Privacy_LockMode)
    suspend fun privacyLockMode(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Path("status") status: String
    ): PrivacyLockResponse

    @POST(ApiConstant.Get_Lock_Log)
    suspend fun getLockLog(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Body request: JsonObject
    ): HistoryLockResponse

    @POST(ApiConstant.Get_Lock_Shared_Log)
    suspend fun getLockSharedLog(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Body request: JsonObject
    ): HistoryLockResponse

    @FormUrlEncoded
    @PATCH(ApiConstant.USER_EDIT_LOCKS)
    suspend fun editLock(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Path("property_id") property_id: String,
        @Field("room_number") room_number: String,
        @Field("appartment_number") appartment_number: String,
        @Field("lock_name") lock_name: String,
        @Field("floor_number") floor_number: String,
        @Field("icon_id") icon_id: String,
        @Field("map_id") map_id: String,
        @Field("status") status: Int,
        @Field("unit_id") unit_id: String
    ): UserProfileResponse

    @GET(ApiConstant.USER_LIST)
    suspend fun getUserList(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): GetAssignLockUserListResponse

    @GET("locks/detail/{id}")
    suspend fun getLockInfoDetail(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): LocksListResponse.LocksModel

    @POST(ApiConstant.USER_REVOKE)
    suspend fun userRevoke(
        @Header("Authorization") token: String,
        @Body request: RevokeResponse
    ): GetAssignLockUserListResponse

    @GET(ApiConstant.USER_SEARCH_LIST)
    suspend fun getUserSearchList(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Path("searchText") searchText: String
    ): GetAssignLockUserListResponse

    @POST(ApiConstant.LOCKS_ASSIGN_TO_GUEST_NEW)
    suspend fun getLocksDetails(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): LocksListResponse

    @POST(ApiConstant.LOCKS_ASSIGN_INVITE)
    suspend fun getLocksInvite(
        @Header("Authorization") token: String,
        @Path("email") email: String,
        @Body request: JsonObject
    ): LocksListResponse

    @POST(ApiConstant.LOCKS_CANCEL_INVITE)
    suspend fun getCancelInvite(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): LocksListResponse

    @GET(ApiConstant.ADMIN_LOCKS_CHINESE)
    suspend fun adminLocksChinese(
        @Header("Authorization") token: String,
        @Query("page") page: Int,
        @Query("per_page") per_page: Int,
        @Query("keyword") keyword: String
    ): AdminLocksResponse

    @DELETE(ApiConstant.ADMIN_DELETE_LOCKS_CHINESE)
    suspend fun adminDeleteLocksChinese(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): SendOtpResponse

    @GET(ApiConstant.ADMIN_COMMON_DETAILS)
    suspend fun adminCommonDetails(
        @Header("Authorization") token: String
    ): GetSizeColorModel

    @POST(ApiConstant.ADMIN_CREATE_LOCKS_CHINESE)
    suspend fun adminCreateLocksChinese(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): SendOtpResponse

    @GET(ApiConstant.ROUTINE_MAIN)
    suspend fun getRoutine(
        @Header("Authorization") token: String,
        @Query("page") page: Int
    ): GetRoutineResponse

    @POST(ApiConstant.ROUTINE)
    suspend fun addRoutine(
        @Header("Authorization") token: String,
        @Body request: CreateRoutineRequest
    ): AddRoutineResponse

    @PATCH(ApiConstant.DELETE_ROUTINE)
    suspend fun editRoutine(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Body request: CreateRoutineRequest
    ): AddRoutineResponse

    @GET(ApiConstant.DELETE_ROUTINE)
    suspend fun getSingleRoutine(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): GetSingleResponse

    @DELETE(ApiConstant.TIME_RANGE)
    suspend fun deleteTimeRangeRoutine(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): GetRoutineDeleteModel

    @DELETE(ApiConstant.DELETE_ROUTINE)
    suspend fun deleteRoutine(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): GetRoutineResponse

    @POST(ApiConstant.SYSCODE_HISTORY_GET)
    suspend fun getSysCodeHistory(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetSysCodeModel

    @POST(ApiConstant.SYSCODE_HISTORY_SUBMIT)
    suspend fun setSysCodeHistorySubmit(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAssignedData

    @POST(ApiConstant.LOCKS_EDIT_ASSIGN_TO_GUEST_NEW)
    suspend fun hitEditAccess(
        @Header("Authorization") token: String,
        @Body request: JsonObject,
        @Path("id") id: String
    ): LocksListResponse

    @GET(ApiConstant.GET_CARD_SERIES)
    suspend fun getCardSeries(
        @Header("Authorization") token: String
    ): ModelCardRayonics

    @POST(ApiConstant.CREATE_CARD)
    suspend fun createCard(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelConfigureCard

    @FormUrlEncoded
    @POST(ApiConstant.CONFIGURED_CARD_LIST)
    suspend fun getConfiguredCardList(
        @Header("Authorization") token: String,
        @Field("page") page: Int,
        @Field("keyword") keyword: String
    ): ModelMangeCards

    @FormUrlEncoded
    @POST(ApiConstant.CARD_LIST)
    suspend fun getCardsList(
        @Header("Authorization") token: String,
        @Field("keyword") keyword: String,
        @Field("page") page: Int
    ): ModelCards

    @POST(ApiConstant.CONFIGURE_CARD)
    suspend fun configureCard(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @POST(ApiConstant.DELETE_CONFIGURE_CARD)
    suspend fun deleteManageCard(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @FormUrlEncoded
    @POST(ApiConstant.ADMIN_INSTALLER_HOME)
    suspend fun getInstallerList(
        @Header("Authorization") token: String,
        @Field("page") page: Int,
        @Field("search") search: String,
        @Field("limit") limit: Int,
        @Field("installation_id") installation_id: String
    ): ModelAdminInstaller

    @GET(ApiConstant.USER_PROPERTY)
    suspend fun getProperty(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): PropertyAllResponse

    @POST(ApiConstant.UPDATE_STATUS)
    suspend fun updateStatus(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @POST(ApiConstant.ADMIN_ASSIGN_LOCK_ISEO)
    suspend fun admin_assign_lock_iseo(
        @Header("Authorization") token: String,
        @Path("id") id: String,
        @Body request: JsonObject
    ): GetAssignedData

    @POST(ApiConstant.ASSIGN_LOCK_ISEO)
    suspend fun assign_lock_iseo(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAssignedData

    @GET(ApiConstant.FORCE_UPDATE)
    suspend fun forceUpdateApi(): ModelMessage

    @POST(ApiConstant.UPDATE_MAINTENANCE_STATUS)
    suspend fun updateMaintenanceStatus(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @POST(ApiConstant.UPDATE_MAINTENANCE_STATUS_USER)
    suspend fun updateMaintenanceStatusUser(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @FormUrlEncoded
    @POST(ApiConstant.INSTALLER_MAINTENANCE)
    suspend fun getMaintenanceList(
        @Header("Authorization") token: String,
        @Field("page") page: Int,
        @Field("search") search: String,
        @Field("limit") limit: Int,
        @Field("uid") uid: String
    ): ModelInstallerMaintenance

    @GET(ApiConstant.PLATFORM_DETAILS)
    suspend fun getPlatformDetails(
        @Header("Authorization") token: String
    ): ModelPlatformDetails

    @GET(ApiConstant.UPGRADE_MAINTENANCE)
    suspend fun upgradeMaintenance(
        @Header("Authorization") token: String,
        @Path("lockId") lockId: String
    ): ModelMessage

    @GET(ApiConstant.DOWNGRADE_MAINTENANCE)
    suspend fun downgradeMaintenance(
        @Header("Authorization") token: String,
        @Path("lockId") lockId: String
    ): ModelMessage

    @POST(ApiConstant.APP_LOG)
    suspend fun sentErrorLog(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAssignedData

    @POST(ApiConstant.CHECK_USER)
    suspend fun getCheckUser(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @POST(ApiConstant.SEARCH_USER)
    suspend fun getAllUsers(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): GetAdminUserModel

    @POST(ApiConstant.LOGIN_AS_USER)
    suspend fun getLoginAsUser(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): SendOtpResponse

    @POST(ApiConstant.WHATSAPP_OTP_MOBILE)
    suspend fun hitWhtsappOtp(
        @Body request: JsonObject
    ): SendOtpResponse

    @POST(ApiConstant.SEND_VOICE_OTP)
    suspend fun hitPhoneOtp(
        @Body request: JsonObject
    ): SendOtpResponse

    @POST(ApiConstant.CLAIM_KEY_GUEST)
    suspend fun claimKey(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @POST(ApiConstant.SERVICES_API)
    suspend fun getServicesApi(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelMessage

    @GET(ApiConstant.USER_LIAS)
    suspend fun getUserLias(
        @Header("Authorization") token: String
    ): ModelLias

    @GET(ApiConstant.USER_FIRMWARE_UPDATE)
    suspend fun getFirmware(
        @Header("Authorization") token: String
    ): ModelDfuBackendData

    @POST(ApiConstant.UPDATE_FIRMWARE_STATUS)
    suspend fun updateFirmwareStatus(
        @Header("Authorization") token: String,
        @Body request: JsonObject
    ): ModelConfigureCard

    @GET(ApiConstant.USER_CHECK_IN_PM)
    suspend fun getGuestAssignments(
        @Header("Authorization") token: String
    ): ModelCheckInPm

    @GET(ApiConstant.USER_LOCKS_UNITS_ALL)
    suspend fun getAllUnits(
        @Header("Authorization") token: String,
        @Path("id") id: String
    ): ModelUnitsData

    @Multipart()
    @POST(ApiConstant.USER_CHECK_IN_DATA)
    suspend fun checkInComplete(
        @Header("Authorization") token: String,
        @Part("name") name: RequestBody?,
        @Part("booking_number") booking_number: RequestBody?,
        @Part("document_number") document_number: RequestBody?,
        @Part("status") status: RequestBody?,
        @Part("document_type") document_type: RequestBody?,
        @Part("similarity") similarity: RequestBody?,
        @Part("expiry_date") expiry_date: RequestBody?,
        @Part identity1: MultipartBody.Part?,
        @Part identity2: MultipartBody.Part?,
        @Part document_image: MultipartBody.Part?,
        @Part captured_image: MultipartBody.Part?,
        @Part("extra") extra: JSONArray
    ): ModelMessage

    @GET(ApiConstant.USER_NOTIFICATIONS)
    suspend fun getNotificationData(
        @Header("Authorization") token: String,
        @Query("page") page: Int
    ): NotificationModel

    @POST(ApiConstant.USER_READ_NOTIFICATION)
    suspend fun readNotifications(
        @Header("Authorization") token: String
    ): ModelMessage
}