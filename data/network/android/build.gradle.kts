import com.android.tools.build.bundletool.model.utils.ResourcesUtils.configValues

plugins {
    id("keyless.android.library")
    id("config.values")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-ktor"))
    api(project(":core-lock-logs"))
    implementation(project(":core-monitoring-test"))
    implementation(project(":data-common"))
    api(project(":data-user-home"))

    api(project(":data-keyless"))

    implementation(keyless.google.play.services.location)
    implementation(keyless.google.play.services.maps)
    implementation(keyless.gson)
    implementation(keyless.okhttp.logging.interceptor)
    implementation(keyless.retrofit)
    implementation(keyless.retrofit.gson)
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val baseImageUrl = getLocalProperty("base.image.url") as String? ?: throw GradleException(
        "Missing base.image.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "baseImageUrl" to baseImageUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}