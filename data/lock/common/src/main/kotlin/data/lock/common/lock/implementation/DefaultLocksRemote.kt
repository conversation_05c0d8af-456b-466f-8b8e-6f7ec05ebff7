package data.lock.common.lock.implementation

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.encodeUrlParameter
import data.lock.common.lock.repositories.LocksRemote

class DefaultLocksRemote(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String,
    private val lockInfoHostname: String
) : LocksRemote {

    override suspend fun upgradeMaintenance(lockId: String, authorization: String): HttpResponse = logger.async() {
        val url = "$hostname/locks/upgrade-maintenance-mode/$lockId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authorization)
            )
        )

        client.request(request)
    }

    override suspend fun downgradeMaintenance(lockId: String, authorization: String): HttpResponse = logger.async() {
        val url = "$hostname/locks/downgrade-maintenance-mode/$lockId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authorization)
            )
        )

        client.request(request)
    }

    override suspend fun getDetails(
        lockName: String,
        uid: String,
        authorization: String
    ): HttpResponse = logger.async() {
        val url = "$lockInfoHostname/stage/locks/${lockName.encodeUrlParameter()}"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                // HttpHeader(HttpHeader.Type.AUTHORIZATION, authorization),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json")
            )
        )

        client.request(request)
    }

    override suspend fun deleteLock(lockId: String, authorization: String): HttpResponse = logger.async() {
        val url = "$hostname/locks/delete/$lockId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authorization)
            )
        )

        client.request(request)
    }

    override suspend fun fetchPlatformDetails(authorization: String): HttpResponse = logger.async() {
        val url = "$hostname/user/platform-details"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authorization)
            )
        )

        return@async client.request(request)
    }
}