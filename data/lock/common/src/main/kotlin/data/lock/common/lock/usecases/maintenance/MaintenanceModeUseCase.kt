package data.lock.common.lock.usecases.maintenance

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.lock.common.lock.repositories.LocksRemote

internal class MaintenanceModeUseCase(
    private val remote: LocksRemote,
    private val logger: Logger
) {

    suspend fun upgrade(lockId: String, authentication: String) = logger.async() {
        remote
            .upgradeMaintenance(lockId = lockId, authorization = authentication)
            .unwrap<Unit>()
    }

    suspend fun downgrade(lockId: String, authentication: String) = logger.async() {
        remote
            .downgradeMaintenance(lockId = lockId, authorization = authentication)
            .unwrap<Unit>()
    }
}