package data.lock.common.lock.implementation

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.lock.common.lock.repositories.LocksRemote
import data.lock.common.lock.repositories.LocksRepository
import data.lock.common.lock.usecases.LocksUseCases

class DefaultLocksRepository(
    remote: LocksRemote,

    private val logger: Logger
) : LocksRepository {

    private val useCases = LocksUseCases(remote = remote, logger = logger)

    override suspend fun upgradeMaintenance(lockId: String, authentication: String): Unit = logger.async() {
        useCases.maintenance.mode.upgrade(lockId, authentication)
    }

    override suspend fun downgradeMaintenance(lockId: String, authentication: String): Unit = logger.async() {
        useCases.maintenance.mode.downgrade(lockId, authentication)
    }

    override suspend fun getDetails(lockName: String, uid: String, authentication: String) = logger.async() {
        useCases.lock.getDetails.execute(lockName, uid, authentication)
    }

    override suspend fun deleteLock(lockId: String, authentication: String): Unit = logger.async() {
        useCases.lock.delete.execute(lockId, authentication)
    }

    override suspend fun getPlatformDetails(authentication: String) = logger.async() {
        useCases.lock.platformDetails.execute(authentication)
    }
}