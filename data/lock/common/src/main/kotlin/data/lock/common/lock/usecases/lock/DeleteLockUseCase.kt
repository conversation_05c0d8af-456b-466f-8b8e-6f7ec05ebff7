package data.lock.common.lock.usecases.lock

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.lock.common.lock.repositories.LocksRemote

internal class DeleteLockUseCase(
    private val remote: LocksRemote,
    private val logger: Logger
) {

    suspend fun execute(lockId: String, authentication: String) = logger.async() {
        remote.deleteLock(lockId = lockId, authorization = authentication).unwrap<Unit>()
    }
}