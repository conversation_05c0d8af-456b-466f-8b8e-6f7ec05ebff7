package data.lock.common.logs

import app.cash.sqldelight.db.SqlDriver
import core.locks.logs.models.LockAccessLog
import core.locks.logs.models.LockLog
import core.locks.logs.repostiories.LockLogsLocalRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.database.queries.LockAccessLogQueries
import data.database.queries.LockLogsQueries
import kotlinx.coroutines.Dispatchers
import kotlinx.datetime.Clock
import kotlin.time.Duration.Companion.days

class DefaultLockLogsLocalRepository(
    private val sqlDriver: SqlDriver,
    private val logger: Logger
) : LockLogsLocalRepository {

    private val queries = LockLogsQueries(sqlDriver)
    private val lockAccessQueries = LockAccessLogQueries(driver = sqlDriver)

    override suspend fun insert(
        actionType: String,
        lockInfoData: String,
        lockInternalId: String,
        message: String,
        provider: String,
        user: String
    ) = logger.async(Dispatchers.IO) {
        queries.insert(
            action_type = actionType,
            lock_info_data = lockInfoData,
            lock_internal_id = lockInternalId,
            message = message,
            provider = provider,
            timestamp_date = Clock.System.now().toEpochMilliseconds(),
            user = user
        )
    }

    override suspend fun getAllByInternalId(internalId: String): List<LockLog> = logger.async(Dispatchers.IO) {
        val date = Clock.System.now().toEpochMilliseconds() - 2.days.inWholeMilliseconds
        queries
            .select_by_internal_id(internalId, date)
            .executeAsList()
            .map { it.toModel() }
    }

    override suspend fun deleteById(id: Long) = logger.async(Dispatchers.IO) {
        queries.delete_by_id(id)
    }

    override suspend fun logLockAccess(log: LockAccessLog) = logger.async(Dispatchers.IO) {
        lockAccessQueries.insert_access_log(
            company_id = log.companyId,
            created_at = log.createdAt,
            device_model = log.deviceModel,
            lock_id = log.lockId,
            mobile_id = log.mobileId,
            status = log.status
        )
    }

    override suspend fun getAllAccessLogs(): List<LockAccessLog> = logger.async(Dispatchers.IO) {
        return@async lockAccessQueries.select_all_logs().executeAsList().map {
            LockAccessLog(
                companyId = it.company_id,
                createdAt = it.created_at,
                deviceModel = it.device_model,
                lockId = it.lock_id,
                mobileId = it.mobile_id,
                status = it.status
            )
        }
    }

    override suspend fun deleteAccessLogs() = logger.async() {
        lockAccessQueries.delete_all()
    }
}