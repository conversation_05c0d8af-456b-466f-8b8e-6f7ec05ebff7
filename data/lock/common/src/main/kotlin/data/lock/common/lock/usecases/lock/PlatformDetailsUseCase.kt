package data.lock.common.lock.usecases.lock

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.unwrap
import data.lock.common.lock.models.dto.PlatformDetailsDto
import data.lock.common.lock.repositories.LocksRemote

internal class PlatformDetailsUseCase(
    private val remote: LocksRemote,
    private val logger: Logger
) {

    suspend fun execute(authentication: String) = logger.async() {
        return@async remote.fetchPlatformDetails(authentication)
            .unwrap<PlatformDetailsDto>()
            .toModel()
    }
}