package data.company.impl

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyRemoteRepository
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class DefaultCompanyRemoteRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) : CompanyRemoteRepository {

    override suspend fun getLias(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/user/lias"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        client.request(request)
    }

    override suspend fun getCompanyProfile(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/company/company-profile"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )

        client.request(request)
    }

    override suspend fun updateCompanyProfile(
        companyName: String,
        address: String,
        country: String,
        city: String,
        trnNumber: String,
        zipCode: String,
        checkIn: Boolean,
        businessType: String,
        businessLia: String,
        tradeLicenseNumber: String,
        timezoneOffset: String,
        timezoneName: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/company/update-company"
        val body = buildJsonObject {
            put("company_name", companyName)
            put("address", address)
            put("country", country)
            put("city", city)
            put("trn_number", trnNumber)
            put("zip_code", zipCode)
            put("checkin", checkIn)
            put("business_type", businessType)
            put("business_lia", businessLia)
            put("trade_license_number", tradeLicenseNumber)
            put("timezone", timezoneOffset)
            put("timezone_name", timezoneName)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/json"),
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            ),
            body = body
        )

        client.request(request)
    }
}