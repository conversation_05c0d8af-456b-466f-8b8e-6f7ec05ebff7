package data.company.impl

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.http.client.HttpRequestForm
import core.http.client.HttpRequestFormTextItem
import core.http.client.HttpResponse
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyStaffRemoteRepository

class DefaultCompanyStaffRemoteRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) : CompanyStaffRemoteRepository {
    override suspend fun getAllStaff(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/company/staff"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )
        return@async client.request(request)
    }

    override suspend fun getRoles(authentication: String): HttpResponse = logger.async() {
        val url = "$baseUrl/roles"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication)
            )
        )
        return@async client.request(request)
    }

    override suspend fun addStaff(
        countryCode: String,
        mobileNumber: String,
        email: String,
        userName: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/company/staff"

        val body = HttpRequestForm(
            binaryData = false,
            items = listOf(
                HttpRequestFormTextItem("country_code", countryCode),
                HttpRequestFormTextItem("mobile_number", mobileNumber),
                HttpRequestFormTextItem("email", email),
                HttpRequestFormTextItem("username", userName),
                HttpRequestFormTextItem("first_name", firstName),
                HttpRequestFormTextItem("last_name", lastName),
                HttpRequestFormTextItem("role", role),
                HttpRequestFormTextItem("passport_number", passportNumber)
            )
        )

        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/x-www-form-urlencoded")
            ),
            form = body
        )
        return@async client.formRequest(request)
    }

    override suspend fun updateStaff(
        staffId: String,
        countryCode: String,
        mobileNumber: String,
        email: String,
        userName: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String,
        authentication: String
    ): HttpResponse = logger.async() {
        val url = "$baseUrl/company/staff"

        val body = HttpRequestForm(
            binaryData = false,
            items = listOf(
                HttpRequestFormTextItem("country_code", countryCode),
                HttpRequestFormTextItem("mobile_number", mobileNumber),
                HttpRequestFormTextItem("email", email),
                HttpRequestFormTextItem("username", userName),
                HttpRequestFormTextItem("first_name", firstName),
                HttpRequestFormTextItem("last_name", lastName),
                HttpRequestFormTextItem("role", role),
                HttpRequestFormTextItem("passport_number", passportNumber),
                HttpRequestFormTextItem("staff_id", staffId)
            )
        )

        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(HttpHeader.Type.AUTHORIZATION, authentication),
                HttpHeader(HttpHeader.Type.CONTENT_TYPE, "application/x-www-form-urlencoded")
            ),
            form = body
        )
        return@async client.formRequest(request)
    }
}