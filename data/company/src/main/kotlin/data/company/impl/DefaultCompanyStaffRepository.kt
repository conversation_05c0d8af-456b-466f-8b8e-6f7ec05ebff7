package data.company.impl

import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.http.ServerResponse
import data.common.http.unwrap
import data.common.preferences.Preferences
import data.company.models.StaffMember
import data.company.models.StaffMemberRole
import data.company.models.dtos.StaffMemberRolesResponseDto
import data.company.models.dtos.StaffMembersResponseDto
import data.company.repositories.CompanyStaffRepository

class DefaultCompanyStaffRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val baseUrl: String
) : CompanyStaffRepository {
    private val remote = DefaultCompanyStaffRemoteRepository(client, logger, baseUrl)

    private val local: MutableMap<String, StaffMember> = mutableMapOf()

    override suspend fun getAllStaff(): List<StaffMember> = logger.async() {
        return@async remote
            .getAllStaff(Preferences.authenticationToken.get())
            .unwrap<StaffMembersResponseDto>()
            .members
            .map {
                local[it._id] = it.toModel()
                local[it._id]!!
            }
    }

    override fun getById(id: String): StaffMember = local[id]!!

    override suspend fun getRoles(): List<StaffMemberRole> = logger.async() {
        return@async remote
            .getRoles(Preferences.authenticationToken.get())
            .unwrap<StaffMemberRolesResponseDto>()
            .toModel()
    }

    override suspend fun addStaff(
        countryCode: String,
        mobileNumber: String,
        email: String,
        username: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String
    ): String = logger.async() {
        return@async remote.addStaff(
            countryCode,
            mobileNumber,
            email,
            username,
            firstName,
            lastName,
            role,
            passportNumber,
            Preferences.authenticationToken.get()
        ).unwrap<ServerResponse>()
            .message
    }

    override suspend fun updateStaff(
        staffId: String,
        countryCode: String,
        mobileNumber: String,
        email: String,
        username: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String
    ): String = logger.async() {
        return@async remote.updateStaff(
            staffId,
            countryCode,
            mobileNumber,
            email,
            username,
            firstName,
            lastName,
            role,
            passportNumber,
            Preferences.authenticationToken.get()
        ).unwrap<ServerResponse>()
            .message
    }
}