package data.utils.android.settings

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import data.common.preferences.Preferences
import data.network.android.LockModelAdmin
import data.network.android.LocksListResponse
import data.network.android.models.AccessLogs
import data.network.android.models.LockHistoryModel
import data.network.android.models.ModelDfuBackendData
import data.utils.android.CommonValues

class SharedPreferenceUtils
private constructor(val context: Context) {
    val TAG = "//"

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(CommonValues.PREFERENCE_NAME, Context.MODE_PRIVATE)
    private val persistableSharedPreferences: SharedPreferences =
        context.getSharedPreferences(CommonValues.PERSISTABLE_PREFERENCE_NAME, Context.MODE_PRIVATE)

    private val editor: SharedPreferences.Editor = sharedPreferences.edit()
    private val persistableEditor: SharedPreferences.Editor = persistableSharedPreferences.edit()

    companion object {

        @SuppressLint("StaticFieldLeak")
        private var instance: SharedPreferenceUtils? = null

        fun getInstance(ctx: Context): SharedPreferenceUtils {
            if (instance == null) {
                instance = SharedPreferenceUtils(ctx)
            }
            return instance!!
        }
    }


    var isApiDone: Boolean
        get() = sharedPreferences["isApiDone", false]!!
        set(value) = sharedPreferences.set("isApiDone", value)
    var isApiWorked: Boolean
        get() = sharedPreferences["isApiWorked", false]!!
        set(value) = sharedPreferences.set("isApiWorked", value)

    var profileStatus: Boolean
        get() = sharedPreferences["profileStatus", false]!!
        set(value) = sharedPreferences.set("profileStatus", value)

    var chatId: String?
        get() = sharedPreferences["chatId"]
        set(value) = sharedPreferences.set("chatId", value)

    var totalUnreadNotification: Int
        get() = sharedPreferences["totalUnreadNotification", 0]!!
        set(value) = sharedPreferences.set("totalUnreadNotification", value)

    var name: String
        get() = sharedPreferences["name", ""]!!
        set(value) = sharedPreferences.set("name", value)

    var profile_photo: String
        get() = sharedPreferences["profile_photo", ""]!!
        set(value) = sharedPreferences.set("profile_photo", value)

    var isLoginTutorialDone: Boolean
        get() = sharedPreferences["isLoginTutorialDone", false]!!
        set(value) = sharedPreferences.set("isLoginTutorialDone", value)

    var selectedLanguage: String
        // get() = sharedPreferences["selectedLanguage", "en"]!!
        get() = sharedPreferences["selectedLanguage", ""]!!
        set(value) = sharedPreferences.set("selectedLanguage", value)

    var companyId: String
        get() = sharedPreferences["companyId", ""]!!
        set(value) = sharedPreferences.set("companyId", value)

    var isDenied: Int
        get() = sharedPreferences["isDenied", 0]!!
        set(value) = sharedPreferences.set("isDenied", value)

    var uuid: String
        get() = sharedPreferences["uuid", ""]!!
        set(value) = sharedPreferences.set("uuid", value)

    var videoOn: Boolean
        get() = sharedPreferences["videoOn", false]!!
        set(value) = sharedPreferences.set("videoOn", value)

    var token: String
        get() = if (Preferences.isAdminLogin()) {
            CommonValues.adminTempToken
        } else {
            sharedPreferences["token", ""]!!
        }
        set(value) = sharedPreferences.set("token", value)

    var isOffline: String
        get() = sharedPreferences["isOffline", ""]!!
        set(value) = sharedPreferences.set("isOffline", value)

    var show8HoursWarning: Boolean
        get() = sharedPreferences["show8HoursWarning", false]!!
        set(value) = sharedPreferences.set("show8HoursWarning", value)

    var isAlarmON: Boolean
        get() = sharedPreferences["isAlarmON", false]!!
        set(value) = sharedPreferences.set("isAlarmON", value)

    var userMobile: String
        get() = sharedPreferences["userMobile", ""]!!
        set(value) = sharedPreferences.set("userMobile", value)

    var isScreenLockEnabled: Boolean
        get() = sharedPreferences["isScreenLockEnabled", false]!!
        set(value) = sharedPreferences.set("isScreenLockEnabled", value)

    var propertyCount: Int
        get() = sharedPreferences["propertyCount", 0]!!
        set(value) = sharedPreferences.set("propertyCount", value)

    var language: String?
        get() = persistableSharedPreferences["language", "en"]
        set(value) = persistableSharedPreferences.set("language", value)

    var iseoUrl: String?
        get() = persistableSharedPreferences["iseoUrl", ""]
        set(value) = persistableSharedPreferences.set("iseoUrl", value)

    var plantName: String?
        get() = persistableSharedPreferences["plantName", ""]
        set(value) = persistableSharedPreferences.set("plantName", value)

    var forgotToken: String?
        get() = persistableSharedPreferences["forgotToken", ""]
        set(value) = persistableSharedPreferences.set("forgotToken", value)

    /**
     * puts a key value pair in shared prefs if doesn't exists, otherwise updates value on given [key]
     */
    operator fun SharedPreferences.set(key: String, value: Any?) {
        when (value) {
            is String? -> edit { it.putString(key, value) }
            is Int -> edit({ it.putInt(key, value) })
            is Boolean -> edit({ it.putBoolean(key, value) })
            is Float -> edit({ it.putFloat(key, value) })
            is Long -> edit({ it.putLong(key, value) })
            else -> Log.e(TAG, "Setting shared pref failed for key: $key and value: $value ")
        }
    }

    private inline fun SharedPreferences.edit(operation: (SharedPreferences.Editor) -> Unit) {
        val editor = this.edit()
        operation(editor)
        editor.apply()
    }

    /**
     * finds value on given key.
     * [T] is the type of value
     * @param defaultValue optional default value - will take null for strings, false for bool and -1 for numeric values if [defaultValue] is not specified
     */
    inline operator fun <reified T : Any> SharedPreferences.get(
        key: String,
        defaultValue: T? = null
    ): T? {
        return when (T::class) {
            String::class -> getString(key, defaultValue as? String) as T?
            Int::class -> getInt(key, defaultValue as? Int ?: -1) as T?
            Boolean::class -> getBoolean(key, defaultValue as? Boolean ?: false) as T?
            Float::class -> getFloat(key, defaultValue as? Float ?: -1f) as T?
            Long::class -> getLong(key, defaultValue as? Long ?: -1) as T?
            else -> throw UnsupportedOperationException("Not yet implemented")
        }
    }

    fun deletePreferences() {
        val mSelectedLanguage = selectedLanguage
        val mIsLoginTutorialDone = isLoginTutorialDone
        editor.clear()
        editor.commit()
        selectedLanguage = mSelectedLanguage
        isLoginTutorialDone = mIsLoginTutorialDone
    }

    fun homeAdminLockData(homeData: String) {
        sharedPreferences["homeAdminLockData"] = homeData
    }

    fun getLockHistoryBackup(): ArrayList<LockHistoryModel> {
        val gson = Gson()
        val json = sharedPreferences.getString("lockHistory", "")
        if (json.isNullOrEmpty()) {
            return arrayListOf()
        }
        return gson.fromJson(
            json,
            object : TypeToken<List<LockHistoryModel>>() {
            }.type
        )
    }

    fun setLockHistoryBackup(list: ArrayList<LockHistoryModel>) {
        val gson = Gson()
        val toJson = gson.toJson(list)
        sharedPreferences.edit().putString("lockHistory", toJson).apply()
    }

    fun getLockHistoryMain(): ArrayList<AccessLogs> {
        val gson = Gson()
        val json = sharedPreferences.getString("getLockHistoryMain", "")
        if (json.isNullOrEmpty()) {
            return arrayListOf()
        }
        return gson.fromJson(
            json,
            object : TypeToken<List<AccessLogs>>() {
            }.type
        )
    }

    fun setLockHistoryMain(list: ArrayList<AccessLogs>) {
        val gson = Gson()
        val toJson = gson.toJson(list)
        sharedPreferences.edit().putString("getLockHistoryMain", toJson).apply()
    }

    fun clearLockHistory() {
        sharedPreferences.edit().remove("lockHistory").apply()
        sharedPreferences.edit().remove("getLockHistoryMain").apply()
    }

    fun getAdminLockData(): ArrayList<LockModelAdmin> {
        val gson = Gson()
        val json = sharedPreferences.getString("homeAdminLockData", "")
        return gson.fromJson(
            json,
            object : TypeToken<List<LockModelAdmin>>() {
            }.type
        )
    }

    fun homeLockData(homeData: String) {
        sharedPreferences["homeLockData"] = homeData
    }

    fun getLockData(): ArrayList<LocksListResponse.LocksModel> {
        val gson = Gson()
        val json = sharedPreferences.getString("homeLockData", "")
        if (!json.isNullOrEmpty()) {
            return gson.fromJson(
                json,
                object : TypeToken<List<LocksListResponse.LocksModel>>() {
                }.type
            )
        } else {
            return arrayListOf()
        }
    }

    fun homePropertiesData(homePropertyData: String) {
        sharedPreferences["homePropertyData"] = homePropertyData
    }

    fun getPropertiesData(): ArrayList<LocksListResponse.PropertiesMainModel> {
        val gson = Gson()
        val json = sharedPreferences.getString("homePropertyData", "")

        return runCatching<ArrayList<LocksListResponse.PropertiesMainModel>> {
            gson.fromJson(
                json,
                object : TypeToken<List<LocksListResponse.PropertiesMainModel>>() {
                }.type
            )
        }.getOrNull() ?: arrayListOf()
    }

    fun putFirmResponse(model: ModelDfuBackendData) {
        val gson = Gson()
        val json: String = gson.toJson(model)

        sharedPreferences.edit { editor ->
            editor.putString("KEY_FIRMWARE_RESPONSE", json)
        }
    }

    fun getFirmResponse(): ModelDfuBackendData? {
        val gson = Gson()
        val json: String? = sharedPreferences.get("KEY_FIRMWARE_RESPONSE", null)
        return gson.fromJson(json, ModelDfuBackendData::class.java)
    }
}