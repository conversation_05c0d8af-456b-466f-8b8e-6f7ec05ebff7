import kotlin.Boolean;
import kotlin.Int;
import kotlin.collections.List;

CREATE TABLE IF NOT EXISTS IconEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    type TEXT NOT NULL,
    created_at TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS TimeProfileIdEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    created_at TEXT NOT NULL,
    created_by TEXT NOT NULL,
    iseo_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    status INTEGER AS Boolean NOT NULL
);

CREATE TABLE IF NOT EXISTS TimeRangeEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    allowed_days TEXT AS List<Int> NOT NULL,
    always_open INTEGER AS Boolean NOT NULL,
    created_at TEXT NOT NULL,
    holidays INTEGER AS Boolean NOT NULL,
    name TEXT NOT NULL,
    routine_id TEXT NOT NULL,
    status INTEGER AS Boolean NOT NULL,
    start_hour INTEGER NOT NULL,
    start_minute INTEGER NOT NULL,
    end_hour INTEGER NOT NULL,
    end_minute INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS PropertyEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    latitude TEXT NOT NULL,
    longitude TEXT NOT NULL,
    manager_id TEXT NOT NULL,
    manager_type TEXT NOT NULL,
    emirate TEXT NOT NULL,
    area TEXT NOT NULL,
    building_name TEXT NOT NULL,
    total_floors INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    icon_id TEXT NOT NULL,
    support_call_number TEXT NOT NULL,
    support_whatsapp_number TEXT NOT NULL,
    count INTEGER NOT NULL,
    floor TEXT NOT NULL,
    name TEXT NOT NULL,
    room_number TEXT NOT NULL,
    laundry_number TEXT NOT NULL,
    grocery_number TEXT NOT NULL,
    maintenance_number TEXT NOT NULL,
    apartment_number TEXT NOT NULL,
    map_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS PropertyWithIconEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    property_id TEXT NOT NULL,
    icon_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS LockAssignmentEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    assigned_at TEXT NOT NULL,
    assigned_by TEXT NOT NULL,
    assigned_to TEXT NOT NULL,
    lock_id TEXT NOT NULL,
    time_profile_id TEXT NOT NULL,
    valid_from TEXT NOT NULL,
    valid_to TEXT NOT NULL,
    status INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS LockAssignmentWithTimeRangeEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    lock_assignment_id TEXT NOT NULL,
    time_range_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS LockEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    assignment_id TEXT NOT NULL,
    owner_id TEXT NOT NULL,
    unit_id TEXT NOT NULL,
    booking_number TEXT NOT NULL,
    privacy INTEGER AS Boolean NOT NULL,
    privacy_changed INTEGER AS Boolean NOT NULL,
    company_check_in INTEGER AS Boolean NOT NULL,
    check_in INTEGER AS Boolean NOT NULL,
    total_checkins INTEGER NOT NULL,
    access_key TEXT NOT NULL,
    battery_level INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    image TEXT NOT NULL,
    lock_uid TEXT NOT NULL,
    name TEXT NOT NULL,
    desc TEXT NOT NULL,
    provider TEXT NOT NULL,
    status INTEGER NOT NULL,
    unique_key TEXT NOT NULL,
    time_zone TEXT NOT NULL,
    internal_id TEXT NOT NULL,
    encrypted_key TEXT NOT NULL,
    privacy_mode INTEGER AS Boolean NOT NULL,
    primary_lock INTEGER AS Boolean NOT NULL,
    privacy_permission INTEGER AS Boolean NOT NULL,
    privacy_owner INTEGER AS Boolean NOT NULL,
    property_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS LockEntityWithIconEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    lock_id TEXT NOT NULL,
    icon_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS UserHomeResponseEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    user_uid TEXT NOT NULL UNIQUE,
    success INTEGER AS Boolean NOT NULL,
    message TEXT NOT NULL,
    assignment_id TEXT NOT NULL,
    logout INTEGER AS Boolean NOT NULL,
    is_paid INTEGER AS Boolean NOT NULL,
    total_unread_notification INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS UserHomeResponseWithLockEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    user_uid TEXT NOT NULL,
    lock_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS UserHomeResponseWithPropertyEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    user_uid TEXT NOT NULL,
    property_id TEXT NOT NULL
);