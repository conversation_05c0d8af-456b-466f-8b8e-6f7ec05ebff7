select_by_internal_id:
SELECT * FROM LockLogEntity WHERE lock_internal_id = :lock_internal_id AND timestamp_date >= :timestamp_date ORDER BY timestamp_date DESC;

insert:
INSERT INTO LockLogEntity (
action_type,
lock_info_data,
lock_internal_id,
message,
timestamp_date,
provider,
user

) VALUES (
:action_type,
:lock_info_data,
:lock_internal_id,
:message,
:timestamp_date,
:provider,
:user
);

delete_by_id:
DELETE FROM LockLogEntity WHERE id = :id;