select_latest_response:
SELECT * FROM UserHomeResponseEntity
WHERE UserHomeResponseEntity.user_uid = :user_uid;

select_locks_of_home_response:
SELECT DISTINCT lock_id, user_uid FROM UserHomeResponseWithLockEntity
WHERE UserHomeResponseWithLockEntity.user_uid = :user_uid;

select_properties_of_home_response:
SELECT DISTINCT property_id, user_uid FROM UserHomeResponseWithPropertyEntity
WHERE UserHomeResponseWithPropertyEntity.user_uid = :user_uid;

select_lock_by_id:
SELECT * FROM LockEntity
WHERE LockEntity.id = :lock_id;

select_icons_of_lock_by_id:
SELECT DISTINCT icon_id, lock_id FROM LockEntityWithIconEntity
WHERE LockEntityWithIconEntity.lock_id = :lock_id;

select_assignment_by_id:
SELECT * FROM LockAssignmentEntity
WHERE LockAssignmentEntity.id = :lock_assignment_id;

select_time_ranges_of_assignment_by_id:
SELECT DISTINCT time_range_id, lock_assignment_id FROM LockAssignmentWithTimeRangeEntity
WHERE LockAssignmentWithTimeRangeEntity.lock_assignment_id = :lock_assignment_id;

select_property_by_id:
SELECT * FROM PropertyEntity
WHERE PropertyEntity.id = :property_id;

select_icons_of_property_by_id:
SELECT DISTINCT property_id, icon_id FROM PropertyWithIconEntity
WHERE PropertyWithIconEntity.property_id = :property_id;

select_lock_property_by_id:
SELECT * FROM LockPropertyEntity
WHERE LockPropertyEntity.lock_id = :lock_id;

select_time_profile_by_id:
SELECT * FROM TimeProfileIdEntity
WHERE TimeProfileIdEntity.id = :time_profile_id;

select_time_range_by_id:
SELECT * FROM TimeRangeEntity
WHERE TimeRangeEntity.id = :time_range_id;

select_icon_by_id:
SELECT * FROM IconEntity
WHERE IconEntity.id = :icon_id;

insert_home_response {
    DELETE FROM UserHomeResponseEntity WHERE user_uid = :user_uid;
    DELETE FROM UserHomeResponseWithLockEntity WHERE user_uid = :user_uid;
    DELETE FROM UserHomeResponseWithPropertyEntity WHERE user_uid = :user_uid;

    INSERT INTO UserHomeResponseEntity (user_uid, success, message, assignment_id, logout, is_paid, total_unread_notification)
    VALUES (:user_uid, :success, :message, :assignment_id, :logout, :is_paid, :total_unread_notification);
}

insert_user_home_response_with_lock {
    INSERT INTO UserHomeResponseWithLockEntity (user_uid, lock_id)
    VALUES (:user_uid, :lock_id);
}

insert_user_home_response_with_property {
    INSERT INTO UserHomeResponseWithPropertyEntity (user_uid, property_id)
    VALUES (:user_uid, :property_id);
}

insert_lock {
    DELETE FROM LockEntityWithIconEntity WHERE lock_id = :id;

    INSERT OR REPLACE INTO LockEntity (id, assignment_id, owner_id, unit_id, booking_number, privacy, privacy_changed, company_check_in, check_in, total_checkins, access_key, battery_level, created_at, image, lock_uid, name, desc, provider, status, unique_key, time_zone, internal_id, encrypted_key, privacy_mode, primary_lock, privacy_permission, privacy_owner, property_id, firmware_updated, passcode_id, passcode, firmware_version, firmware_available_version, tedee_lock_id)
    VALUES (:id, :assignment_id, :owner_id, :unit_id, :booking_number, :privacy, :privacy_changed, :company_check_in, :check_in, :total_checkins, :access_key, :battery_level, :created_at, :image, :lock_uid, :name, :desc, :provider, :status, :unique_key, :time_zone, :internal_id, :encrypted_key, :privacy_mode, :primary_lock, :privacy_permission, :privacy_owner, :property_id, :firmware_updated, :passcode_id, :passcode, :firmware_version, :firmware_available_version, :tedee_lock_id);
}

insert_lock_with_icon:
INSERT OR REPLACE INTO LockEntityWithIconEntity (lock_id, icon_id)
VALUES (:lock_id, :icon_id);

insert_assignment {
    DELETE FROM LockAssignmentWithTimeRangeEntity WHERE lock_assignment_id = :id;

    INSERT OR REPLACE INTO LockAssignmentEntity (id, assigned_at, assigned_by, assigned_to, lock_id, time_profile_id, valid_from, valid_to, status)
    VALUES (:id, :assigned_at, :assigned_by, :assigned_to, :lock_id, :time_profile_id, :valid_from, :valid_to, :status);

}

insert_property {
    DELETE FROM PropertyWithIconEntity WHERE property_id = :id;

    INSERT OR REPLACE INTO PropertyEntity (id, latitude, longitude, manager_id, manager_type, emirate, area, building_name, total_floors, created_at, icon_id, support_call_number, support_whatsapp_number, count, laundry_number, grocery_number, maintenance_number)
    VALUES (:id, :latitude, :longitude, :manager_id, :manager_type, :emirate, :area, :building_name, :total_floors, :created_at, :icon_id, :support_call_number, :support_whatsapp_number, :count, :laundry_number, :grocery_number, :maintenance_number);
}

insert_property_with_icon:
INSERT OR REPLACE INTO PropertyWithIconEntity (property_id, icon_id)
VALUES (:property_id, :icon_id);

insert_time_profile:
INSERT OR REPLACE INTO TimeProfileIdEntity (id, created_at, created_by, iseo_id, name, status)
VALUES (:id, :created_at, :created_by, :iseo_id, :name, :status);

insert_time_range:
INSERT OR REPLACE INTO TimeRangeEntity (id, allowed_days, always_open, created_at, holidays, name, routine_id, status, start_hour, start_minute, end_hour, end_minute)
VALUES (:id, :allowed_days, :always_open, :created_at, :holidays, :name, :routine_id, :status, :start_hour, :start_minute, :end_hour, :end_minute);

insert_lock_assignment_with_time_range:
INSERT OR REPLACE INTO LockAssignmentWithTimeRangeEntity (lock_assignment_id, time_range_id)
VALUES (:lock_assignment_id, :time_range_id);

insert_icon:
INSERT OR REPLACE INTO IconEntity (id, name, icon, type, created_at)
VALUES (:id, :name, :icon, :type, :created_at);

insert_lock_property {
    INSERT OR REPLACE INTO LockPropertyEntity (lock_id, id, latitude, longitude, manager_id, manager_type, emirate, area, building_name, total_floors, created_at, icon_id, support_call_number, support_whatsapp_number, count, floor, name, room_number, laundry_number, grocery_number, maintenance_number, apartment_number, map_id)
    VALUES (:lock_id, :id, :latitude, :longitude, :manager_id, :manager_type, :emirate, :area, :building_name, :total_floors, :created_at, :icon_id, :support_call_number, :support_whatsapp_number, :count, :floor, :name, :room_number, :laundry_number, :grocery_number, :maintenance_number, :apartment_number, :map_id);
}