CREATE TABLE IF NOT EXISTS LockPropertyEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    latitude TEXT NOT NULL,
    longitude TEXT NOT NULL,
    manager_id TEXT NOT NULL,
    manager_type TEXT NOT NULL,
    emirate TEXT NOT NULL,
    area TEXT NOT NULL,
    building_name TEXT NOT NULL,
    total_floors INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    icon_id TEXT NOT NULL,
    support_call_number TEXT NOT NULL,
    support_whatsapp_number TEXT NOT NULL,
    count INTEGER NOT NULL,
    floor TEXT NOT NULL,
    name TEXT NOT NULL,
    room_number TEXT NOT NULL,
    laundry_number TEXT NOT NULL,
    grocery_number TEXT NOT NULL,
    maintenance_number TEXT NOT NULL,
    apartment_number TEXT NOT NULL,
    map_id TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS LockPropertyWithIconEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    property_id TEXT NOT NULL,
    icon_id TEXT NOT NULL
);
