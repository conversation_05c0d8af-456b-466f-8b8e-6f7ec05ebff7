PRAGMA foreign_keys=off;

BEGIN TRANSACTION;
DROP TABLE IF EXISTS LockPropertyWithIconEntity;

ALTER TABLE PropertyEntity RENAME TO PropertyEntity_old;

CREATE TABLE IF NOT EXISTS PropertyEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    id TEXT NOT NULL UNIQUE,
    latitude TEXT NOT NULL,
    longitude TEXT NOT NULL,
    manager_id TEXT NOT NULL,
    manager_type TEXT NOT NULL,
    emirate TEXT NOT NULL,
    area TEXT NOT NULL,
    building_name TEXT NOT NULL,
    total_floors INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    icon_id TEXT NOT NULL,
    support_call_number TEXT NOT NULL,
    support_whatsapp_number TEXT NOT NULL,
    count INTEGER NOT NULL,
    laundry_number TEXT NOT NULL,
    grocery_number TEXT NOT NULL,
    maintenance_number TEXT NOT NULL
);

INSERT INTO PropertyEntity SELECT db_table_id,id,latitude,longitude,manager_id,manager_type,emirate,area,building_name,total_floors,created_at,icon_id,support_call_number,support_whatsapp_number,count,laundry_number,grocery_number,maintenance_number FROM PropertyEntity_old;

DROP TABLE IF EXISTS PropertyEntity_old;

ALTER TABLE LockPropertyEntity RENAME TO LockPropertyEntity_old;

CREATE TABLE IF NOT EXISTS LockPropertyEntity(
    db_table_id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    lock_id TEXT NOT NULL UNIQUE,
    id TEXT NOT NULL,
    latitude TEXT NOT NULL,
    longitude TEXT NOT NULL,
    manager_id TEXT NOT NULL,
    manager_type TEXT NOT NULL,
    emirate TEXT NOT NULL,
    area TEXT NOT NULL,
    building_name TEXT NOT NULL,
    total_floors INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    icon_id TEXT NOT NULL,
    support_call_number TEXT NOT NULL,
    support_whatsapp_number TEXT NOT NULL,
    count INTEGER NOT NULL,
    floor TEXT NOT NULL,
    name TEXT NOT NULL,
    room_number TEXT NOT NULL,
    laundry_number TEXT NOT NULL,
    grocery_number TEXT NOT NULL,
    maintenance_number TEXT NOT NULL,
    apartment_number TEXT NOT NULL,
    map_id TEXT NOT NULL
);


DROP TABLE IF EXISTS LockPropertyEntity_old;

DELETE FROM UserHomeResponseEntity;

COMMIT;

PRAGMA foreign_keys=on;
