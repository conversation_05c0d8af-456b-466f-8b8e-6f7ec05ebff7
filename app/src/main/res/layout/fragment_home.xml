<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipeRefresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorAccent"
        tools:context=".home.HomeFragment">


        <TextView
            android:id="@+id/titlePage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:fontFamily="@font/poppins_bold_700"
            android:singleLine="true"
            android:text="@string/home"
            android:textColor="@color/white"
            android:textSize="24dp"
            app:layout_constraintEnd_toStartOf="@+id/notificationIconGuest"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

<!--        ONLY VISIBLE WHEN INSTALLER ROLE-->
        <ImageView
            android:id="@+id/changeLanguage"
            android:layout_width="24dp"
            android:layout_height="24dp"
            style="@style/ImageMirror"
            android:layout_marginEnd="16dp"
            android:background="@drawable/iv_change_language"
            android:backgroundTint="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/titlePage"
            app:layout_constraintEnd_toStartOf="@+id/ivAddAdminLock"
            app:layout_constraintTop_toTopOf="@+id/titlePage" />

        <ImageView
            android:id="@+id/ivSettings"
            android:layout_width="wrap_content"
            style="@style/ImageMirror"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:src="@drawable/iv_settings"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/titlePage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/titlePage" />


<!--        ONLY VISIBLE AT ADMIN ROLE-->
        <ImageView
            android:id="@+id/ivAddAdminLock"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/iv_bg_new"
            android:padding="6dp"
            android:src="@drawable/iv_add_black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivSettings"
            app:layout_constraintEnd_toStartOf="@+id/ivSettings"
            app:layout_constraintTop_toTopOf="@+id/ivSettings" />


        <ImageView
            android:id="@+id/notificationIconGuest"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:src="@drawable/iv_notifications"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/changeLanguage"
            app:layout_constraintEnd_toStartOf="@+id/changeLanguage"
            app:layout_constraintTop_toTopOf="@+id/changeLanguage" />


        <TextView
            android:id="@+id/notificationCountGuest"
            android:layout_width="17dp"
            android:layout_height="16dp"
            android:background="@drawable/circle_notification"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:maxLength="3"
            android:textColor="@color/white"
            android:textSize="10dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/notificationIconGuest"
            app:layout_constraintEnd_toStartOf="@+id/changeLanguage"
            app:layout_constraintHorizontal_bias="0.48"
            app:layout_constraintStart_toStartOf="@+id/notificationIconGuest"
            app:layout_constraintTop_toTopOf="@+id/titlePage"
            app:layout_constraintVertical_bias="0.45" />


<!--        ONLY USED IN PM ROLES and PARTIALLY in INSTALLER ROLE-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mainFilter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="18dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/titlePage"
            app:layout_constraintVertical_bias="0.0">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:id="@+id/constraintInstaller"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivLockInstaller"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/iv_checked_radio"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/txtLocksInstaller"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/installation"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivLockInstaller"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/viewLockInstaller"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="8dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ivMaintenance"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <ImageView
                    android:id="@+id/ivMaintenance"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginStart="32dp"
                    android:src="@drawable/iv_not_checked_grey"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/txtLocksInstaller"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/txtMaintenance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/maintenance"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@+id/txtLocksInstaller"
                    app:layout_constraintStart_toEndOf="@+id/ivMaintenance"
                    app:layout_constraintTop_toTopOf="@+id/txtLocksInstaller" />

                <View
                    android:id="@+id/viewMaintenance"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@+id/txtMaintenance"
                    app:layout_constraintStart_toStartOf="@+id/ivMaintenance"
                    app:layout_constraintTop_toTopOf="parent" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:visibility="visible"
                android:id="@+id/mainFilterInner"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <View
                    android:layout_width="10dp"
                    android:layout_height="35dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <ImageView
                    android:id="@+id/ivLock"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/iv_checked_radio"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/txtLocks"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/lock"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivLock"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/viewLock"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ivProperties"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <ImageView
                    android:id="@+id/ivProperties"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginStart="32dp"
                    android:src="@drawable/iv_not_checked_grey"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/txtLocks"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/txtProperties"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/buildings"
                    android:textColor="@color/white"
                    android:textSize="16dp"
                    app:layout_constraintBottom_toBottomOf="@+id/txtLocks"
                    app:layout_constraintStart_toEndOf="@+id/ivProperties"
                    app:layout_constraintTop_toTopOf="@+id/txtLocks" />

                <View
                    android:id="@+id/viewProperties"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@+id/txtProperties"
                    app:layout_constraintStart_toStartOf="@+id/ivProperties"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/iv_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/iv_bg_new"
                    android:padding="6dp"
                    android:visibility="visible"
                    android:src="@drawable/iv_add_black"
                    app:layout_constraintBottom_toBottomOf="@+id/txtLocks"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/txtLocks" />


                <ImageView
                    android:id="@+id/notificationIcon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/iv_notifications"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@+id/txtLocks"
                    app:layout_constraintEnd_toStartOf="@+id/iv_add"
                    app:layout_constraintTop_toTopOf="@+id/txtLocks" />


                <TextView
                    android:id="@+id/notificationCount"
                    android:layout_width="17dp"
                    android:layout_height="16dp"
                    android:background="@drawable/circle_notification"
                    android:fontFamily="@font/poppins_medium_500"
                    android:gravity="center"
                    android:maxLength="3"
                    android:visibility="gone"
                    android:textColor="@color/white"
                    android:textSize="10dp"
                    app:layout_constraintBottom_toBottomOf="@+id/notificationIcon"
                    app:layout_constraintEnd_toStartOf="@+id/iv_add"
                    app:layout_constraintHorizontal_bias="0.48"
                    app:layout_constraintStart_toStartOf="@+id/notificationIcon"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>


        <FrameLayout
            android:id="@+id/frameContainerHome"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="15dp"
            android:background="@drawable/white_top_corners"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mainFilter"
            app:layout_constraintVertical_bias="0.0">

            <androidx.cardview.widget.CardView
                android:id="@+id/mapLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="-12dp"
                android:elevation="0dp"
                android:visibility="gone"
                app:cardCornerRadius="32dp">

                <FrameLayout
                    android:id="@+id/mapFragment"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingBottom="20dp" />
            </androidx.cardview.widget.CardView>

        </FrameLayout>

<!--        PROPERTIES/BUILDINGS-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/propertyItemView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="@dimen/dp_20"
            android:background="@drawable/bg_button_rounded"
            android:backgroundTint="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <include
                android:id="@+id/includeMarkLayout"
                layout="@layout/property_item_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <me.grantland.widget.AutofitTextView
                android:id="@+id/viewDetailBtn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="15dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:paddingStart="30dp"
                android:paddingTop="8dp"
                android:textSize="14dp"
                app:minTextSize="8dp"
                android:autoSizeMaxTextSize="14dp"
                android:singleLine="true"
                android:paddingEnd="30dp"
                android:paddingBottom="8dp"
                android:text="@string/view_details"
                android:textColor="@color/black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/directionBtn"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/includeMarkLayout" />

            <me.grantland.widget.AutofitTextView
                android:id="@+id/directionBtn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:paddingStart="30dp"
                android:textSize="14dp"
                app:minTextSize="8dp"
                android:autoSizeMaxTextSize="14dp"
                android:singleLine="true"
                android:paddingTop="8dp"
                android:paddingEnd="30dp"
                android:paddingBottom="8dp"
                android:text="@string/direction"
                android:textColor="@color/black"
                app:layout_constraintBottom_toBottomOf="@+id/viewDetailBtn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/viewDetailBtn" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/propertiesListRV"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/frameContainerHome" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>