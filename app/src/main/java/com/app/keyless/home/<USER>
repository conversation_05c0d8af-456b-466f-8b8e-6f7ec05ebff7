package com.app.keyless.home

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.MenuItem
import android.view.ViewGroup
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.annotation.MainThread
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import androidx.core.view.isVisible
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.app.keyless.R
import com.app.keyless.databinding.ActivityDashboardBinding
import com.google.android.gms.tasks.OnCompleteListener
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.iseo.v364droidsdk.AndroidContext
import com.iseo.v364droidsdk.service.mobilecredentialservice.DroidMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import data.common.preferences.Preferences
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADMIN
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.CommonValues.Companion.INSTALLER
import data.utils.android.applications.ServiceProvider
import data.utils.android.database.Constant
import data.utils.android.interfaces.ClickFragments
import data.utils.android.settings.SharedPreferenceUtils
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import feature.dfu.view.nearbyDfuActivity.DownloadStatus
import feature.properties.PropertiesFragment
import feature.routines.RoutinesFragment
import feature.security.BioMatric
import feature.security.SecurityClass
import feature.settings.common.MoreSettingsFragment
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import presentation.home.welcome.feature.WelcomeScreenFragment
import java.io.IOException
import java.util.Locale

class DashboardActivity :
    SplashCompact(),
    ClickFragments,
    BottomNavigationView.OnNavigationItemSelectedListener,
    BottomNavigationView.OnNavigationItemReselectedListener {

    val sharePrefs: SharedPreferenceUtils by lazy { SharedPreferenceUtils.getInstance(this) }
    var position = 0
    private var mobileCredentialService: IMobileCredentialService? = null
    var runSplash = false
    private var homeFragment: HomeFragment? = null
    private var security: SecurityClass? = null
    private lateinit var scanManagerService: IScanManagerService
    lateinit var mViewModel: HomeViewModel
    private lateinit var binding: ActivityDashboardBinding
    var totalNotifyLive = MutableLiveData<String>()

    private fun afterSplash() {
        if (!Preferences.isLoggedIn.get()) {
            CommonValues.loadFragment(
                WelcomeScreenFragment(),
                supportFragmentManager = supportFragmentManager,
                layoutId = R.id.frameContainer
            )
            sharePrefs.isApiWorked = false
        } else {
            apiTime()
            Constant.getDataBase(this)?.daoAccess()
        }
    }

    private fun apiTime() {
        if (CommonValues.isNetworkAvailable(this)) {
//            var time = TimeZone.getDefault().id
            var time = "UTC"
            val request = Request.Builder().url("https://www.timeapi.io/api/Time/current/zone?timeZone=$time").build()
            val client = OkHttpClient.Builder().build()
            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    e.printStackTrace()
                    lifecycleScope.launch(
                        CoroutineExceptionHandler { coroutineContext, throwable -> }
                    ) {
                        callFragment()
                        bottomNavSetUp()
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    if (response.isSuccessful) {
                        try {
                            val res = response.body!!.string()
                            val json = JSONObject(res)
                            CommonValues.serverDateTime = json.optString("dateTime")
                            lifecycleScope.launch(
                                CoroutineExceptionHandler { coroutineContext, throwable -> }
                            ) {
                                callFragment()
                                bottomNavSetUp()
                            }
                            Log.e("dateeee", CommonValues.serverDateTime)
                        } catch (e: Exception) {
                            e.printStackTrace()
                            lifecycleScope.launch(
                                CoroutineExceptionHandler { coroutineContext, throwable -> }
                            ) {
                                callFragment()
                                bottomNavSetUp()
                            }
                        }
                    }
                }
            })
        } else {
            lifecycleScope.launch(CoroutineExceptionHandler { coroutineContext, throwable -> }) {
                callFragment()
                bottomNavSetUp()
            }
        }
    }

//    @Deprecated("Deprecated in Java")
//    override fun onBackPressed() {
//
//    }

    override fun onResume() {
        super.onResume()
        mViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
        try {
            if (CommonValues.isNetworkAvailable(this)) {
                mViewModel.forceUpdateApi {
                    if (it.success) {
                        if (it.force_update) {
                            update()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun update() {
        val appUpdateManager = AppUpdateManagerFactory.create(applicationContext)
        val updateInfo = appUpdateManager.appUpdateInfo
        updateInfo.addOnCompleteListener {
            if (it.isSuccessful) {
                val pInfo: PackageInfo = packageManager.getPackageInfo(packageName, 0)
                val version: Int = pInfo.versionCode

                val versionCode = it.result.availableVersionCode() // 50

                if (versionCode > version) {
                    val builder = AlertDialog.Builder(this)
                    builder.setTitle(getString(keyless.data.utils.android.R.string.update_available))
                    builder.setMessage(
                        getString(
                            keyless.data.utils.android.R.string
                                .a_new_version_of_the_keyless_is_available_please_update_to_new_version_now
                        )
                    )
                    builder.setPositiveButton(getString(keyless.feature.common.R.string.update)) { dialog, which ->
                        val intent = Intent(
                            Intent.ACTION_VIEW,
                            Uri.parse("market://details?id=com.keyless_dubai")
                        )
                        startActivity(intent)
                    }
                    builder.setCancelable(false)
                    builder.show()
                }
            }
        }
    }

    @MainThread
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        when (Preferences.userRole.get()) {
            GUEST -> {
                if (Preferences.isAdminLogin()) {
                    startActivity(
                        Intent(
                            this@DashboardActivity,
                            DashboardActivity::class.java
                        ).putExtra("runSplash", false)
                    )
                    Preferences.setAdminLogin(false)
                } else {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }

            else -> {
                if (position == 0) {
                    if (Preferences.isAdminLogin()) {
                        startActivity(
                            Intent(
                                this@DashboardActivity,
                                DashboardActivity::class.java
                            ).putExtra("runSplash", false)
                        )
                        Preferences.setAdminLogin(false)
                    } else {
                        finishAffinity()
                    }
                } else {
                    position = 0
                    binding.mBottomNavigation.menu.getItem(0).isChecked = true
                    CommonValues.loadFragment(
                        HomeFragment(),
                        supportFragmentManager = supportFragmentManager,
                        layoutId = R.id.frameContainer
                    )
                }
            }
        }
    }

    @SuppressLint("NewApi")
    override fun onCreate(savedInstanceState: Bundle?) {
        Log.e("//", "onCreatelanguage: " + sharePrefs.language)

//        val config = resources.configuration
//        val locale = Locale(sharePrefs.language)
//        Locale.setDefault(locale)
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1)
//            config.setLocale(locale)
//        else
//            config.locale = locale
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
//            createConfigurationContext(config)
//        resources.updateConfiguration(config, resources.displayMetrics)

        AppCompatDelegate.setApplicationLocales(
            LocaleListCompat.create(Locale.forLanguageTag(sharePrefs.language))
        )
        super.onCreate(savedInstanceState)

        binding = ActivityDashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)
        if (CommonValues.isRooted(this)) {
            defaultDialog(
                this,
                getString(keyless.data.utils.android.R.string.not_allowed_on_rooted_device),
                object : OnActionOK {
                    override fun onClickData() {
                        finish()
                    }
                }
            )
            return
        }

        security = SecurityClass(this)
        runSplash = intent?.getBooleanExtra("runSplash", true) ?: true
        val showSecurity = intent?.getBooleanExtra("showSecurity", true) ?: true

//        if (BuildConfig.DEBUG) {
// //            runSplash = false
//        }
        val screenLockEnabled = SharedPreferenceUtils.getInstance(this).isScreenLockEnabled
        if (screenLockEnabled && security?.isAnySecurity() == true && showSecurity) {
            lifecycleScope.launch {
                delay(1000)
                security?.startProcess(object : BioMatric {
                    override fun onUnlock() {
                        startActivity(
                            Intent(
                                this@DashboardActivity,
                                DashboardActivity::class.java
                            ).putExtra("showSecurity", false)
                        )
                        overridePendingTransition(0, 0)
                        finishAffinity()
                    }

                    override fun onError() {
                        finish()
                        overridePendingTransition(0, 0)
                    }
                })
            }
        } else {
            afterUnlock()
        }
        binding.returnToAdmin.isVisible = Preferences.isAdminLogin()
        clickListeners()

        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (
                    object :
                        OnBackPressedCallback(true),
                        OnBackInvokedCallback {
                        override fun handleOnBackPressed() {
                        }

                        override fun onBackInvoked() {
                            when (Preferences.userRole.get()) {
                                GUEST -> {
                                    if (Preferences.isAdminLogin()) {
                                        startActivity(
                                            Intent(
                                                this@DashboardActivity,
                                                DashboardActivity::class.java
                                            ).putExtra("runSplash", false)
                                        )
                                        Preferences.setAdminLogin(false)
                                    } else {
                                        setResult(Activity.RESULT_OK)
                                        finish()
                                    }
                                }

                                else -> {
                                    if (position == 0) {
                                        if (Preferences.isAdminLogin()) {
                                            startActivity(
                                                Intent(
                                                    this@DashboardActivity,
                                                    DashboardActivity::class.java
                                                ).putExtra("runSplash", false)
                                            )
                                            Preferences.setAdminLogin(false)
                                        } else {
                                            finishAffinity()
                                        }
                                    } else {
                                        position = 0
                                        binding.mBottomNavigation.menu.getItem(0).isChecked = true
                                        CommonValues.loadFragment(
                                            HomeFragment(),
                                            supportFragmentManager = supportFragmentManager,
                                            layoutId = R.id.frameContainer
                                        )
                                    }
                                }
                            }
                        }
                    }
                    )
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun callApiNotification1(event: DownloadStatus) {
        if (CommonValues.isNetworkAvailable(this)) {
            mViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
            mViewModel?.hitLocksApi(sharePrefs.token, sharePrefs.uuid)
            mViewModel?.getResponseMain?.observe(this) {
                sharePrefs.totalUnreadNotification = it.totalUnreadNotification
                totalNotifyLive.value = sharePrefs.totalUnreadNotification.toString()
            }
        }
    }

    override fun onStop() {
        super.onStop()
        EventBus.getDefault().unregister(this)
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

//
//    private var receiver = object : BroadcastReceiver() {
//        override fun onReceive(p0: Context?, br: Intent?) {
//            br?.let {
//                homeFragment?.callApiNotification(this@DashboardActivity,sharePrefs)
//            }
//        }
//    }
//
//
//    override fun onStart() {
//        super.onStart()
//        val filter = IntentFilter()
//        filter.addAction(CommonValues.NOTIFICATION_UPDATE)
//        registerReceiver(receiver, filter)
//    }
//
//    override fun onDestroy() {
//        super.onDestroy()
//        unregisterReceiver(receiver)
//    }

    private fun appInitialization() {
        val intent = Intent(this, DashboardActivity::class.java)
        intent.addFlags(FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        Runtime.getRuntime().exit(0)
    }

    private fun clickListeners() {
        binding.returnToAdmin.setOnClickListener {
            finishAffinity()
            startActivity(
                Intent(
                    this,
                    DashboardActivity::class.java
                ).putExtra("runSplash", false)
            )
            Preferences.setAdminLogin(false)
        }
    }

    private fun afterUnlock() {
        if (runSplash) {
            binding.vidView.surfaceTextureListener = this@DashboardActivity
            val videoProportion: Float = 1.8f
            val screenWidth = resources.displayMetrics.widthPixels
            val screenHeight = resources.displayMetrics.heightPixels
            val screenProportion = screenHeight.toFloat() / screenWidth.toFloat()
            val lp: ViewGroup.LayoutParams = binding.vidView.layoutParams

            if (videoProportion < screenProportion) {
                lp.height = screenHeight
                lp.width = (screenHeight.toFloat() / videoProportion).toInt()
            } else {
                lp.width = screenWidth
                lp.height = (screenWidth.toFloat() * videoProportion).toInt()
            }
            binding.vidView.layoutParams = lp
        }
        sharePrefs.isApiDone = false
        getToken()
        initGetUID()
        if (runSplash) {
            if (sharePrefs.videoOn) {
                binding.vidView.isVisible = false
                binding.splashNormal.isVisible = true
                Handler(Looper.getMainLooper()).postDelayed({
                    binding.vidView.isVisible = false
                    binding.splashNormal.isVisible = false
                    afterSplash()
//                    delay(580)
                    homeFragment?.callApi()
                }, 500)
            } else {
                lifecycleScope.launch {
                    while (mediaPlayer == null) {
                        delay(500)
                        loadVideo()
                        sharePrefs.videoOn = true
                    }
                }
            }
        } else {
            afterSplash()
            Handler(Looper.getMainLooper()).postDelayed({
                homeFragment?.callApi()
            }, 50)
        }
    }

    private fun initGetUID() {
        try {
            AndroidContext.setContext(this)
            scanManagerService = ServiceProvider.scanManagerService
            mobileCredentialService = ServiceProvider.mobileCredentialService

            sharePrefs.uuid = (mobileCredentialService as? DroidMobileCredentialService)
                ?.userIdentity
                ?.mobileCredentialUUID
                .toString()

            Preferences.uuid.set(
                (mobileCredentialService as? DroidMobileCredentialService)
                    ?.userIdentity
                    ?.mobileCredentialUUID
                    .toString()
            )

            Log.d(
                "Hello check",
                "${(mobileCredentialService as? DroidMobileCredentialService)?.userIdentity?.mobileCredentialUUID}"
            )
            Firebase.crashlytics.log("crashing")
        } catch (e: Exception) {
            appInitialization()
        }
    }

    private fun isAlreadyLogin(): Boolean {
        if (Preferences.isLoggedInAnotherDevice.get()) {
            defaultDialog(
                this,
                CommonValues.ALREADY_LOGIN(this),
                object : OnActionOK {
                    override fun onClickData() {
                        clicksIntents(1)
                    }
                }
            )
            return true
        }
        return false
    }

    private fun getToken() {
        FirebaseApp.initializeApp(this)
        FirebaseMessaging.getInstance().token.addOnCompleteListener(
            OnCompleteListener { task ->
                if (!task.isSuccessful) {
                    return@OnCompleteListener
                }
                val token = task.result
                val msg = token.toString()
                Preferences.deviceFCMToken.set(msg)
                Log.d("TAG", msg)
            }
        )
    }

    private fun askPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.CAMERA
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.CAMERA
                ),
                50
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    grantResults.isNotEmpty() &&
                        grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[2] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[3] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[4] == PackageManager.PERMISSION_GRANTED
                } else {
                    grantResults.isNotEmpty() &&
                        grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                        grantResults[2] == PackageManager.PERMISSION_GRANTED
                }

            ) {
                if (runSplash) {
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadVideo()
                    }, 500)
                } else {
                    afterSplash()
                    Handler(Looper.getMainLooper()).postDelayed({
                        homeFragment?.callApi()
                    }, 50)
                }
            } else {
                showDialogForPermissions()
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(keyless.data.utils.android.R.string.please_allow_permissions_to_continue),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermissions()
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun bottomNavSetUp() {
        binding.mBottomNavigation.setOnNavigationItemSelectedListener(this)
        binding.mBottomNavigation.setOnNavigationItemReselectedListener(this)
        binding.mBottomNavigation.isVisible =
            !(Preferences.userRole.get() == GUEST || Preferences.userRole.get() == INSTALLER)

        if (Preferences.userRole.get() == ADMIN) {
            binding.mBottomNavigation.menu.findItem(R.id.properties).isVisible = false
            binding.mBottomNavigation.menu.findItem(R.id.routines).isVisible = false
            binding.mBottomNavigation.menu.findItem(R.id.home).title = getString(
                keyless.data.utils.android.R.string.text_locks
            )
            binding.mBottomNavigation.menu.findItem(R.id.home).icon = getDrawable(
                R.drawable.admin_lock
            )
        } else {
            binding.mBottomNavigation.menu.findItem(R.id.properties).isVisible = true
            binding.mBottomNavigation.menu.findItem(R.id.routines).isVisible = true
            binding.mBottomNavigation.menu.findItem(R.id.home).title = getString(
                keyless.data.utils.android.R.string.home
            )
        }
    }

    private fun callFragment() {
        try {
            homeFragment = HomeFragment()
            CommonValues.loadFragment(
                HomeFragment(),
                supportFragmentManager = supportFragmentManager,
                layoutId = R.id.frameContainer
            )
        } catch (e: java.lang.Exception) {
            Log.e("//", "callFragment: " + e.message)
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.home -> {
                if (!isAlreadyLogin()) {
                    position = 0
                    binding.mBottomNavigation.menu.getItem(0).isChecked = true
                    CommonValues.loadFragment(
                        HomeFragment(),
                        supportFragmentManager = supportFragmentManager,
                        layoutId = R.id.frameContainer
                    )
                }
            }

            R.id.properties -> {
                if (!isAlreadyLogin()) {
                    position = 1
                    binding.mBottomNavigation.menu.getItem(1).isChecked = true
                    CommonValues.loadFragment(
                        PropertiesFragment(),
                        supportFragmentManager = supportFragmentManager,
                        layoutId = R.id.frameContainer
                    )
                }
            }

            R.id.routines -> {
                if (!isAlreadyLogin()) {
                    position = 2
                    binding.mBottomNavigation.menu.getItem(2).isChecked = true
                    val bundle = Bundle()
                    bundle.putString("whichScreen", "")
                    CommonValues.loadFragment(
                        RoutinesFragment(),
                        supportFragmentManager = supportFragmentManager,
                        layoutId = R.id.frameContainer
                    )
                }
            }

            R.id.moreSettings -> {
                if (!isAlreadyLogin()) {
                    position = 3
                    binding.mBottomNavigation.menu.getItem(3).isChecked = true
                    CommonValues.loadFragment(
                        MoreSettingsFragment(),
                        supportFragmentManager = supportFragmentManager,
                        layoutId = R.id.frameContainer
                    )
                }
            }
        }
        return false
    }

    override fun onNavigationItemReselected(item: MenuItem) {
//        when (item.itemId) {
//            R.id.home -> {
//                if (!isAlreadyLogin()) {
//                    position = 0
//                    mBottomNavigation.menu.getItem(0).isChecked = true
//                    val bundle = Bundle()
//                    bundle.putString("reselected", "1")
//                    CommonValues.loadFragment(
//                        HomeFragment(),bundle,
//                        supportFragmentManager = supportFragmentManager
//                    )
//
//                }
//            }
//        }
    }

    override fun clicksIntents(i: Int) {
        if (i == 3) {
            if (!isAlreadyLogin()) {
                position = 0
                binding.mBottomNavigation.menu.getItem(0).isChecked = true
                homeFragment = HomeFragment()
                val bundle = Bundle()
                bundle.putString("openLockScreen", "1")
                CommonValues.loadFragment(
                    HomeFragment(),
                    bundle,
                    supportFragmentManager = supportFragmentManager,
                    layoutId = R.id.frameContainer
                )
//               homeFragment!!.openAddLock()
            }
        } else if (i == 15) {
            finish()
        } else if (i == 4) {
            if (!isAlreadyLogin()) {
                position = 0
                binding.mBottomNavigation.menu.getItem(0).isChecked = true
                homeFragment = HomeFragment()
                val bundle = Bundle()
                bundle.putString("openLockScreen", "2")
                CommonValues.loadFragment(
                    HomeFragment(),
                    bundle,
                    supportFragmentManager = supportFragmentManager,
                    layoutId = R.id.frameContainer
                )
//               homeFragment!!.openAddLock()
            }
        } else if (i == 2) {
            if (!isAlreadyLogin()) {
                position = 1
                binding.mBottomNavigation.menu.getItem(1).isChecked = true
                CommonValues.loadFragment(
                    PropertiesFragment(),
                    supportFragmentManager = supportFragmentManager,
                    layoutId = R.id.frameContainer
                )
            }
        } else if (i == 4) {
            if (!isAlreadyLogin()) {
                position = 3
                binding.mBottomNavigation.menu.getItem(3).isChecked = true
                CommonValues.loadFragment(
                    MoreSettingsFragment(),
                    supportFragmentManager = supportFragmentManager,
                    layoutId = R.id.frameContainer
                )
            }

            bottomNavSetUp()
        } else {
            Preferences.clear()
            sharePrefs.deletePreferences()
            startActivity(
                Intent(this, DashboardActivity::class.java).putExtra(
                    "runSplash",
                    false
                )
            )
            finish()
        }
    }

    override fun loadFragmentInBack(mediaPlayer: MediaPlayer) {
        lifecycleScope.launch {
            delay(1500)
            afterSplash()

            val time = mediaPlayer.duration - 900
            while (time >= mediaPlayer.currentPosition) {
                delay(200)
            }
            binding.vidView.animate().setDuration(800).alpha(0.0f)
            delay(580)
            homeFragment?.callApi()
        }
    }
}