package com.app.keyless.home

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import data.common.preferences.Preferences
import data.keyless.authentication.models.SendOtpResponse
import data.network.android.AdminLocksResponse
import data.network.android.ApiUtils
import data.network.android.LocksListResponse
import data.network.android.ModelCardRayonics
import data.network.android.ModelMessage
import data.network.android.models.GetAssignedData
import data.network.android.models.GetSizeColorModel
import data.network.android.models.HistoryLockResponse
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.ModelCheckInPm
import data.network.android.models.ModelConfigureCard
import data.network.android.models.ModelInstallerMaintenance
import data.network.android.models.ModelPlatformDetails
import data.network.android.models.old
import data.utils.android.settings.SharedPreferenceUtils
import domain.home.models.ScreenEvent
import feature.common.dialogs.ProgressDialogUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import presentation.common.domain.repositories.ErrorMessageHandler

class HomeViewModel : ViewModel(), KoinComponent {

    private val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress
    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    private val _getResponseLogout = MutableLiveData<JsonObject>()
    val getResponseLogout: LiveData<JsonObject> = _getResponseLogout
    var _getResponseHomeLock = MutableLiveData<List<LocksListResponse.LocksModel>>()
    var _getResponseMain = MutableLiveData<LocksListResponse>()
    private var _getResponseHomeProperties =
        MutableLiveData<List<LocksListResponse.PropertiesMainModel>>()

    val getResponsePropertiesLock: LiveData<List<LocksListResponse.PropertiesMainModel>> =
        _getResponseHomeProperties

    val getResponseHomeLock: LiveData<List<LocksListResponse.LocksModel>> =
        _getResponseHomeLock

    val getResponseMain: LiveData<LocksListResponse> =
        _getResponseMain

    private var _errorLogout = MutableLiveData<LocksListResponse>()
    val errorLogout: LiveData<LocksListResponse> = _errorLogout
    private var _getResponseAdminLocks = MutableLiveData<AdminLocksResponse>()
    val getResponseAdminLocks: LiveData<AdminLocksResponse> = _getResponseAdminLocks
    private var _deleteLock = MutableLiveData<SendOtpResponse>()
    val deleteLock: LiveData<SendOtpResponse> = _deleteLock

    private var _getResponseInstaller = MutableLiveData<ModelAdminInstaller>()
    val getResponseInstaller: LiveData<ModelAdminInstaller> = _getResponseInstaller

    private val viewModel: domain.home.ViewModel by inject()

    fun logoutAccount(token: String, context: Context) {
        _progress.value = false
        _progress.value = true
        viewModelScope.launch(Dispatchers.IO) {
            val pref = SharedPreferenceUtils.getInstance(context)
            val history = pref.getLockHistoryBackup()
            val jsonObject = JsonObject()
            if (history.isNotEmpty()) {
                ApiUtils.updateLockLogs(token, history, {
                    pref.clearLockHistory()
                    logout(token)
                }) {
                    _progress.value = false
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }
            } else {
                logout(token)
            }
        }
    }

    private suspend fun logout(token: String) {
        ApiUtils.getLogoutAccount(token, {
            it as JsonObject
            Preferences.isLoggedIn.set(false)
            _progress.value = false
            _getResponseLogout.value = it
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun updateLogs(context: Context) {
        viewModelScope.launch(Dispatchers.IO) {
            val pref = SharedPreferenceUtils.getInstance(context)
            val lockUpdateHistory = pref.getLockHistoryBackup()

            ApiUtils.updateLockLogs(pref.token, lockUpdateHistory, {
                (it as HistoryLockResponse)
                _progress.value = false
                pref.setLockHistoryBackup(arrayListOf())
//                _getResponseLog.value = it
            }) {
//                _progress.value = false
//                _error.value = ApiErrorHandler.handleException(it)
            }
        }
    }

    fun hitLocksApi(token: String, uuid: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val event = ScreenEvent.Refresh(uuid, Preferences.isAdminLogin(), token) {
                    val response = it.old()

                    withContext(Dispatchers.Main) {
                        ProgressDialogUtils.getInstance().hideProgress()
                        if (response.success) {
                            Preferences.isPaidUser.set(response.is_paid)
                            _getResponseHomeLock.value = response.locks
                            _getResponseHomeProperties.value = response.properties
                            _getResponseMain.value = response
                            Preferences.lastOnlineLocksFetchDateTime.set(Clock.System.now().toString())
                        } else {
                            if (it.logout) {
                                _errorLogout.value = response
                            } else {
                                _error.value = it.message
                            }
                        }
                    }
                }
                viewModel.onEvent(event)
            } catch (ex: Exception) {
                withContext(Dispatchers.Main) {
                    _progress.value = false
                    _error.value = ErrorMessageHandler.handleException(ex)
                }
            }
        }
    }

    fun hitAdminLocksApi(token: String, progress: Boolean, page: Int, search: String) {
        if (!progress) {
            _progress.value = false
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.adminLocksChinese(token, page, search, {
                (it as AdminLocksResponse)
                _progress.value = false
                _getResponseAdminLocks.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun hitAdminDeleteLock(token: String, id: String) {
//        _progress.value = false
//        _progress.value = true
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.adminDeleteLocksChinese(
                token,
                id,
                {
                    (it as SendOtpResponse)
                    _progress.value = false
                    _deleteLock.value = it
                },
                {
                    _progress.value = false
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }
            )
        }
    }

    fun getSizeColorData(token: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.getSizeColorData(
            token,
            {
                (it as GetSizeColorModel)
                _progress.value = false
                emit(it)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }

    fun hitAdminEditLocksChinese(
        token: String,
        jsonObject: JsonObject
    ) = liveData {
        ApiUtils.adminCreateLocksChinese(
            token,
            jsonObject,
            {
                emit(it as SendOtpResponse)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }

    fun getCardSeries(token: String) = liveData {
        ApiUtils.getCardSeries(
            token,
            {
                emit(it as ModelCardRayonics)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }

    fun createCard(token: String, jsonObject: JsonObject) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.createCard(
            token,
            jsonObject,
            {
                (it as ModelConfigureCard)
                _progress.value = false
                emit(it)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }

    fun getInstallerList(
        token: String,
        page: Int,
        search: String,
        progressValue: Boolean,
        company_id: String
    ) {
        if (!progressValue) {
            _progress.value = false
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.getInstallerList(token, page, search, company_id, {
                (it as ModelAdminInstaller)
                _progress.value = false
                _getResponseInstaller.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun forceUpdateApi(callBack: (ModelMessage) -> Unit) {
        viewModelScope.launch(Dispatchers.Main) {
            ApiUtils.forceUpdateApi({
                (it as ModelMessage)
                _progress.value = false
                callBack.invoke(it)
            }, {
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun getMaintenanceList(
        token: String,
        page: Int,
        search: String,
        progressValue: Boolean,
        uuid: String
    ) = liveData {
        if (!progressValue) {
            _progress.value = false
            _progress.value = true
        }
        ApiUtils.getMaintenanceList(token, page, search, uuid, {
            (it as ModelInstallerMaintenance)
            _progress.value = false
            emit(it)
//                _getResponseInstaller.value = it
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun platformDetails(token: String) = liveData {
        ApiUtils.platformDetails(token, {
            (it as ModelPlatformDetails)
            _progress.value = false
            emit(it)
//                _getResponseInstaller.value = it
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun assign_lock_iseo(
        token: String,
        _id: String,
        jsonObject: JsonObject
    ) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.assign_lock_iseo_installer(token, _id, jsonObject, {
            _progress.value = false
            emit(it as GetAssignedData)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun sentErrorLog(token: String, jsonObject: JsonObject, callBack: (GetAssignedData) -> Unit) {
        _progress.value = false
        _progress.value = true
        CoroutineScope(Dispatchers.Main).launch { // can be other dispatcher
            kotlinx.coroutines.delay(2500)
            ApiUtils.sentErrorLog(token, jsonObject, {
                _progress.value = false
                callBack.invoke(it as GetAssignedData)
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun getCheckUser(token: String, jsonObject: JsonObject, isProgress: Boolean) = liveData {
        if (isProgress) {
            _progress.value = true
        }
        ApiUtils.getCheckUser(
            token,
            jsonObject,
            {
                (it as ModelMessage)
                _progress.value = false
                emit(it)
            },
            {
//            _progress.value = false
//            _error.value = ApiErrorHandler.handleException(it).toString()
            }
        )
    }

    fun claimKey(token: String, jsonObject: JsonObject) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.claimKey(token, jsonObject, {
            _progress.value = false
            emit(it as ModelMessage)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun getServicesApi(token: String, jsonObject: JsonObject) = liveData {
        ApiUtils.getServicesApi(token, jsonObject, {
            emit(it as ModelMessage)
        }, {
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun getGuestAssignments(token: String) = liveData {
        ApiUtils.getGuestAssignments(token, {
            emit(it as ModelCheckInPm)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }
}