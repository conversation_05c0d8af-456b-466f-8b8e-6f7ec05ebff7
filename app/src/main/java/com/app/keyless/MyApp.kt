package com.app.keyless

import android.app.Application
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import com.app.keyless.home.DashboardActivity
import core.monitoring.common.repository.Logger
import core.monitoring.firebase.FirebaseLogger
import dagger.hilt.android.HiltAndroidApp
import data.common.preferences.Preferences
import feature.dashboard.unlock.LockDetailsActivity
import feature.injection.appModule
import feature.regula.ScanActivity
import org.koin.android.ext.android.inject
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import presentation.authentication.changepassword.changePasswordInjection
import presentation.authentication.login.feature.LoginFragment
import presentation.authentication.login.loginInjection
import presentation.authentication.signup.signupInjection
import presentation.authentication.verifyemail.verifyEmailInjection
import presentation.authentication.verifyotp.verifyOtpInjection
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.home.dashboard.dashboardInjection
import presentation.home.welcome.welcomeInjection

@HiltAndroidApp
class MyApp : Application() {
    private val logger by inject<Logger>()
    private val featureModules = listOf(
        loginInjection,
        verifyPhoneInjection,
        verifyOtpInjection,
        verifyEmailInjection,
        signupInjection,
        changePasswordInjection,

        dashboardInjection,
        welcomeInjection
    )

    override fun onCreate() {
        super.onCreate()
        Preferences.setDeviceType("android")
        feature.injection.database.applicationContext = this
        instance = this
        data.utils.android.applications.MyApp.instance = this
        data.utils.android.applications.MyApp.AppVersion = BuildConfig.VERSION_NAME
        feature.common.application.App.DASHBOARD_ACTIVITY_CLASS = DashboardActivity::class.java
        LoginFragment.DASHBOARD_ACTIVITY_CLASS = DashboardActivity::class.java
        feature.common.application.App.REGULA_SCANNER_ACTIVITY_CLASS = ScanActivity::class.java
        feature.common.application.App.LOCK_DETAILS_ACTIVITY_CLASS = LockDetailsActivity::class.java
        createServiceChannel(this)

        startKoin {
            androidContext(this@MyApp)
            this.androidLogger(Level.INFO)

            modules(appModule)
            modules(featureModules)
        }

        (logger as? FirebaseLogger)?.initFirebase()
    }

    companion object {
        const val DFU_CHANNEL = "DFU_CHANNEL"
        lateinit var instance: MyApp
            private set
    }

    private fun createServiceChannel(context: Context) {
        val channel = NotificationChannel(
            DFU_CHANNEL,
            context.getString(keyless.data.utils.android.R.string.dfu_channel_name),
            NotificationManager.IMPORTANCE_LOW
        )
        channel.description = context.getString(keyless.data.utils.android.R.string.dfu_channel_description)
        channel.setShowBadge(false)
        channel.lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        val notificationManager = context.getSystemService(Application.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }
}