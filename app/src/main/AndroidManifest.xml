<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="33"
        tools:replace="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="33"
        tools:replace="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:remove="android:maxSdkVersion" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:remove="android:maxSdkVersion" />
    <!-- <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />

    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />

    <queries>
        <package android:name="com.whatsapp" />
    </queries>

    <application
        android:name=".MyApp"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:localeConfig="@xml/locales_config"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:fullBackupContent,allowBackup"
        tools:targetApi="31">

        <activity
                android:name="feature.notifications.NotificationActivity"
                android:screenOrientation="portrait"
            android:exported="false" />

        <receiver
            android:name="feature.dfu.view.nearbyDfuActivity.FirmwareDownloadReceiver"
            android:enabled="true"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
            </intent-filter>
        </receiver>

        <activity
            android:name="feature.settings.checkin.CheckInPMActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="feature.settings.checkin.CheckInStartActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name="feature.regula.DocResultActivity"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name="feature.regula.DocScanActivity"
             android:screenOrientation="portrait"
                android:exported="false" />
        <activity
            android:name="feature.regula.FaceCameraActivity"
            android:screenOrientation="portrait"
            android:exported="true" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource" />
        </provider>

        <service
                android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
                android:enabled="false"
                android:exported="false">
            <meta-data
                    android:name="autoStoreLocales"
                    android:value="true" />
        </service>

        <service
            android:name="feature.dfu.repo.DeviceFirmUpdateService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="feature.dfu.view.DFUActivity"
            android:exported="false" /> <!-- <activity -->
        <!-- android:name=".dfuMain.view.DFUActivity" -->
        <!-- android:exported="false" /> -->
        <activity
            android:name="feature.regula.ScanActivity"
            android:screenOrientation="portrait"
            android:exported="true" />
        <activity
            android:name="feature.regula.ResultsActivity"
                android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name="feature.dfu.view.nearbyDfuActivity.NearByDfuDeviceActivity"
                android:screenOrientation="portrait"
            android:exported="false" /> <!-- <activity -->
        <activity
            android:name="feature.settings.admin.SelectUserAdminActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="feature.settings.aboutus.AboutUsNewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="feature.settings.support.diagnostics.DiagnosticsScreen"
            android:exported="false"
            android:screenOrientation="portrait" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource" />
        </provider>

        <activity
                android:name="feature.settings.changelock.ChangeLockActivity"
                android:exported="false"
                android:screenOrientation="portrait" />
        <activity
                android:name="feature.settings.maintenance.MaintenanceActivity"
                android:exported="false"
                android:screenOrientation="portrait" />
        <activity
            android:name="feature.home.installer.InstallerLockActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.settings.cards.CardsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.settings.cards.SelectRayonicsLock"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.settings.cards.ConfiguredCardsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.home.admin.SelectLockForCardActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.settings.profile.VerifyOtpActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.profile.ChangeEmailActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <receiver
            android:name=".alarm.ReminderNotification"
            android:enabled="true" />

        <activity
            android:name="feature.settings.support.nearby.NearByDeviceActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="presentation.common.feature.components.WebActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.company.staff.AddStaffActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.common.SupportActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.routines.SelectRoutineActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.company.staff.ManageStaffActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.company.CompanyProfileActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.settings.profile.ProfileActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.routines.AddRoutineActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.masterkey.AddMasterKeyActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.home.admin.AddAdminLockActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.properties.PropertyDetailActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.dashboard.shareaccess.ShareAccessMainActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.properties.SearchLocationActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />

        <activity
            android:name="feature.properties.AddProperty"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.dashboard.lockinfo.EditLockActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="feature.dashboard.shareaccess.AddUserActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.dashboard.lockinfo.LockInfoActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.dashboard.locksettings.LockSettingsActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.dashboard.lockhistory.LockHistoryActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="feature.dashboard.unlock.LockDetailsActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
        <activity
                android:name=".home.ExampleActivity"
                android:exported="true" >
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.MAIN" />-->

<!--                <category android:name="android.intent.category.LAUNCHER" />-->
<!--            </intent-filter>-->
        </activity>
        <activity
            android:name=".home.DashboardActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Transparent"
            android:windowSoftInputMode="adjustPan">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="feature.pm.addlock.AddLockMainActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
                android:name="feature.regula.refactored.RegulaActivity"
                android:configChanges="orientation|keyboardHidden"
                android:exported="false"
                android:screenOrientation="portrait">
            <meta-data
                    android:name="android.app.lib_name"
                    android:value="" />
        </activity>
        <service
            android:name=".common.MyFirebaseMessagingService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

</manifest>