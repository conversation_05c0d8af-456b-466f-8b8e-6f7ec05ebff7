apply(from = "mobilelogic/submodule.settings.gradle.kts")

pluginManagement {
    includeBuild("keyless-build-logic")
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven("https://jitpack.io")
        jcenter()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven("https://jitpack.io")
        maven("https://maven.regulaforensics.com/RegulaDocumentReader")
        maven("https://maven.fabric.io/public")
        jcenter()
    }
    versionCatalogs {
        create("keyless") {
            from(files("gradle/keyless.versions.toml"))
        }
    }
    versionCatalogs {
        create("libs") {
            from(files("mobilelogic/gradle/libs.versions.toml"))
        }
    }
}
rootProject.name = "Keyless"
include(":app")
include(":v364Sdk")
include(":rayonicsSdk")
// include(":ttlockSdk")
// include(":mstblelib")

private data class ProjectDeclaration(val name: String, val path: String = name.replace("-", "/"))

private val projects = listOf(
    ProjectDeclaration("data-common"),
    ProjectDeclaration("data-utils-android"),
    ProjectDeclaration("data-error"),
    ProjectDeclaration("data-company"),
    ProjectDeclaration("data-encryption"),
    ProjectDeclaration("data-lock-common"),
    ProjectDeclaration("data-network-android"),
    ProjectDeclaration("data-test"),
    ProjectDeclaration("data-database"),
    ProjectDeclaration("data-user-account"),
    ProjectDeclaration("data-user-guest"),
    ProjectDeclaration("data-user-home"),
    ProjectDeclaration("data-keyless"),

    ProjectDeclaration("domain-account-email"),
    ProjectDeclaration("domain-common"),
    ProjectDeclaration("domain-home"),
    ProjectDeclaration("domain-regula"),
    ProjectDeclaration("domain-settings-checkin"),
    ProjectDeclaration("domain-settings-company"),
    ProjectDeclaration("domain-settings-company-staff"),
    ProjectDeclaration("domain-settings-profile"),
    ProjectDeclaration("domain-settings-support"),

    ProjectDeclaration("feature-pm-addlock"),
    ProjectDeclaration("feature-common"),
    ProjectDeclaration("feature-dashboard"),
    ProjectDeclaration("feature-dfu"),
    ProjectDeclaration("feature-home-admin"),
    ProjectDeclaration("feature-home-installer"),
    ProjectDeclaration("feature-home-locks"),
    ProjectDeclaration("feature-home-properties"),
    ProjectDeclaration("feature-master-key", "feature/master-key"),
    ProjectDeclaration("feature-notifications"),
    ProjectDeclaration("feature-onboarding"),
    ProjectDeclaration("feature-properties"),
    ProjectDeclaration("feature-qrcode-scan"),
    ProjectDeclaration("feature-regula"),
    ProjectDeclaration("feature-regula-refactored"),
    ProjectDeclaration("feature-routines"),
    ProjectDeclaration("feature-security"),
    ProjectDeclaration("feature-settings"),
    ProjectDeclaration("feature-settings-checkin"),
    ProjectDeclaration("feature-settings-company-profile"),
    ProjectDeclaration("feature-settings-company-staff"),
    ProjectDeclaration("feature-settings-maintenance"),
    ProjectDeclaration("feature-settings-profile"),
    ProjectDeclaration("feature-settings-support"),
    ProjectDeclaration("feature-injection"),

    ProjectDeclaration("presentation-common"),
    ProjectDeclaration("presentation-authentication"),
    ProjectDeclaration("presentation-home"),
    ProjectDeclaration("presentation-test"),
)


projects.forEach { project ->
    include(":${project.name}")
    project(":${project.name}").projectDir = file(project.path)
}