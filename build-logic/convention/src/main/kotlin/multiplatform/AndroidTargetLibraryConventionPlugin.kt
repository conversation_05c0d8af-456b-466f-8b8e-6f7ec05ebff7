import com.android.build.gradle.LibraryExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.getByType
import org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension
import org.jetbrains.kotlin.gradle.plugin.KotlinDependencyHandler

@Suppress("unused")
class AndroidTargetLibraryConventionPlugin : Plugin<Project> {

    override fun apply(target: Project) = with(target) {
        val extension by lazy {
            extensions.getByType<KotlinMultiplatformExtension>()
        }
        val androidMain by lazy {
            extension.sourceSets.getByName("androidMain")
        }
        val unitTest by lazy {
            extension.sourceSets.getByName("androidUnitTest")
        }
        val instrumentedTest by lazy {
            extension.sourceSets.getByName("androidInstrumentedTest")
        }
        val library by lazy { extensions.getByType<LibraryExtension>() }

        pluginManager.apply("common.module")
        pluginManager.apply("com.android.library")

        extension.androidTarget {
            compilations.all {
                kotlinOptions {
                    jvmTarget = catalog.findVersion("javaCompatibility").get().displayName
                }
            }
        }

        androidMain.dependencies {
            implementation(catalog.findLibrary("androidx.annotation").get())
            implementation(catalog.findLibrary("androidx.appcompat").get())
            implementation(catalog.findLibrary("androidx.core.ktx").get())
        }

        unitTest.dependencies {
            implementation(catalog.findLibrary("kotlin.test").get())
        }
        instrumentedTest.dependencies {
            implementation(catalog.findLibrary("androidx.test.core.ktx").get())
            implementation(catalog.findLibrary("androidx.test.rules").get())
            implementation(catalog.findLibrary("androidx.test.runner").get())
            implementation(catalog.findLibrary("kotlin.test").get())
        }

        library.compileOptions {
            sourceCompatibility = JavaVersion.toVersion(catalog.findVersion("javaCompatibility").get().displayName)
            targetCompatibility = JavaVersion.toVersion(catalog.findVersion("javaCompatibility").get().displayName)
        }
        library.namespace = namespace()
        library.compileSdk = catalog.findVersion("androidSdkCompile").get().displayName.toInt()
        library.defaultConfig.testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        library.defaultConfig.minSdk = catalog.findVersion("androidSdkMin").get().displayName.toInt()
        library.defaultConfig.targetSdk = catalog.findVersion("androidSdkTarget").get().displayName.toInt()
    }
}

@Suppress("unused")
fun Project.androidDependencies(scope: KotlinDependencyHandler.() -> Unit) {
    val extension by lazy {
        extensions.getByType<KotlinMultiplatformExtension>()
    }
    val androidMain by lazy { extension.sourceSets.getByName("androidMain") }

    androidMain.dependencies { scope() }
}

@Suppress("unused")
fun Project.androidTestDependencies(scope: KotlinDependencyHandler.() -> Unit) {
    val extension by lazy {
        extensions.getByType<KotlinMultiplatformExtension>()
    }
    val androidTest by lazy { extension.sourceSets.getByName("androidUnitTest") }

    androidTest.dependencies { scope() }
}

@Suppress("unused")
fun Project.androidInstrumentedTestDependencies(
    scope: KotlinDependencyHandler.() -> Unit
) {
    val extension by lazy {
        extensions.getByType<KotlinMultiplatformExtension>()
    }
    val androidTest by lazy { extension.sourceSets.getByName("androidInstrumentedTest") }

    androidTest.dependencies { scope() }
}