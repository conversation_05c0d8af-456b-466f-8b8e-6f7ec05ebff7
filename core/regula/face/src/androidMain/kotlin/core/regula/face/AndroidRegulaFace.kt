package core.regula.face

import android.content.Context
import com.regula.facesdk.FaceSDK
import com.regula.facesdk.configuration.LivenessConfiguration
import com.regula.facesdk.enums.ImageType
import com.regula.facesdk.enums.LivenessErrorCode
import com.regula.facesdk.enums.LivenessStatus
import com.regula.facesdk.enums.LivenessType
import com.regula.facesdk.exception.InitException
import com.regula.facesdk.model.MatchFacesImage
import com.regula.facesdk.model.results.matchfaces.MatchFacesSimilarityThresholdSplit
import com.regula.facesdk.request.MatchFacesRequest
import core.common.error.toError
import core.common.platform.Platform
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import core.regula.common.models.RegulaImage
import core.regula.documents.FaceLiveNessResult
import core.regula.documents.MatchFacesResult
import core.regula.documents.RegulaFace
import kotlinx.coroutines.isActive
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class AndroidRegulaFace(
    private val context: Context,
    private val logger: Logger
) : RegulaFace {

    override suspend fun initialize() = logger.async {
        suspendCancellableCoroutine<Unit> { continuation ->
            FaceSDK.Instance().init(context) { status: Boolean, exception: InitException? ->
                if (status) {
                    continuation.resume(Unit)
                } else {
                    continuation.resumeWithException(exception ?: Exception("Unknown error"))
                }
            }
        }
    }

    override fun deInitialize(): Unit = logger.log {
        FaceSDK.Instance().deinit()
    }

    override suspend fun showScanner() = logger.async {
    }

    override fun stopLiveNess() {
        kotlin.runCatching { FaceSDK.Instance().stopLivenessProcessing(context) }
    }

    override suspend fun liveNessCheck() = logger.async {
        suspendCancellableCoroutine<FaceLiveNessResult?> { continuation ->
            val configuration: LivenessConfiguration = LivenessConfiguration.Builder()
                .setType(LivenessType.PASSIVE)
                .build()
            FaceSDK.Instance().startLiveness(context, configuration) { response ->
                if (response.liveness == LivenessStatus.PASSED) {
                    val result = response.bitmap?.let { FaceLiveNessResult(image = RegulaImage(it)) }
                    if (isActive) continuation.resume(result)
                    return@startLiveness
                } else if (
                    response.exception != null && response.exception!!.errorCode == LivenessErrorCode.CANCELLED
                ) {
                    if (isActive) continuation.resume(null)
                    return@startLiveness
                } else if (response.exception != null) {
                    response.exception!!.toError(Platform.executeLocation(), mapOf())
                    if (isActive) continuation.resumeWithException(response.exception!!)
                }

                if (isActive) continuation.resume(null)
            }
        }
    }

    override suspend fun matchFaces(
        firstImage: RegulaImage,
        secondImage: RegulaImage
    ): MatchFacesResult = logger.async {
        suspendCancellableCoroutine<MatchFacesResult> { continuation ->
            val request = MatchFacesRequest(
                listOf(
                    MatchFacesImage(firstImage.bitmap, ImageType.LIVE, true),
                    MatchFacesImage(secondImage.bitmap, ImageType.LIVE, true)
                )
            )

            FaceSDK.Instance().matchFaces(request) { response ->
                val split = MatchFacesSimilarityThresholdSplit(response.results, 0.75)
                if (split.matchedFaces.size > 0) {
                    val similarity = split.matchedFaces[0].similarity
                    val similarity2digit = String.format(Locale.ENGLISH, "%.2f", similarity).toDouble()

                    continuation.resume(MatchFacesResult(similarity2digit))
                } else {
                    continuation.resume(MatchFacesResult(0.0))
                }
            }
        }
    }
}