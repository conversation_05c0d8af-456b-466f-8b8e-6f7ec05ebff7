package core.regula.documents

import core.regula.common.models.RegulaImage

sealed interface DocumentScanResult {
    val firstName: String?
    val lastName: String?
    val dateOfBirth: String?
    val image: RegulaImage
    val expiryDate: String?
    val typeDescription: String
    val pageNumber: Int
    val portrait: RegulaImage?
    val documentNumber: String?
    val country: String?
    val textResults: Map<String, String>
    val identity: String?

    val fullName: String?
        get() = if (firstName != null && lastName != null) "$firstName $lastName" else null
}

data class IdScanResult(
    override val firstName: String?,
    override val lastName: String?,
    override val dateOfBirth: String?,
    override val image: RegulaImage,
    override val expiryDate: String?,
    override val typeDescription: String,
    override val pageNumber: Int,
    override val portrait: RegulaImage?,
    override val documentNumber: String?,
    override val country: String,
    override val textResults: Map<String, String>,
    override val identity: String?,
    val idType: Int
) : DocumentScanResult

data class PassportScanResult(
    override val firstName: String,
    override val lastName: String,
    override val dateOfBirth: String,
    override val image: RegulaImage,
    override val expiryDate: String,
    override val typeDescription: String,
    override val pageNumber: Int,
    override val portrait: RegulaImage,
    override val documentNumber: String,
    override val country: String?,
    override val textResults: Map<String, String>,
    override val identity: String?
) : DocumentScanResult