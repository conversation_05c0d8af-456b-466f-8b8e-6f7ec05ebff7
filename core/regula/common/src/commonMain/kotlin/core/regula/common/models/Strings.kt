package core.regula.common.models

import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

fun normalizeDate(date: String, futureOffset: Int): String {
    val parts = date
        .replace(".", "-")
        .replace("/", "-")
        .replace("\\", "-")
        .map { if (Character.isDigit(it)) Character.getNumericValue(it).digitToChar() else it }
        .joinToString("")
        .fixDate(futureOffset)
        .split("-")
        .map { if (it.length == 1) "0$it" else it }
        .toMutableList()

    if ((parts[1].toIntOrNull() ?: 0) > 12) {
        val temp = parts[1]
        parts[1] = parts[2]
        parts[2] = temp
    }

    return parts.joinToString("-")
}

private fun String.fixDate(futureOffset: Int): String {
    return when {
        this.matches("\\d{1,2}-\\d{1,2}-\\d{2}".toRegex()) -> {
            val parts = this.split("-")
            val prefix = if (
                parts[2].toInt() - futureOffset > Clock
                    .System
                    .now()
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                    .date
                    .year
                    .toString()
                    .takeLast(2)
                    .toInt()
            ) {
                "19"
            } else {
                "20"
            }
            "$prefix${parts[2]}-${parts[0]}-${parts[1]}"
        }

        this.matches("\\d{1,2}-\\d{1,2}-\\d{4}".toRegex()) -> {
            val parts = this.split("-")
            "${parts[2]}-${parts[1]}-${parts[0]}"
        }

        else -> this
    }
}