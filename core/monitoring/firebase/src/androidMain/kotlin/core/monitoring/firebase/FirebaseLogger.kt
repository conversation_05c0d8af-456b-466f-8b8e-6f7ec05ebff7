package core.monitoring.firebase

import android.content.Context
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import core.common.error.KError
import core.common.error.debugString
import core.monitoring.common.models.EventLog
import core.monitoring.common.models.ExecutionLog
import core.monitoring.common.models.Log
import core.monitoring.common.models.LogProperty
import core.monitoring.common.models.UserPropertyLog
import core.monitoring.common.repository.DebugLogger
import core.monitoring.common.repository.Logger
import kotlin.reflect.KClass

class FirebaseLogger(
    private val appContext: Context,
    override val isDebugging: Boolean,
    private val filteredLogs: List<KClass<out Log>>,
    private val onCrash: (FirebaseCrashlytics) -> Unit = {}
) : Logger, DebugLogger {

    private val analytics = Firebase.analytics
    private val crashlytics = Firebase.crashlytics
    private val defaultParamsBundle = Bundle()

    private var lastCustomKey = 0

    fun initFirebase() {
        FirebaseAnalytics.getInstance(appContext)
    }

    override fun setUserId(id: String) {
        debugInfo(id)
        analytics.setUserId(id)
        crashlytics.setUserId(id)
    }

    override fun userProperty(property: UserPropertyLog) {
        debugInfo(property.toString())

        analytics.setUserProperty(
            /* name = */ property.log.logName(),
            /* value = */ property.value.toString()
        )
    }

    override fun log(log: String) {
        log(ExecutionLog.Info(log))
    }

    override fun log(log: ExecutionLog) {
        debugInfo(log.toString())

        if (filteredLogs.contains(log::class)) return
        crashlytics.log("[${log.datetime}] ${log.log}")
    }

    override fun event(log: EventLog) {
        debugInfo(log.toString())

        analytics.logEvent(log.type.toString()) {
            log.params.forEach { property ->
                property.value.typeCallback(
                    onBoolean = {
                        param(property.log.logName(), it.toString())
                    },
                    onLong = {
                        param(property.log.logName(), it)
                    },
                    onDouble = {
                        param(property.log.logName(), it)
                    },
                    onFloat = {
                        param(property.log.logName(), it.toDouble())
                    },
                    onString = {
                        param(property.log.logName(), it)
                    },
                    onIterable = {
                        param(property.log.logName(), it.joinToString(",\n"))
                    }
                )
            }
            param("name_", log.log)
            param("datetime", log.datetime)
        }
    }

    override fun error(error: KError.Fatal) {
        debugError(error.toString())

        log(ExecutionLog.Info("[ERROR] ${error.debugString()}"))

        overrideCustomKeys()
        setCustomKeys(error)

        onCrash(crashlytics)
        crashlytics.recordException(error)
    }

    override fun defaultProperty(property: LogProperty) {
        debugInfo(property.toString())

        property.value.typeCallback(
            onBoolean = {
                defaultParamsBundle.putBoolean(property.log.logName(), it)
            },
            onLong = {
                defaultParamsBundle.putLong(property.log.logName(), it)
            },
            onDouble = {
                defaultParamsBundle.putDouble(property.log.logName(), it)
            },
            onFloat = {
                defaultParamsBundle.putFloat(property.log.logName(), it)
            },
            onString = {
                defaultParamsBundle.putString(property.log.logName(), it)
            },
            onIterable = {
                defaultParamsBundle.putString(
                    property.log.logName(),
                    it.joinToString(",\n")
                )
            }
        )
        analytics.setDefaultEventParameters(defaultParamsBundle)
    }

    override fun debugInfo(message: String) {
        if (isDebugging) {
            android.util.Log.d(
                "[LOG][DEBUG][INFO]",
                "$message\n--------------------"
            )
        }
    }

    override fun debugError(message: String) {
        if (isDebugging) {
            android.util.Log.e(
                "[LOG][DEBUG][ERROR]",
                "$message\n----------------------"
            )
        }
    }

    private fun Any.typeCallback(
        onBoolean: (Boolean) -> Unit,
        onLong: (Long) -> Unit,
        onDouble: (Double) -> Unit,
        onFloat: (Float) -> Unit,
        onIterable: (Iterable<*>) -> Unit,
        onString: (String) -> Unit
    ) {
        when (this) {
            is Boolean -> onBoolean(this)
            is Long -> onLong(this)
            is Double -> onDouble(this)
            is Float -> onFloat(this)
            is Iterable<*> -> onIterable(this)
            else -> onString(this.toString())
        }
    }

    private fun String.logName(): String {
        return this.lowercase().replace(" ", "_")
    }

    private fun overrideCustomKeys() {
        crashlytics.setCustomKey("error_id", "unset")
        crashlytics.setCustomKey("call_location", "unset")
        for (i in 0 until lastCustomKey) {
            crashlytics.setCustomKey("extra_detail_$i", "unset")
        }
    }

    private fun setCustomKeys(error: KError.Fatal) {
        val details = error.details.toList()
        crashlytics.setCustomKey("error_id", error.errorUid)
        crashlytics.setCustomKey("call_location", error.callLocation)
        for (i in details.indices) {
            crashlytics.setCustomKey("extra_detail_${i + 1}", details[i].second.toString())
            if (i + 1 > lastCustomKey) lastCustomKey = i + 1
        }
    }
}