package core.monitoring.common.repository

import core.common.error.KError
import core.common.error.toError
import core.common.platform.Platform
import core.monitoring.common.models.ExecutionLog
import core.monitoring.common.models.ExecutionScope
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.withContext
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

interface CrashesLogger {

    fun log(log: ExecutionLog)

    fun log(log: String)

    fun error(error: KError.Fatal)
}

inline fun <T> CrashesLogger.log(
    crossinline block: ExecutionScope<T>.() -> T
): T {
    val location = Platform.executeLocation()
    log(ExecutionLog.Start("$location: started"))
    val result = ExecutionScope<T>(location, EmptyCoroutineContext).execute {
        try {
            block()
        } catch (ex: Exception) {
            throw ex.toError(location, toDetails())
        }
    }

    log(ExecutionLog.End("$location: finished"))
    return result
}

suspend inline fun <T> CrashesLogger.async(
    context: CoroutineContext = EmptyCoroutineContext,
    crossinline block: suspend ExecutionScope<T>.() -> T
): T = withContext(currentCoroutineContext() + context) {
    val location = Platform.executeLocation()
    log(ExecutionLog.Start("$location: started"))

    val result = ExecutionScope<T>(location, currentCoroutineContext()).executeSuspend {
        try {
            block()
        } catch (ex: Exception) {
            throw ex.toError(location, toDetails())
        }
    }

    log(ExecutionLog.End("$location: finished"))
    return@withContext result
}