package core.monitoring.common.models

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

sealed class ExecutionLog(
    override val log: String,
    override val time: Long,
    override val datetime: String
) : Log {

    data class Start(val start: String, private val now: Instant = Clock.System.now()) : ExecutionLog(
        log = start,
        time = now.toEpochMilliseconds(),
        datetime = now.toString()
    )

    data class End(val end: String, private val now: Instant = Clock.System.now()) : ExecutionLog(
        log = end,
        time = now.toEpochMilliseconds(),
        datetime = now.toString()
    )

    data class Info(val info: String, private val now: Instant = Clock.System.now()) : ExecutionLog(
        log = info,
        time = now.toEpochMilliseconds(),
        datetime = now.toString()
    )
}