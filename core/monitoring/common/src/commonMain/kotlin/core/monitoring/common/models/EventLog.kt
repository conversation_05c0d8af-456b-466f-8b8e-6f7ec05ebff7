package core.monitoring.common.models

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

data class EventLog(
    override val log: String,
    val type: Type,
    val params: List<LogProperty>,
    private val now: Instant = Clock.System.now(),

    override val time: Long = now.toEpochMilliseconds(),
    override val datetime: String = now.toString()
) : Log {
    enum class Type {
        User {
            override fun toString(): String = "user"
        },
        Screen {
            override fun toString(): String = "screen"
        },
        Service {
            override fun toString(): String = "service"
        },
        Application {
            override fun toString(): String = "application"
        },
        HttpRequest {
            override fun toString(): String = "http_request"
        },
        HttpResponse {
            override fun toString(): String = "http_response"
        },
        Click {
            override fun toString(): String = "click"
        },
        Swipe {
            override fun toString(): String = "swipe"
        },
        Scroll {
            override fun toString(): String = "scroll"
        },
        Back {
            override fun toString(): String = "back"
        },
        Other {
            override fun toString(): String = "other"
        }
    }
}