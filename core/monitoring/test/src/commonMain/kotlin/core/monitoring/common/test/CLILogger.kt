package core.monitoring.common.test

import core.common.error.KError
import core.monitoring.common.models.EventLog
import core.monitoring.common.models.ExecutionLog
import core.monitoring.common.models.LogProperty
import core.monitoring.common.models.UserPropertyLog
import core.monitoring.common.repository.DebugLogger
import core.monitoring.common.repository.Logger
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

class CLILogger(
    internal val androidContext: Any
) : Logger, DebugLogger {

    private var userId = ""
    private val userProperties = ArrayList<UserPropertyLog>()
    private val events = ArrayList<EventLog>()
    private val defaultProperties = ArrayList<LogProperty>()
    private val executionLogs = ArrayList<ExecutionLog>()
    private val errorLogs = ArrayList<KError.Fatal>()

    override fun setUserId(id: String) {
        debugInfo(id)
        userId = id
        writeToFile(Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(), "User id: $id")
    }

    override fun userProperty(property: UserPropertyLog) {
        debugInfo(property.toString())
        userProperties.add(property)
        writeToFile(
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(),
            "User property: $property"
        )
    }

    override fun event(log: EventLog) {
        debugInfo(log.toString())
        events.add(log)
        writeToFile(
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(),
            "Event: $log"
        )
    }

    override fun defaultProperty(property: LogProperty) {
        debugInfo(property.toString())
        defaultProperties.add(property)
        writeToFile(
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(),
            "Default property: $property"
        )
    }

    override fun log(log: String) {
        log(ExecutionLog.Info(log))
    }

    override fun log(log: ExecutionLog) {
        debugInfo(log.toString())
        executionLogs.add(log)
        writeToFile(
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(),
            "Execution log: $log"
        )
    }

    override fun error(error: KError.Fatal) {
        debugInfo(error.toString())
        errorLogs.add(error)
        writeToFile(
            Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date.toString(),
            "Error: $error"
        )
    }

    override fun debugInfo(message: String) {
        println("[LOG][DEBUG][INFO] $message")
    }

    override fun debugError(message: String) {
        println("[LOG][DEBUG][ERROR] $message")
    }

    override val isDebugging: Boolean
        get() = true
}

internal expect fun CLILogger.writeToFile(date: String, text: String)