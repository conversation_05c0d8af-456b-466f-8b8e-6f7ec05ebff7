package core.permissions.manager

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.provider.Settings
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.LifecycleOwner
import core.common.lifecycle.BindLifecycle
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AndroidNetworkManager(
    override val context: Context,
    private val logger: Logger,
    private val dispatcher: CoroutineDispatcher = Dispatchers.Default
) : NetworkManager, BindLifecycle {

    private val enableStream = MutableSharedFlow<Boolean>()
    private lateinit var enableLauncher: ActivityResultLauncher<Intent>
    override var owner: LifecycleOwner? = null

    override suspend fun enableNetwork(): Boolean = logger.async {
        if (isNetworkEnabled()) return@async true

        if (!getDialog().first()) return@async false

        val panelIntent = Intent(Settings.Panel.ACTION_INTERNET_CONNECTIVITY)
        enableLauncher.launch(panelIntent)
        enableStream.take(1).last()
        delay(1000)

        return@async isNetworkEnabled()
    }

    override suspend fun isNetworkEnabled(): Boolean = logger.async {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        val capabilities = connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        if (capabilities != null) {
            when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    return@async true
                }

                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    return@async true
                }

                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                    return@async true
                }
            }
        }

        return@async false
    }

    override fun bind(owner: LifecycleOwner) = logger.log {
        super.bind(owner)
        enableLauncher = getLauncher(owner)
    }

    private fun getLauncher(owner: LifecycleOwner): ActivityResultLauncher<Intent> = logger.log {
        val caller = owner as ActivityResultCaller
        val callback = ActivityResultCallback<ActivityResult> { result ->
            CoroutineScope(dispatcher).launch {
                enableStream.emit(result.resultCode == Activity.RESULT_OK)
            }
        }

        return@log caller.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
            callback
        )
    }

    private suspend fun getDialog(): Flow<Boolean> {
        val flow = MutableSharedFlow<Boolean>()
        val scope = CoroutineScope(currentCoroutineContext())
        val dialog = AlertDialog.Builder(owner as Context)
            .setTitle("No internet connectivity")
            .setMessage("Would you like to enable internet connection?")
            .setPositiveButton(android.R.string.ok) { dialog, which -> scope.launch { flow.emit(true) } }
            .setNegativeButton(android.R.string.cancel) { _, _ -> scope.launch { flow.emit(false) } }
            .setIconAttribute(android.R.attr.alertDialogIcon)

        return flow
            .onStart { withContext(Dispatchers.Main) { dialog.show() } }
    }
}