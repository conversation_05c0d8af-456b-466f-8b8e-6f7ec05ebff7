package core.permissions.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.util.Log
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.LifecycleOwner
import core.common.lifecycle.BindLifecycle
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.permissions.manager.models.Permission
import core.permissions.manager.models.Response
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

class AndroidBluetoothManager(
    override val context: Context,
    private val permissions: PermissionsManager,
    private val logger: Logger
) : BluetoothManager, BindLifecycle {

    private val enableStream = MutableSharedFlow<Boolean>()
    private val scanStream = MutableSharedFlow<core.permissions.manager.models.BluetoothDevice>()
    private val bluetoothManager = context.getSystemService(android.bluetooth.BluetoothManager::class.java)
    private val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.adapter
    private val devices: ArrayList<core.permissions.manager.models.BluetoothDevice> = arrayListOf()
    private lateinit var enableLauncher: ActivityResultLauncher<Intent>
    override var owner: LifecycleOwner? = null

    override suspend fun isPermissionGranted(): Boolean = logger.async {
        return@async permissions.check(Permission.Bluetooth)
    }

    override suspend fun isConnectPermissionGranted(): Boolean = logger.async {
        return@async permissions.check(Permission.BluetoothConnect)
    }

    override suspend fun requestPermission(): Boolean = logger.async {
        return@async permissions.request(listOf(Permission.Bluetooth)).all { it.result == Response.Result.Granted }
    }

    override suspend fun requestConnectPermission(): Boolean = logger.async {
        return@async permissions.request(listOf(Permission.BluetoothConnect))
            .all { it.result == Response.Result.Granted }
    }

    override suspend fun isBluetoothEnabled(): Boolean = logger.async {
        val hasBluetooth = context.packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)
        val bluetoothAdapter = context.getSystemService(android.bluetooth.BluetoothManager::class.java).adapter

        return@async hasBluetooth && bluetoothAdapter?.isEnabled == true
    }

    override fun bind(owner: LifecycleOwner) {
        super.bind(owner)
        (permissions as? AndroidPermissionsManager)?.bind(owner)
        enableLauncher = getLauncher(owner)
    }

    @SuppressLint("MissingPermission")
    override val bluetoothStream: Flow<core.permissions.manager.models.BluetoothDevice> = scanStream
        .onStart {
            context.registerReceiver(receiver, IntentFilter(BluetoothDevice.ACTION_FOUND))
            bluetoothAdapter?.startDiscovery()
        }
        .onCompletion {
            context.unregisterReceiver(receiver)
            bluetoothAdapter?.cancelDiscovery()
        }

    @SuppressLint("MissingPermission")
    override val leBluetoothStream: Flow<core.permissions.manager.models.BluetoothDevice> = scanStream
        .onStart {
            bluetoothAdapter?.bluetoothLeScanner?.startScan(leScanCallback)
        }
        .onCompletion {
            bluetoothAdapter?.bluetoothLeScanner?.stopScan(leScanCallback)
        }

    override val allBluetoothStream: Flow<List<core.permissions.manager.models.BluetoothDevice>> = merge(
        bluetoothStream,
        leBluetoothStream
    ).map { update(it) }

    override suspend fun enableBluetooth(): Boolean = logger.async {
        if (!isPermissionGranted() && !requestPermission()) return@async false
        if (isBluetoothEnabled()) return@async true

        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        try {
            enableLauncher.launch(enableBtIntent)
        } catch (ex: Exception) {
            Log.e("PermissionHolder", ex.message, ex)
            return@async false
        }

        return@async enableStream.take(1).last()
    }

    override suspend fun enableConnectBluetooth(): Boolean = logger.async {
        if (!isConnectPermissionGranted() && !requestConnectPermission()) return@async false
        if (isBluetoothEnabled()) return@async true

        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)

        try {
            enableLauncher.launch(enableBtIntent)
        } catch (ex: Exception) {
            Log.e("PermissionHolder", ex.message, ex)
            return@async false
        }

        return@async enableStream.take(1).last()
    }

    private fun getLauncher(owner: LifecycleOwner): ActivityResultLauncher<Intent> {
        val caller = owner as ActivityResultCaller
        val callback = ActivityResultCallback<ActivityResult> { result ->
            CoroutineScope(Dispatchers.Default).launch {
                enableStream.emit(result.resultCode == Activity.RESULT_OK)
            }
        }

        return caller.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult(),
            callback
        )
    }

    private val receiver = object : BroadcastReceiver() {

        @SuppressLint("MissingPermission")
        override fun onReceive(context: Context, intent: Intent) {
            val action: String = intent.action ?: return
            when (action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val rssi = intent
                        .getShortExtra(BluetoothDevice.EXTRA_RSSI, Short.MIN_VALUE)
                        .toInt()

                    val device: BluetoothDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE) ?: return

                    CoroutineScope(Dispatchers.Default).launch {
                        scanStream.emit(
                            core.permissions.manager.models.BluetoothDevice(
                                name = device.name ?: "Bluetooth Device",
                                macAddress = device.address,
                                rssi = rssi
                            )
                        )
                    }
                }
            }
        }
    }

    private val leScanCallback: ScanCallback = object : ScanCallback() {
        @SuppressLint("MissingPermission")
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            if (result.device.name == null) return

            CoroutineScope(Dispatchers.Default).launch {
                scanStream.emit(
                    core.permissions.manager.models.BluetoothDevice(
                        name = result.device.name ?: "Bluetooth Device",
                        macAddress = result.device.address,
                        rssi = result.rssi
                    )
                )
            }
        }
    }

    private fun update(
        ble: core.permissions.manager.models.BluetoothDevice
    ): List<core.permissions.manager.models.BluetoothDevice> {
        val now = Clock.System.now().toEpochMilliseconds()
        devices.removeAll { now - it.lastSeen > 20_000 }
        val index = devices.indexOfFirst { it.macAddress == ble.macAddress }
        if (index == -1) devices.add(ble) else devices[index] = ble

        return devices
    }
}