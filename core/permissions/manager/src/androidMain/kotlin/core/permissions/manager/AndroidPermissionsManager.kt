package core.permissions.manager

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.os.PowerManager
import androidx.core.app.ActivityCompat
import androidx.lifecycle.LifecycleOwner
import core.common.lifecycle.BindLifecycle
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.permissions.manager.models.Permission
import core.permissions.manager.models.PermissionHolder
import core.permissions.manager.models.Response
import kotlinx.coroutines.currentCoroutineContext

class AndroidPermissionsManager(
    private val applicationContext: Context,
    private val logger: Logger
) : PermissionsManager, BindLifecycle {

    override var owner: LifecycleOwner? = null
    private val permissions = HashMap<Permission, PermissionHolder>()

    override suspend fun check(permission: Permission): Boolean = logger.async(currentCoroutineContext()) {
        return@async when (permission) {
            is Permission.Location -> {
                checkPermission(permission.android())
            }

            is Permission.LocationPrecise -> {
                checkPermission(permission.android())
            }

            is Permission.Camera -> {
                checkPermission(permission.android())
            }

            is Permission.BypassBatteryRestrictions -> {
                checkBatteryRestrictions()
            }

            is Permission.LocationSettingsOptimization -> {
                TODO()
            }

            is Permission.LocationBackground -> {
                val isAndroidQ = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q

                @SuppressLint("NewApi")
                if (isAndroidQ) checkPermission(permission.android()) else true
            }

            is Permission.Notification -> {
                val isTiramisu = Build.VERSION.SDK_INT >= 33

                @SuppressLint("NewApi")
                if (isTiramisu) checkPermission(permission.android()) else true
            }

            is Permission.Bluetooth -> {
                checkPermission(permission.android())
            }

            is Permission.BluetoothConnect -> {
                checkPermission(permission.android())
            }
        }
    }

    override suspend fun request(permissions: List<Permission>): List<Response> {
        return logger.async(currentCoroutineContext()) {
            permissions.map { permission -> onRequest(permission) }.flatten()
        }
    }

    private suspend fun onRequest(permission: Permission): List<Response> {
        return permissions[permission]?.request(this) ?: listOf()
    }

    private fun checkPermission(permissions: List<String>): Boolean {
        return permissions.all { permission ->
            ActivityCompat.checkSelfPermission(applicationContext, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun checkBatteryRestrictions(): Boolean {
        val pm = applicationContext.getSystemService(Context.POWER_SERVICE) as PowerManager
        val packageName = applicationContext.packageName

        return pm.isIgnoringBatteryOptimizations(packageName)
    }

    override fun onCreate(source: LifecycleOwner) {
        super.onCreate(source)
        bindPermissions(source)
    }

    private fun bindPermissions(owner: LifecycleOwner) {
        for (permission in Permission::class.sealedSubclasses) {
            val instance = permission.objectInstance ?: continue

            permissions[instance] = PermissionHolder(instance, owner)
        }
    }
}