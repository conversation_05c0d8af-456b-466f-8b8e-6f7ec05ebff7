package core.permissions.manager.models

sealed class Permission {
    object Location : Permission()

    object LocationPrecise : Permission()

    object LocationBackground : Permission()

    object LocationSettingsOptimization : Permission()

    object BypassBatteryRestrictions : Permission()

    object Bluetooth : Permission()

    object BluetoothConnect : Permission()

    object Camera : Permission()

    object Notification : Permission()
}