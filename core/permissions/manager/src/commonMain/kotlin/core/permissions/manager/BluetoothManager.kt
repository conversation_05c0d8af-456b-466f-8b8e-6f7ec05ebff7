package core.permissions.manager

import core.permissions.manager.models.BluetoothDevice
import kotlinx.coroutines.flow.Flow

interface BluetoothManager {

    val bluetoothStream: Flow<BluetoothDevice>

    val leBluetoothStream: Flow<BluetoothDevice>

    val allBluetoothStream: Flow<List<BluetoothDevice>>

    suspend fun isConnectPermissionGranted(): Boolean

    suspend fun isPermissionGranted(): Boolean

    suspend fun requestConnectPermission(): Boolean

    suspend fun requestPermission(): Boolean

    suspend fun isBluetoothEnabled(): Boolean

    suspend fun enableBluetooth(): Boolean

    suspend fun enableConnectBluetooth(): <PERSON><PERSON><PERSON>
}