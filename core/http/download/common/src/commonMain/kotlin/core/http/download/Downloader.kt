package core.http.download

import core.http.download.models.Download
import kotlinx.coroutines.flow.Flow

interface Downloader {

    fun enqueue(url: String, title: String, savePath: String): DownloadId

    fun cancel(id: DownloadId)

    fun listen(id: DownloadId): Flow<Download>

    fun getProgress(id: DownloadId): Double

    fun getDownloadedBytes(id: DownloadId): Long

    fun getTotalBytes(id: DownloadId): Long

    fun isInProgress(id: DownloadId): Boolean

    fun isPending(id: DownloadId): Boolean

    fun isFinished(id: DownloadId): <PERSON><PERSON>an
}