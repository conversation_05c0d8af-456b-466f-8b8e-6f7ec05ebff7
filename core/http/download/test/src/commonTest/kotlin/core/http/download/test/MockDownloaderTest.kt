package core.http.download.test

import core.http.download.models.DownloadState
import core.monitoring.common.test.CLILogger
import kotlin.test.Test
import kotlin.test.assertEquals

class MockDownloaderTest {

    private val manager = MockDownloader(CLILogger(Any()))

    @Test
    fun `is mock download manager working correct`() {
        val id = manager.enqueue("url", "title", "save")

        assertEquals(0, manager.getDownloadedBytes(id))
        assertEquals(0, manager.getTotalBytes(id))
        assertEquals(false, manager.isPending(id))

        manager.setupDownload(id, 5_000)
        assertEquals(5_000, manager.getTotalBytes(id))
        assertEquals(0, manager.getDownloadedBytes(id))
        assertEquals(true, manager.isPending(id))

        manager.setDownloadedBytes(id, 1_000)
        assertEquals(1_000, manager.getDownloadedBytes(id))
        assertEquals(20.0, manager.getProgress(id))

        manager.setStatus(id, DownloadState.InProgress)
        assertEquals(true, manager.isInProgress(id))
    }
}