package core.http.download.test

import core.http.download.DownloadId
import core.http.download.Downloader
import core.http.download.models.Download
import core.http.download.models.DownloadState
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class MockDownloader(private val logger: Logger) : Downloader {

    private val downloads = arrayListOf<DownloadId>()
    private val downloadedBytes = arrayListOf<Pair<DownloadId, Long>>()
    private val downloadTotals = arrayListOf<Pair<DownloadId, Long>>()
    private val downloadStatuses = arrayListOf<Pair<DownloadId, DownloadState>>()

    override fun enqueue(url: String, title: String, savePath: String): DownloadId {
        return logger.log { newDownload() }
    }

    override fun cancel(id: DownloadId) {
        downloads.remove(id)
        downloadedBytes.removeAll { it.first == id }
        downloadTotals.removeAll { it.first == id }
        downloadStatuses.removeAll { it.first == id }
    }

    override fun listen(id: DownloadId): Flow<Download> = flow {
        emit(getDownload(id, DownloadState.Pending))
        while (!isFinished(id)) {
            if (isInProgress(id)) {
                emit(getDownload(id, DownloadState.InProgress))
            }
        }
        emit(getDownload(id, DownloadState.Finished))
    }

    private fun getDownload(id: DownloadId, state: DownloadState): Download {
        val total = getTotalBytes(id)
        val downloaded = getDownloadedBytes(id)
        val progress = getProgress(id)

        return Download(state = state, downloadedBytes = downloaded, totalBytes = total, id = id, progress = progress)
    }

    override fun getProgress(id: DownloadId): Double = logger.log {
        return@log (getDownloadedBytes(id) * 100.0) / getTotalBytes(id)
    }

    override fun getDownloadedBytes(id: DownloadId): Long = logger.log {
        return@log downloadedBytes.find { it.first == id }?.second ?: 0
    }

    override fun getTotalBytes(id: DownloadId): Long = logger.log {
        return@log downloadTotals.find { it.first == id }?.second ?: 0
    }

    override fun isInProgress(id: DownloadId): Boolean = logger.log {
        val status = downloadStatuses.find { it.first == id }?.second ?: 0

        return@log status == DownloadState.InProgress
    }

    override fun isPending(id: DownloadId): Boolean = logger.log {
        val status = downloadStatuses.find { it.first == id }?.second ?: 0

        return@log status == DownloadState.Pending
    }

    override fun isFinished(id: DownloadId): Boolean = logger.log {
        val status = downloadStatuses.find { it.first == id }?.second ?: 0

        return@log status == DownloadState.Finished
    }

    fun setupDownload(id: DownloadId, totalSize: Long) {
        downloads.add(id)
        downloadedBytes.add(id to 0)
        downloadTotals.add(id to totalSize)
        downloadStatuses.add(id to DownloadState.Pending)
    }

    fun setDownloadedBytes(id: DownloadId, bytes: Long) {
        val index = downloadedBytes.indexOfFirst { it.first == id }

        downloadedBytes[index] = id to bytes
    }

    fun setStatus(id: DownloadId, status: DownloadState) {
        val index = downloadStatuses.indexOfFirst { it.first == id }

        downloadStatuses[index] = id to status
    }

    private fun newDownload(): DownloadId {
        val newId = getRandomId()

        if (downloads.contains(newId)) {
            newDownload()
        }

        return newId
    }
}

internal expect fun getRandomId(): DownloadId