package core.http.client.test

import core.http.client.HttpClient
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.http.client.ktor.KtorHttpClient
import core.http.client.test.models.MockHttpResponse
import core.monitoring.common.repository.Logger
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.client.request.HttpRequestData
import io.ktor.client.request.HttpResponseData
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf

class MockKtorHttpClient(private val logger: Logger) : HttpClient {
    private val engine = MockEngine(handler = { handle(it) })
    private val client = KtorHttpClient(engine = engine, unsafeEngine = engine, log = logger)
    private val responses = arrayListOf<Pair<MockHttpResponse, List<HttpRequest>>>()

    override suspend fun request(request: HttpRequest): HttpResponse {
        val index = getResponseIndexByUrlAndMethod(request.url, request.method.toString())
        val value = responses[index]

        responses[index] = value.first to value.second + request

        return client.request(request)
    }

    override suspend fun unsafeRequest(request: HttpRequest): HttpResponse {
        val index = getResponseIndexByUrlAndMethod(request.url, request.method.toString())
        val value = responses[index]

        responses[index] = value.first to value.second + request

        return client.request(request)
    }

    override suspend fun formRequest(request: HttpRequest): HttpResponse {
        val index = getResponseIndexByUrlAndMethod(request.url, request.method.toString())
        val value = responses[index]

        responses[index] = value.first to value.second + request

        return client.request(request)
    }

    fun setup(response: MockHttpResponse) {
        val index = responses.indexOfFirst {
            it.first.url == response.url && it.first.method.lowercase() == response.method.lowercase()
        }
        if (index == -1) responses.add(response to listOf()) else responses[index] = response to responses[index].second
    }

    fun clear() {
        responses.clear()
    }

    fun getRequestsOfCall(url: String, method: String): List<HttpRequest> {
        val index = responses.indexOfFirst { it.first.url == url && it.first.method.lowercase() == method.lowercase() }
        if (index == -1) return listOf()

        val value = responses[index]

        return value.second
    }

    private fun MockRequestHandleScope.handle(request: HttpRequestData): HttpResponseData {
        val index = getResponseIndexByUrlAndMethod(request.url.toString(), request.method.value)

        return responses[index].first.toResponse(this)
    }

    private fun getResponseIndexByUrlAndMethod(url: String, method: String): Int {
        val equalIndex = responses.indexOfFirst {
            it.first.url == url && it.first.method.lowercase() == method.lowercase()
        }
        val matchIndex = if (equalIndex != -1) {
            -1
        } else {
            responses.indexOfFirst {
                it.first.url.urlMatch(url) && it.first.method.lowercase() == method.lowercase()
            }
        }
        val index = if (equalIndex != -1) equalIndex else matchIndex
        if (index == -1) throw Exception("Mock Ktor client is not setup for: $method $url")

        return index
    }

    private fun String.urlMatch(other: String): Boolean {
        val result = this == other || this.toRegex().matches(other)
        return result
    }
}

private fun MockHttpResponse.toResponse(scope: MockRequestHandleScope): HttpResponseData {
    val headers = headers.map { it.key to it.value.split(",") }.toTypedArray()
    return scope.respond(content = body, status = HttpStatusCode.fromValue(code), headers = headersOf(*headers))
}