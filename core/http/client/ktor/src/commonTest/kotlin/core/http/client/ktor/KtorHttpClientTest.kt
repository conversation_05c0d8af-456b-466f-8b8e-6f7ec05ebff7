package core.http.client.ktor

import core.common.serialization.json
import core.http.client.HttpError
import core.http.client.HttpRequest
import core.http.client.HttpResponse
import core.monitoring.common.test.CLILogger
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertNotEquals

internal class KtorHttpClientTest {

    @Test
    fun getRequestSuccess() = runBlocking {
        val bodyStr = Json.encodeToString(MockModel.Empty)
        val client = KtorHttpClient(
            engine = MockEngine { respond(bodyStr) },
            unsafeEngine = MockEngine { respond(bodyStr) },
            log = CLILogger(Any())
        )
        val request = HttpRequest(
            url = "www.mock.com",
            body = null,
            method = HttpRequest.Method.GET,
            headers = listOf()
        )
        val mockResponse = HttpResponse(
            request = request,
            responseHeaders = mapOf(),
            responseCode = HttpStatusCode.OK.value,
            body = bodyStr
        )
        val response = client.request(request)

        assertNotEquals(
            mockResponse,
            response.copy(responseCode = HttpStatusCode.FailedDependency.value)
        )
        assertEquals(mockResponse, response)
    }

    @Test
    fun throwCorrectException(): Unit = runBlocking {
        try {
            val client = KtorHttpClient(
                engine = MockEngine {
                    respond(
                        content = """
                            {"message": "mock bad gateway message"}
                        """.trimIndent(),
                        status = HttpStatusCode.NotFound
                    )
                },
                unsafeEngine = MockEngine {
                    respond(
                        content = """
                            {"message": "mock bad gateway message"}
                        """.trimIndent(),
                        status = HttpStatusCode.NotFound
                    )
                },
                log = CLILogger(Any())
            )
            val request = HttpRequest(
                url = "www.mock.com",
                body = null,
                method = HttpRequest.Method.GET,
                headers = listOf()
            )
            client.request(request)
        } catch (ex: Exception) {
            assertIs<HttpError>(ex)
        }
    }

    @Serializable
    internal data class MockModel(val data: String) {
        companion object {
            val Empty = MockModel("Hello Mock!")
        }
    }

    @Test
    fun errorSerializer() = runTest {
        val sample = """
            {
                "customer": {
                    "address": {
                        "line_1": [
                            "This field is required."
                        ],
                        "zone": [
                            "A valid integer is required."
                        ]
                    }
                }
            }
        """.trimIndent()
        val jsonSample = json.parseToJsonElement(sample)

        assertEquals(
            jsonSample.jsonObject.getMessage().toString(),
            "[This field is required., A valid integer is required.]"
        )
    }
}