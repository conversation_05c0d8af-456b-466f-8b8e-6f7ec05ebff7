package core.http.client.ktor

import core.http.client.WssSocket
import core.monitoring.common.repository.Logger
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.websocket.WebSockets
import io.ktor.client.plugins.websocket.wss
import io.ktor.serialization.kotlinx.json.json
import io.ktor.websocket.Frame
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.ClosedSendChannelException
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout

class KtorWssSocketRepository(
    private val engine: HttpClientEngine,
    private val logger: Logger
) : WssSocket {

    private var client: HttpClient? = null
    private var collectJob: Job? = null

    private val messageFlow = MutableSharedFlow<String>(replay = 0)

    private var isOpen = false

    override fun isOpen(): Boolean {
        return isOpen
    }

    override suspend fun open(url: String) {
        isOpen = true
        catch(onError = {
//            checkInternetAndReport(it)
        }) {
            client = getClient()
            client?.wss(urlString = url) {
                collectJob = CoroutineScope(currentCoroutineContext()).launch {
                    messageFlow.collect {
                        catch(onError = {
//                            checkInternetAndReport(it)
                        }) {
                            outgoing.send(Frame.Text(it))
                            withTimeout(2000) {
                                incoming.receive()
                            }
                        }
                    }
                }
                collectJob?.join()
            }
        }
        close()
    }

    override suspend fun close() {
        client?.close()
        collectJob?.cancel()
        collectJob = null
        client = null
        isOpen = false
    }

    override suspend fun sendInSocket(message: String) {
        messageFlow.emit(message)
    }

    private fun getClient() = HttpClient(engine) {
        install(WebSockets)
        install(ContentNegotiation) {
            json(core.common.serialization.json)
        }
    }

    private suspend fun catch(
        onError: suspend (ex: Exception) -> Unit,
        block: suspend () -> Unit
    ) {
        try {
            block()
        } catch (ex: ClosedSendChannelException) {
            close()
        } catch (ex: CancellationException) {
            close()
        } catch (ex: Exception) {
            onError(ex)
        }
    }
}