package core.http.client.ktor

import core.common.platform.Platform
import core.http.client.HttpError
import core.http.client.HttpRequest
import core.http.client.HttpRequestForm
import core.http.client.HttpRequestFormFileItem
import core.http.client.HttpRequestFormTextItem
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.delete
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.client.statement.bodyAsText
import io.ktor.http.Headers
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

internal typealias KtorResponse = io.ktor.client.statement.HttpResponse

suspend fun HttpRequest.toRequest(
    client: io.ktor.client.HttpClient
): io.ktor.client.statement.HttpResponse {
    return when (method) {
        HttpRequest.Method.GET -> {
            client.get {
                buildHttpRequest(httpRequest = this@toRequest)
            }
        }

        HttpRequest.Method.POST -> {
            client.post { buildHttpRequest(httpRequest = this@toRequest) }
        }

        HttpRequest.Method.PATCH -> {
            client.patch { buildHttpRequest(httpRequest = this@toRequest) }
        }

        HttpRequest.Method.DELETE -> {
            client.delete { buildHttpRequest(httpRequest = this@toRequest) }
        }
    }
}

internal suspend fun Exception.getError(request: HttpRequest): HttpError {
    val message = if (this is ResponseException) {
        """
            ${getErrorMessage(response)}
        """.trimIndent()
    } else {
        this.message
    }
    val responseBody = if (this is ResponseException) {
        response.bodyAsText()
    } else {
        null
    }

    return HttpError(
        cause = this,
        callLocation = Platform.executeLocation(),
        message = message ?: "Connection error",
        responseCode = if (this is ResponseException) response.status.value else 0,
        details = mapOf(
            "Method" to request.method,
            "Url" to request.url,
            "Body" to (request.body?.toString() ?: "No body"),
            *request.headers.map {
                it.type.toString() to substring(it.value.toString(), 0, 15)
            }.toTypedArray()
        ),
        responseBody = responseBody,
        responseCodeDescription = if (this is ResponseException) {
            response.status.description
        } else {
            message ?: "Message not found"
        }
    )
}

private fun substring(string: String, start: Int, end: Int): String {
    if (end >= string.length) {
        return string
    }

    return string.substring(start, end)
}

private suspend fun getErrorMessage(
    res: io.ktor.client.statement.HttpResponse
): String {
    val response = runCatching { Json.parseToJsonElement(res.bodyAsText()).jsonObject }
    val responseObject = response.getOrNull() ?: return "Error code: ${res.status}"

    return runCatching {
        responseObject.getErrorMessage()
            ?: responseObject["detail"]?.jsonPrimitive?.contentOrNull
            ?: responseObject["details"]?.jsonPrimitive?.contentOrNull
            ?: responseObject.scrapSerializer()
    }.getOrNull() ?: "Error code: ${res.status}"
}

private fun JsonObject.getErrorMessage(): String? {
    return this["message"]?.getErrorMessage()
}

private fun HttpRequestBuilder.buildHttpRequest(httpRequest: HttpRequest) {
    url(httpRequest.url)
    if (httpRequest.body != null) {
        setBody(httpRequest.body!!)
    }

    if (httpRequest.form != null) {
        setBody((httpRequest.form as HttpRequestForm).toContent())
    }

    for (httpHeader in httpRequest.headers) {
        header(httpHeader.type.toString(), httpHeader.value)
    }
}

private fun HttpRequestForm.toContent(): MultiPartFormDataContent {
    return MultiPartFormDataContent(
        formData {
            for (item in items) {
                when (item) {
                    is HttpRequestFormTextItem -> append(item.name, item.value as String)
                    is HttpRequestFormFileItem -> append(
                        item.name,
                        item.file,
                        Headers.build {
                            append("Content-Disposition", "filename=\"${item.fileName}\"")
                            append("Content-Type", item.fileType)
                        }
                    )
                }
            }
        }
    )
}

private fun JsonObject.scrapSerializer(): String? {
    val message = getMessage()
    return if (message.isNotEmpty()) {
        message.joinToString("\n")
    } else {
        null
    }
}

internal fun JsonObject.getMessage(): ArrayList<String> {
    val messages = arrayListOf<String>()
    for (key in keys) {
        val value = get(key)
        if (value is JsonObject) {
            messages.addAll(value.getMessage())
        } else if (value is JsonArray) {
            messages.addAll(value.map { it.jsonPrimitive.content })
        } else if (value is JsonPrimitive) {
            messages.add(value.content)
        }
    }
    return messages
}

private fun JsonElement.getErrorMessage(): String? {
    return when (this) {
        is JsonObject -> scrapSerializer()
        is JsonArray -> scrapSerializer()
        is JsonPrimitive -> contentOrNull
        else -> null
    }
}

private fun JsonArray.scrapSerializer(): String? {
    return map { it.getErrorMessage() }.joinToString("\n")
}