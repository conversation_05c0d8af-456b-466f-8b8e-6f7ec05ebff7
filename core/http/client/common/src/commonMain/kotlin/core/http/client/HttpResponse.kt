package core.http.client

import core.common.serialization.json
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable

@Serializable
data class HttpResponse(
    val request: HttpRequest,
    val responseHeaders: Map<String, List<String>>,
    val responseCode: Int,
    val body: String
) {
    fun <T> value(serializer: KSerializer<T>): T {
        return json.decodeFromString(serializer, body)
    }
}