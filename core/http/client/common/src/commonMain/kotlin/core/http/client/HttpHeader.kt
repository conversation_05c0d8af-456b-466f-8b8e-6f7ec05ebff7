package core.http.client

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class HttpHeader(
    val type: Type,
    @Contextual val value: Any
) {

    enum class Type {
        AUTHORIZATION {
            override fun toString(): String = "Authorization"
        },
        ACCEPT_LANGUAGE {
            override fun toString(): String = "Accept-Language"
        },
        ACCEPT_ENCODING {
            override fun toString(): String = "Accept-Encoding"
        },
        CONTENT_TYPE {
            override fun toString(): String = "Content-Type"
        }
    }

    fun toPair() = type.toString() to listOf(value.toString())
}