package core.http.client

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class HttpRequest(
    val url: String,
    val body: JsonElement? = null,
    val form: HttpRequestForm? = null,
    val method: Method,
    val headers: List<HttpHeader>
) {
    enum class Method {
        GET {
            override fun toString(): String = "GET"
        },
        POST {
            override fun toString(): String = "POST"
        },
        PATCH {
            override fun toString(): String = "PATCH"
        },
        DELETE {
            override fun toString(): String = "DELETE"
        };

        companion object {
            fun fromString(method: String): Method {
                return when (method.uppercase()) {
                    "GET" -> GET
                    "POST" -> POST
                    "PATCH" -> PATCH
                    "DELETE" -> DELETE
                    else -> throw Exception("UnHandled HttpMethod")
                }
            }
        }
    }
}

@Serializable
data class HttpRequestForm(
    val binaryData: Boolean,
    val items: List<HttpRequestFormItem>
)

sealed interface HttpRequestFormItem

@Serializable
data class HttpRequestFormTextItem(
    val name: String,
    @Contextual val value: Any
) : HttpRequestFormItem

@Serializable
data class HttpRequestFormFileItem(
    val name: String,
    val fileName: String,
    val fileType: String,
    val file: ByteArray
) : HttpRequestFormItem {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as HttpRequestFormFileItem

        if (name != other.name) return false
        if (fileName != other.fileName) return false
        if (fileType != other.fileType) return false
        if (!file.contentEquals(other.file)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = name.hashCode()
        result = 31 * result + fileName.hashCode()
        result = 31 * result + fileType.hashCode()
        result = 31 * result + file.contentHashCode()
        return result
    }
}