package core.http.client

import core.common.error.KError
import core.common.message.Message

open class HttpError(
    message: String,
    cause: Exception,
    callLocation: String,
    val responseCode: Int,
    val responseCodeDescription: String,
    details: Map<String, Any>,
    val responseBody: String?
) : KError.Fatal(
    message = Message.fromString(message),
    cause = cause,
    callLocation = callLocation,
    details = details
)