package core.location.common

import kotlinx.coroutines.flow.Flow

interface LocationRepository {
    fun openMap(lat: Double, long: Double, title: String)

    fun openMap(address: String, title: String)

    fun stream(): Flow<Location?>

    suspend fun getCurrentLocation(highAccuracy: Boolean = true, forceRefresh: Boolean = false): Location

    fun calculateDistance(location1: Location, location2: Location): Double

    suspend fun areLocationSettingsOptimised(): Boolean

    suspend fun requestLocationSettingsOptimization(): Boolean
}