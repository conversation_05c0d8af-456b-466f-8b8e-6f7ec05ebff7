package core.location.google

import android.Manifest
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import android.os.SystemClock
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.Priority
import core.common.error.debugString
import core.common.error.toError
import core.common.platform.Platform
import core.monitoring.common.models.ExecutionLog
import kotlin.math.acos
import kotlin.math.cos
import kotlin.math.sin

fun Location.toLocation(): core.location.common.Location {
    return core.location.common.Location(
        latitude,
        longitude,
        ageInSeconds = fromNanosToSeconds(),
        isMock = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            isMock
        } else {
            isFromMockProvider
        }
    )
}

internal inline fun <T> AndroidLocationRepository.handler(job: () -> T): T {
    try {
        return job()
    } catch (ex: SecurityException) {
        logger.log(ExecutionLog.Info("[ERROR] location security exception: ${ex.debugString()}"))
        throw ex.toError(Platform.executeLocation(), mapOf())
    } catch (ex: Exception) {
        logger.log(ExecutionLog.Info("[ERROR] location exception: ${ex.debugString()}"))
        throw ex.toError(Platform.executeLocation(), mapOf())
    }
}

internal suspend fun AndroidLocationRepository.checkPermissionsOrThrow(
    @Suppress("SameParameterValue") highAccuracy:
        Boolean
) {
    val permissions = arrayListOf(Manifest.permission.ACCESS_COARSE_LOCATION)

    if (highAccuracy) {
        permissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
    }

    for (permission in permissions) {
        if (ActivityCompat.checkSelfPermission(applicationContext, permission) != PackageManager.PERMISSION_GRANTED) {
            throw Exception("Location permission is not granted").toError(Platform.executeLocation(), mapOf())
        }
    }

    if (!areLocationSettingsOptimised() && !requestLocationSettingsOptimization()) {
        throw Exception("Location settings are not optimised").toError(Platform.executeLocation(), mapOf())
    }
}

internal fun distance(location1: core.location.common.Location, location2: core.location.common.Location): Double {
    val theta = location1.long - location2.long
    var dist = sin(deg2rad(location1.lat)) * sin(deg2rad(location2.lat)) +
        cos(deg2rad(location1.lat)) * cos(deg2rad(location2.lat)) *
        cos(deg2rad(theta))
    dist = acos(dist)
    dist = rad2deg(dist)
    dist *= 60 * 1.1515
    dist *= 1.609344 * 1000
    return dist
}

private fun deg2rad(deg: Double): Double {
    return deg * Math.PI / 180.0
}

private fun rad2deg(rad: Double): Double {
    return rad * 180.0 / Math.PI
}

internal fun Location.fromNanosToSeconds(): Long {
    return (SystemClock.elapsedRealtimeNanos() - elapsedRealtimeNanos) / 1_000_000_000
}

internal fun getAccuraciesList(highAccuracy: Boolean): List<Int> {
    val accuracies = arrayListOf(
        Priority.PRIORITY_BALANCED_POWER_ACCURACY,
        Priority.PRIORITY_LOW_POWER,
        Priority.PRIORITY_PASSIVE
    )

    if (highAccuracy) accuracies.add(0, Priority.PRIORITY_HIGH_ACCURACY)

    return accuracies
}