package core.location.google

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.net.Uri
import android.os.Looper
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.LifecycleOwner
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResponse
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.Task
import core.common.error.debugString
import core.common.error.toFatal
import core.common.lifecycle.BindLifecycle
import core.common.platform.Platform
import core.location.common.Location
import core.location.common.LocationRepository
import core.monitoring.common.models.ExecutionLog
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class AndroidLocationRepository(
    internal val applicationContext: Context,
    internal val logger: Logger
) : LocationRepository, BindLifecycle {

    private val LAST_LOCATION_TIMEOUT = 5.seconds
    private val CURRENT_LOCATION_TIMEOUT = 60.seconds

    private val locationStream = MutableSharedFlow<Location?>()
        .onStart {
            if (!areLocationSettingsOptimised()) {
                requestLocationSettingsOptimization()
            }

            registerLocationUpdates { runBlocking { emit(it) } }
        }.onCompletion {
            unregisterLocationUpdates()
        }

    private val request = LocationRequest
        .Builder(/* priority */ Priority.PRIORITY_HIGH_ACCURACY, /* intervalMillis = */ 15.seconds.inWholeMilliseconds)
        .setMinUpdateIntervalMillis(10.seconds.inWholeMilliseconds)
        .setMaxUpdateDelayMillis(20.seconds.inWholeMilliseconds)

    private var callback: LocationCallback? = null

    override var owner: LifecycleOwner? = null
    private val flow = MutableStateFlow<Boolean>(false)
    private val settingsFlow = flow.asSharedFlow()
    private lateinit var launcher: ActivityResultLauncher<IntentSenderRequest>

    override fun onCreate(source: LifecycleOwner) {
        super.onCreate(source)
        launcher = getActivityLauncher { result -> flow.update { result } }
    }

    override fun openMap(address: String, title: String) = logger.log {
        logger.log(ExecutionLog.Info("opening map with address: $address"))
        val encodedAddress = Uri.encode(address)
        val query = "geo:0,0?q=$encodedAddress"
        val gmmIntentUri = Uri.parse(query)
        val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
        mapIntent.setPackage("com.google.android.apps.maps")
        mapIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        applicationContext.startActivity(mapIntent)
    }

    override fun openMap(lat: Double, long: Double, title: String) = logger.log {
        logger.log(ExecutionLog.Info("opening map with lat: $lat, long: $long"))
        val gmmIntentUri = Uri.parse("geo:0,0?q=$lat,$long($title)")
        val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
        mapIntent.setPackage("com.google.android.apps.maps")
        mapIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        applicationContext.startActivity(mapIntent)
    }

    override fun stream(): Flow<Location?> {
        return locationStream
    }

    @SuppressLint("MissingPermission")
    private fun registerLocationUpdates(update: (Location?) -> Unit) = logger.log {
        handler {
            if (callback != null) return@log
            logger.log(ExecutionLog.Info("registering location updates"))

            val looper = Looper.getMainLooper()
            val client = LocationServices.getFusedLocationProviderClient(applicationContext)
            val locationCallback = object : LocationCallback() {
                override fun onLocationResult(locationResult: LocationResult) {
                    super.onLocationResult(locationResult)
                    val lastLocation = locationResult.lastLocation?.toLocation()
                    logger.log(ExecutionLog.Info("emitting last location: $lastLocation"))
                    update(lastLocation)
                }
            }

            client.requestLocationUpdates(request.build(), locationCallback, looper)
            callback = locationCallback
        }
    }

    @SuppressLint("MissingPermission")
    private fun unregisterLocationUpdates() = logger.log {
        handler {
            if (callback == null) return@log
            logger.log(ExecutionLog.Info("unregistering location updates"))
            val client = LocationServices.getFusedLocationProviderClient(applicationContext)
            callback?.let { client.removeLocationUpdates(it) }
            callback = null
        }
    }

    override suspend fun getCurrentLocation(highAccuracy: Boolean, forceRefresh: Boolean): Location {
        val lastLocation = getLastLocation(highAccuracy)

        checkPermissionsOrThrow(highAccuracy)

        if (lastLocation != null && !forceRefresh && lastLocation.ageInSeconds <= 1.minutes.inWholeSeconds) {
            return lastLocation
        }
        val freshLocation = getFreshLocation(highAccuracy)

        return freshLocation ?: lastLocation ?: error(
            "Error in getting location of the\n" +
                "Make sure all permissions are granted\n" +
                "GPS is turned on, and there is internet connection"

        )
    }

    override fun calculateDistance(location1: Location, location2: Location): Double {
        return distance(location1, location2)
    }

    private suspend fun getFreshLocation(highAccuracy: Boolean) = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("getting fresh location"))
        val accuracies = getAccuraciesList(highAccuracy)

        for (accuracy in accuracies) {
            val location =
                withTimeoutOrNull(CURRENT_LOCATION_TIMEOUT.inWholeMilliseconds) { getFusedCurrentLocation(accuracy) }
            if (location != null) {
                logger.log(ExecutionLog.Info("fresh location: $location"))
                return@async location
            } else {
                logger.log(ExecutionLog.Info("fresh location failed for accuracy: $accuracy"))
            }
        }

        return@async null
    }

    private suspend fun getLastLocation(highAccuracy: Boolean): Location? = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("getting last location"))
        val accuracies = getAccuraciesList(highAccuracy)

        for (accuracy in accuracies) {
            val location =
                withTimeoutOrNull(LAST_LOCATION_TIMEOUT.inWholeMilliseconds) { getFusedLastLocation(accuracy) }
            if (location != null) {
                logger.log(ExecutionLog.Info("last location: $location"))
                return@async location
            } else {
                logger.log(ExecutionLog.Info("last location failed for accuracy: $accuracy"))
            }
        }

        return@async runCatching { getOfflineLocation() }.getOrNull()
    }

    @SuppressLint("MissingPermission")
    private suspend fun getFusedCurrentLocation(accuracy: Int): Location? = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("getting current location: $accuracy "))
        val client = LocationServices.getFusedLocationProviderClient(applicationContext)
        checkPermissionsOrThrow(accuracy == Priority.PRIORITY_HIGH_ACCURACY)
        return@async suspendCancellableCoroutine { continuation ->
            val task = client.getCurrentLocation(accuracy, null)

            task.addOnSuccessListener {
                logger.log(ExecutionLog.Info("current $accuracy location: $it"))
                continuation.resume(it?.toLocation())
            }

            task.addOnFailureListener {
                logger.log(ExecutionLog.Info("current $accuracy location failed with: ${it.debugString()}"))
                continuation.resumeWithException(it)
            }
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun getFusedLastLocation(accuracy: Int): Location? = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("getting last location: $accuracy "))
        val client = LocationServices.getFusedLocationProviderClient(applicationContext)
        checkPermissionsOrThrow(accuracy == Priority.PRIORITY_HIGH_ACCURACY)

        return@async suspendCancellableCoroutine { continuation ->
            val task = client.lastLocation

            task.addOnSuccessListener {
                logger.log(ExecutionLog.Info("last $accuracy location: $it"))
                continuation.resume(it?.toLocation())
            }

            task.addOnFailureListener {
                logger.log(ExecutionLog.Info("last $accuracy location failed with: ${it.debugString()}"))
                continuation.resumeWithException(it)
            }
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun getOfflineLocation(): Location = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("getting location from location manager"))
        checkPermissionsOrThrow(false)
        val manager = applicationContext.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val currentLocation = manager.getLastKnownLocation(LocationManager.PASSIVE_PROVIDER)

        return@async currentLocation?.toLocation() ?: error("No offline location available")
    }

    override suspend fun areLocationSettingsOptimised(): Boolean = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("checking location settings optimization"))
        return@async suspendCancellableCoroutine { continuation ->
            val builder = LocationSettingsRequest
                .Builder()
                .addLocationRequest(request.build())

            val result: Task<LocationSettingsResponse> = LocationServices
                .getSettingsClient(applicationContext)
                .checkLocationSettings(builder.build())

            result.addOnSuccessListener {
                logger.log(ExecutionLog.Info("location settings optimization is on"))
                continuation.resume(true)
            }

            result.addOnFailureListener {
                logger.log(ExecutionLog.Info("location settings optimization failed with: ${it.debugString()}"))
                continuation.resume(false)
            }
        }
    }

    override suspend fun requestLocationSettingsOptimization(): Boolean = logger.async(currentCoroutineContext()) {
        logger.log(ExecutionLog.Info("requesting location settings optimization"))
        val context = currentCoroutineContext()
        val result = suspendCancellableCoroutine { continuation ->
            val request = request.build()
            val builder = LocationSettingsRequest.Builder().addLocationRequest(request)
            val result: Task<LocationSettingsResponse> = LocationServices
                .getSettingsClient(applicationContext)
                .checkLocationSettings(builder.build())

            result.addOnSuccessListener {
                logger.log(ExecutionLog.Info("location settings optimization is on"))
                continuation.resume(true)
            }

            result.addOnFailureListener { ex ->
                logger.log(ExecutionLog.Info("location settings optimization failed with: ${ex.debugString()}"))
                CoroutineScope(context).launch { continuation.onRequestSettingsOptimizationFailed(ex) }
            }
        }

        return@async withTimeoutOrNull(300.seconds) { result } == true
    }

    private fun getActivityLauncher(callback: (Boolean) -> Unit): ActivityResultLauncher<IntentSenderRequest> {
        val caller = owner as ActivityResultCaller
        val resultCallback = ActivityResultCallback<ActivityResult> { result ->
            callback(result.resultCode == Activity.RESULT_OK)
        }

        return caller.registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult(), resultCallback)
    }

    private suspend fun CancellableContinuation<Boolean>.onRequestSettingsOptimizationFailed(
        exception: Exception
    ): Unit = logger.async(currentCoroutineContext()) {
        if (exception is ResolvableApiException) {
            launcher.launch(IntentSenderRequest.Builder(exception.resolution).build())
            val result = settingsFlow.take(1).last()
            logger.log(ExecutionLog.Info("location settings optimization result: $result"))
            resume(result)
        } else {
            logger.error(exception.toFatal(Platform.executeLocation(), mapOf()))
            resume(false)
        }
    }
}