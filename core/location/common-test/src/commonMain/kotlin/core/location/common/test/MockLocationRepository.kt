package core.location.common.test

import core.location.common.Location
import core.location.common.LocationRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow

class MockLocationRepository : LocationRepository {

    private val locationStream = MutableSharedFlow<Location>()

    private var areLocationSettingsOptimised = false
    private var areRequestLocationSettingsOptimization = false
    private var calculatedDistance = 0.0
    private var currentLocation = Location(0.0, 0.0, false, 0)
    private var mapOpened = false
    private var locationUpdatesRegistered = false

    override fun openMap(lat: Double, long: Double, title: String) {
        mapOpened = true
    }

    override fun openMap(address: String, title: String) {
        mapOpened = true
    }

    private fun registerLocationUpdates() {
        locationUpdatesRegistered = true
    }

    private fun endLocationUpdates() {
        locationUpdatesRegistered = false
    }

    override fun stream(): SharedFlow<Location> {
        return locationStream
    }

    override suspend fun getCurrentLocation(highAccuracy: Boolean, forceRefresh: Boolean): Location {
        return currentLocation
    }

    override fun calculateDistance(location1: Location, location2: Location): Double {
        return calculatedDistance
    }

    override suspend fun areLocationSettingsOptimised(): Boolean {
        return areLocationSettingsOptimised
    }

    override suspend fun requestLocationSettingsOptimization(): Boolean {
        return areRequestLocationSettingsOptimization
    }

    fun setCalculatedDistance(distance: Double) {
        calculatedDistance = distance
    }

    fun setCurrentLocation(location: Location) {
        currentLocation = location
    }

    fun setAreLocationSettingsOptimised(areOptimised: Boolean) {
        areLocationSettingsOptimised = areOptimised
    }

    fun setAreRequestLocationSettingsOptimization(areOptimised: Boolean) {
        areRequestLocationSettingsOptimization = areOptimised
    }

    fun isMapOpened(): Boolean {
        return mapOpened
    }

    fun isLocationUpdatesRegistered(): Boolean {
        return locationUpdatesRegistered
    }

    suspend fun emitLocation(location: Location) {
        locationStream.emit(location)
    }
}