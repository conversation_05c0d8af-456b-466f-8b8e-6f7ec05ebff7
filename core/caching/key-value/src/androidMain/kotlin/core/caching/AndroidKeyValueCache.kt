package core.caching

import android.content.Context
import android.content.SharedPreferences
import core.monitoring.common.models.ExecutionLog
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update

class AndroidKeyValueCache(
    applicationContext: Context,
    name: String = "${applicationContext.packageName}.KeyValueStorage",
    private val logger: Logger
) : KeyValueCache {

    private val storage = applicationContext.getSharedPreferences(
        name,
        Context.MODE_PRIVATE
    )

    override fun setString(key: String, value: String) = logger.log {
        logger.log(ExecutionLog.Info("setting string preference: key -> $key, value: $value"))
        storage.edit().putString(key, value).apply()
    }

    override fun setInt(key: String, value: Int) = logger.log {
        logger.log(ExecutionLog.Info("setting int preference: key -> $key, value: $value"))
        storage.edit().putInt(key, value).apply()
    }

    override fun setLong(key: String, value: Long) = logger.log {
        logger.log(ExecutionLog.Info("setting long preference: key -> $key, value: $value"))
        storage.edit().putLong(key, value).apply()
    }

    override fun setBoolean(key: String, value: Boolean) = logger.log {
        logger.log(ExecutionLog.Info("setting boolean preference: key -> $key, value: $value"))
        storage.edit().putBoolean(key, value).apply()
    }

    override fun setFloat(key: String, value: Float) = logger.log {
        logger.log(ExecutionLog.Info("setting float preference: key -> $key, value: $value"))
        storage.edit().putFloat(key, value).apply()
    }

    override fun getString(key: String, default: String): String = logger.log {
        val result = storage.getString(key, default) ?: default
        logger.log(ExecutionLog.Info("getting string preference: key -> $key, value: $result"))
        return@log result
    }

    override fun streamString(key: String, default: String): Flow<String> = logger.log {
        logger.log(ExecutionLog.Info("streaming string preference: key -> $key, default: $default"))
        stream(key) {
            val value = it.getString(key, default) ?: default
            logger.log(ExecutionLog.Info("streaming string preference: key -> $key, value: $value"))
            value
        }
    }

    override fun getInt(key: String, default: Int): Int = logger.log {
        val result = storage.getInt(key, default)
        logger.log(ExecutionLog.Info("getting int preference: key -> $key, value: $result"))
        return@log result
    }

    override fun streamInt(key: String, default: Int): Flow<Int> = logger.log {
        logger.log(ExecutionLog.Info("streaming int preference: key -> $key, default: $default"))
        stream(key) {
            val value = it.getInt(key, default)
            logger.log(ExecutionLog.Info("streaming int preference: key -> $key, value: $value"))
            value
        }
    }

    override fun getLong(key: String, default: Long): Long = logger.log {
        val result = storage.getLong(key, default)
        logger.log(ExecutionLog.Info("getting long preference: key -> $key, value: $result"))
        return@log result
    }

    override fun streamLong(key: String, default: Long): Flow<Long> = logger.log {
        logger.log(ExecutionLog.Info("streaming long preference: key -> $key, default: $default"))
        stream(key) {
            val value = it.getLong(key, default)
            logger.log(ExecutionLog.Info("streaming long preference: key -> $key, value: $value"))
            value
        }
    }

    override fun getBoolean(key: String, default: Boolean): Boolean = logger.log {
        val result = storage.getBoolean(key, default)
        logger.log(ExecutionLog.Info("getting boolean preference: key -> $key, value: $result"))
        return@log result
    }

    override fun streamBoolean(key: String, default: Boolean): Flow<Boolean> = logger.log {
        logger.log(ExecutionLog.Info("streaming boolean preference: key -> $key, default: $default"))
        stream(key) {
            val value = it.getBoolean(key, default)
            logger.log(ExecutionLog.Info("streaming boolean preference: key -> $key, value: $value"))
            value
        }
    }

    override fun getFloat(key: String, default: Float): Float = logger.log {
        val result = storage.getFloat(key, default)
        logger.log(ExecutionLog.Info("getting float preference: key -> $key, value: $result"))
        return@log result
    }

    override fun streamFloat(key: String, default: Float): Flow<Float> = logger.log {
        logger.log(ExecutionLog.Info("streaming float preference: key -> $key, default: $default"))
        stream(key) {
            val value = it.getFloat(key, default)
            logger.log(ExecutionLog.Info("streaming float preference: key -> $key, value: $value"))
            value
        }
    }

    override fun remove(key: String) = logger.log {
        logger.log(ExecutionLog.Info("removing preference: key -> $key"))
        storage.edit().remove(key).apply()
    }

    override fun clear() {
        logger.log(ExecutionLog.Info("clearing all preferences"))
        storage.edit().clear().apply()
    }

    private fun <T> stream(key: String, callback: (SharedPreferences) -> T): Flow<T> {
        val flow = MutableStateFlow(callback(storage))
        val listener = SharedPreferences.OnSharedPreferenceChangeListener { sharedPreferences, preferenceKey ->
            if (preferenceKey === key) {
                flow.update { callback(sharedPreferences) }
            }
        }

        return flow
            .onStart { storage.registerOnSharedPreferenceChangeListener(listener) }
            .onCompletion { storage.unregisterOnSharedPreferenceChangeListener(listener) }
            .distinctUntilChanged()
    }
}