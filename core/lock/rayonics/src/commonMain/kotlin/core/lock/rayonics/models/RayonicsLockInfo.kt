package core.lock.rayonics.models

data class RayonicsLockInfo(
    val mac: String,
    val hardwareVersion: String,
    val softwareVersion: String,
    val serialId: Int,
    val cylinderId: Int,
    val battery: Int,
    val autoLockTimeSec: Int,
    val bleBroadcastIntervalTime: Int,
    val beep: Int,
    val isUsePK: Boolean,
    val groupId: IntArray,
    val locationId: Int

) {
    override fun equals(other: Any?): <PERSON>olean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RayonicsLockInfo

        if (mac != other.mac) return false
        if (hardwareVersion != other.hardwareVersion) return false
        if (softwareVersion != other.softwareVersion) return false
        if (serialId != other.serialId) return false
        if (cylinderId != other.cylinderId) return false
        if (battery != other.battery) return false
        if (autoLockTimeSec != other.autoLockTimeSec) return false
        if (bleBroadcastIntervalTime != other.bleBroadcastIntervalTime) return false
        if (beep != other.beep) return false
        if (isUsePK != other.isUsePK) return false
        if (!groupId.contentEquals(other.groupId)) return false
        if (locationId != other.locationId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = mac.hashCode()
        result = 31 * result + hardwareVersion.hashCode()
        result = 31 * result + softwareVersion.hashCode()
        result = 31 * result + serialId
        result = 31 * result + cylinderId
        result = 31 * result + battery
        result = 31 * result + autoLockTimeSec
        result = 31 * result + bleBroadcastIntervalTime
        result = 31 * result + beep
        result = 31 * result + isUsePK.hashCode()
        result = 31 * result + groupId.contentHashCode()
        result = 31 * result + locationId
        return result
    }
}