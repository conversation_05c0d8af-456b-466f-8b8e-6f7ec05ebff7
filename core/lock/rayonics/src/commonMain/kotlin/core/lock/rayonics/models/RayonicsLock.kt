package core.lock.rayonics.models

import core.lock.common.models.DeviceFirmwareUpdateState
import kotlinx.coroutines.flow.Flow

interface RayonicsLock {

    val macAddress: String
    val name: String
    val rssi: Int
    val scanRecord: ByteArray
    val lastSeen: Long

    suspend fun unlock()

    suspend fun connect(accessKey: String): RayonicsLockInfo

    suspend fun registerLock(accessKey: String)

    suspend fun resetLockFactory(accessKey: String)

    suspend fun disconnect()

    suspend fun getDeviceStatus()

    suspend fun getDeviceInfo(): String

    suspend fun resetLockTimeCustom(dateTime: Long)

    suspend fun resetLockTime()

    suspend fun updateFirmware(
        url: String,
        activityQualifier: String,
        accessKey: String,
        status: suspend (DeviceFirmwareUpdateState) -> Unit
    )

    suspend fun enableUpgradeFlag()

    suspend fun createPasscode(passcode: String, fromEpochMillis: Long, toEpochMillis: Long): String

    suspend fun deletePasscode(passcode: String): String

    suspend fun openWithPasscode(accessKey: String, passcode: String)

    suspend fun logs(last: Int): Flow<String>

    suspend fun pairKeypadWithLock(accessKey: String, lockName: String, macAddress: String): String
}