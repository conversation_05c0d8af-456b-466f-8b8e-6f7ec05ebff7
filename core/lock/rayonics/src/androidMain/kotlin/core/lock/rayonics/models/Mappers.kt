package core.lock.rayonics.models

import core.lock.common.toLocalDateTime
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockEventData

internal fun LockBasicInfo.toModel(): RayonicsLockInfo {
    return RayonicsLockInfo(
        mac = mac,
        hardwareVersion = hardwareVersion,
        softwareVersion = softwareVersion,
        serialId = serialId,
        cylinderId = cylinderId,
        battery = battery,
        autoLockTimeSec = autoLockTimeSec,
        bleBroadcastIntervalTime = bleBroadcastIntervalTime,
        beep = beep,
        isUsePK = isUsePK,
        groupId = groupId,
        locationId = locationId
    )
}

internal fun LockEventData.EventData.string(): String? {
    val date = eventTime.toLocalDateTime()
    val dateTime = "${date.date} ${date.time}"
    return "[$dateTime] ${lockEventEnum.name} : ${eventModeEnum.name}"
}