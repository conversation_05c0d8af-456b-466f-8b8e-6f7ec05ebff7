package core.lock.rayonics.models

import android.app.Application
import android.bluetooth.BluetoothManager
import androidx.core.net.toUri
import core.common.result.AsyncResult
import core.http.download.Downloader
import core.lock.common.Constants
import core.lock.common.errors.connectionException
import core.lock.common.errors.timeoutException
import core.lock.common.models.DeviceFirmwareUpdateState
import core.lock.nordicfirmware.NordicFirmwareRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import rayo.logicsdk.bean.EnableEnum
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.LockTimeClass
import rayo.logicsdk.bean.PermissionActionEnum
import rayo.logicsdk.bean.PermissionTypeEnum
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockBleOpenData
import rayo.logicsdk.data.LockEventData
import rayo.logicsdk.data.LockPermissionData
import rayo.logicsdk.data.LockStatusData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.utils.HexUtil
import rayo.logicsdk.utils.TimeUtils
import java.io.File
import java.sql.Timestamp
import java.time.temporal.ChronoUnit
import java.util.Calendar
import java.util.Date

class AndroidRayonicsLock(
    override val macAddress: String,
    override val name: String,
    override val rssi: Int,
    override val scanRecord: ByteArray,
    override val lastSeen: Long,
    internal val logger: Logger,
    private val context: Application,
    private val bluetoothManager: BluetoothManager,
    private val downloader: Downloader,
    private val nordic: NordicFirmwareRepository,
    dispatcher: CoroutineDispatcher
) : RayonicsLock {

    internal val scope = CoroutineScope(dispatcher)

    private val sdk = BleLockSdk().apply { init(bluetoothManager, mBleLockSdkCallback) }

    override suspend fun unlock() = logger.async {
        val lockBleOpenData = LockBleOpenData()
        val nowTime = Calendar.getInstance()

        nowTime.add(Calendar.MINUTE, -1)
        lockBleOpenData.beginTime = TimeUtils.dateFromNotYMDHMS(
            TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' }
        )
        nowTime.add(Calendar.MINUTE, 1)
        lockBleOpenData.endTime = TimeUtils.dateFromNotYMDHMS(
            TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' }
        )

        sdk.bleOpenLock(lockBleOpenData)

        timed { unlockFlow.first() }

        Unit
    }

    override suspend fun connect(accessKey: String) = logger.async {
        val lockCodeClass = LockCodeClass()

        if (accessKey.isNotBlank()) {
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = accessKey.toByteArray()
        }
        sdk.connect(
            lockCodeClass,
            bluetoothManager,
            context,
            macAddress,
            scanRecord,
            "1".toByteArray(),
            Date(),
            false
        )

        timed { authenticationFlow.first() }.toModel()
    }

    override suspend fun disconnect() = logger.async {
        sdk.disconnect()
        timed { disconnectFlow.first() }
        Unit
    }

    override suspend fun registerLock(accessKey: String): Unit = logger.async {
        val lockCodeClass = LockCodeClass()

        lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
        lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
        lockCodeClass.sysCode = accessKey.toByteArray()

        sdk.registerLock(lockCodeClass)

        timed(Constants.BLUETOOTH_TIMEOUT_MILLIS) { registerLockFlow.first() }
        Unit
    }

    override suspend fun resetLockFactory(accessKey: String) = logger.async {
        val lockCodeClass = LockCodeClass()

        sdk.resetLockFactory(lockCodeClass)

        timed { resetLockFlow.first() }
        Unit
    }

    override suspend fun getDeviceStatus(): Unit = logger.async {
        sdk.getLockStatus()
        timed { lockStatusFlow.first() }
        Unit
    }

    override suspend fun getDeviceInfo(): String = logger.async {
        sdk.getLockInfo()
        timed { lockInfoFlow.first() }.toString()
    }

    override suspend fun resetLockTimeCustom(dateTime: Long) {
        val instant = Instant
            .fromEpochMilliseconds(dateTime)
            .toLocalDateTime(TimeZone.currentSystemDefault())
        val (date, time) = instant.toString().split("T")
        val splits = time.split(".")
        val whole = splits.first().let {
            if (it.length == 5) "$it:00" else it
        }
        sdk.setLockTime(LockTimeClass().apply { lockTime = Timestamp.valueOf("$date $whole") })
        timed { setLockTimeFlow.first() }
        Unit
    }

    override suspend fun resetLockTime() = logger.async {
        sdk.setLockTime(LockTimeClass().apply { lockTime = Timestamp(System.currentTimeMillis()) })
        timed { setLockTimeFlow.first() }
        Unit
    }

    override suspend fun updateFirmware(
        url: String,
        activityQualifier: String,
        accessKey: String,
        status: suspend (DeviceFirmwareUpdateState) -> Unit
    ) = logger.async {
        connect(accessKey)
        val destination = File(context.externalCacheDir.toString(), "keylessFirmware.zip")
        if (destination.exists()) {
            destination.delete()
        }
        val downloadId = downloader.enqueue(url = url, title = "keylessFirmware", savePath = destination.path)
        downloader.listen(id = downloadId).collect { logger.log("Downloading firmware: $it") }

        nordic.install(
            fileUri = destination.toUri(),
            deviceMac = macAddress,
            deviceName = name,
            activityQualifier = activityQualifier
        )
        nordic
            .stream
            .takeWhile {
                it is DeviceFirmwareUpdateState.Idle || it is DeviceFirmwareUpdateState.InProgress
            }
            .collect {
                logger.log("Firmware update: $it")
                status(it)
            }

        if (nordic.stream.value is DeviceFirmwareUpdateState.Error) {
            val error = connectionException(
                message = "Firmware update failed with: ${
                (nordic.stream.value as DeviceFirmwareUpdateState.Error).message
                }"
            )
            logger.error(error)
            status(nordic.stream.value)
            throw error
        } else {
            logger.log("Firmware update succeeded.")
        }
    }

    override suspend fun enableUpgradeFlag() = logger.async {
        sdk.getLockInfo()
        val info = lockInfoFlow.first().result

        info.setmUpgradeFlag(EnableEnum.ENABLE_ENUM)
        sdk.setLockInfo(info)
        sdk.disconnect()

        timed { disconnectFlow.first() }
        Unit
    }

    override suspend fun createPasscode(passcode: String, fromEpochMillis: Long, toEpochMillis: Long): String {
        val from = Date.from(java.time.Instant.ofEpochMilli(fromEpochMillis))
        val to = Date.from(java.time.Instant.ofEpochMilli(toEpochMillis))
        passcodeOperation(passcode, PermissionActionEnum.ADD_ENUM, from, to)
        return passcode
    }

    override suspend fun deletePasscode(passcode: String): String {
        passcodeOperation(passcode, PermissionActionEnum.DELETE_ENUM, null, null)
        return passcode
    }

    private suspend fun passcodeOperation(passcode: String, enum: PermissionActionEnum, from: Date?, to: Date?) {
        enablePinCodeFlag()
        val data = CustomLockPermissionData()
        val beginTime = from ?: Date.from(java.time.Instant.now().minus(10, ChronoUnit.HOURS))
        val endTime = to ?: Date.from(java.time.Instant.now().plus(10, ChronoUnit.HOURS))

        val permission2 = LockPermissionData.PermissionData(
            enum,
            passcode,
            PermissionTypeEnum.PIN_CODE_ENUM,
            beginTime,
            endTime,
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            true,
            false,
            false
        )

        // data.permissionData.add(permission)
        data.permissionData.add(permission2)

        sdk.updateLockPermission(data, false)

        updateLockPermissionFlow.first().result
    }

    override suspend fun openWithPasscode(accessKey: String, passcode: String) {
        val bytes = HexUtil.decodeHex(
            String.format("%-12s", passcode.trim { it <= ' ' }).replace(' ', 'F').toCharArray()
        )
        val code = LockCodeClass()

        if (accessKey.isNotBlank()) {
            code.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            code.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            code.sysCode = accessKey.toByteArray()
        }
        sdk.pinCodeOpenLock(code, passcode.toByteArray())
    }

    override suspend fun logs(last: Int): Flow<String> = logger.async {
        callbackFlow {
            val scope = CoroutineScope(currentCoroutineContext())

            val lockData = object : LockEventData() {
                override fun getLockEventFromPos(count: Int, date: Date?): Int {
                    return if (count > last) count - last else 1
                }

                override fun getLockEvent(count: Int, pos: Int, event: EventData?) {
                    if (!scope.isActive) return
                    scope.launch {
                        <EMAIL>("Event #$pos/$count: ${event?.string()}")
                        if (count == pos) <EMAIL>()
                    }
                }
            }

            sdk.readLockEvent(lockData, false)

            awaitClose()
        }
    }

    private suspend fun enablePinCodeFlag() = logger.async {
        sdk.getLockInfo()
        val info = lockInfoFlow.first().result

        info.tempPinCode = EnableEnum.ENABLE_ENUM
        sdk.setLockInfo(info)
        lockInfoFlow.first().result
    }

    override suspend fun pairKeypadWithLock(accessKey: String, lockName: String, macAddress: String) = logger.async {
        val code = LockCodeClass()
        code.ivCode = "8qli5ljbkBJSBip8".toByteArray()
        code.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
        code.sysCode = accessKey.toByteArray()
        sdk.setKeyboardInfo(lockName, macAddress, EnableEnum.ENABLE_ENUM, EnableEnum.ENABLE_ENUM, code)
        setKeyboardFlow.first().result
    }

    internal val authenticationFlow = MutableSharedFlow<AsyncResult<LockBasicInfo>>()
    internal val unlockFlow = MutableSharedFlow<AsyncResult<Boolean>>()
    internal val lockInfoFlow = MutableSharedFlow<AsyncResult<LockBasicInfo>>()
    internal val lockStatusFlow = MutableSharedFlow<AsyncResult<LockStatusData>>()
    internal val setLockTimeFlow = MutableSharedFlow<AsyncResult<LocalDateTime>>()
    internal val registerLockFlow = MutableSharedFlow<AsyncResult<Boolean>>()
    internal val resetLockFlow = MutableSharedFlow<AsyncResult<Boolean>>()
    internal val disconnectFlow = MutableSharedFlow<AsyncResult<Boolean>>()
    internal val updateLockPermissionFlow = MutableSharedFlow<AsyncResult<Boolean>>()
    internal val setKeyboardFlow = MutableSharedFlow<AsyncResult<String>>()

    private suspend fun <T> timed(
        timeout: Long = Constants.BLUETOOTH_TIMEOUT_MILLIS,
        job: suspend () -> AsyncResult<T>
    ): T {
        return withTimeoutOrNull(timeout) { job().result } ?: throw timeoutException()
    }
}