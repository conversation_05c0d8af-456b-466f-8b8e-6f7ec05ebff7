package core.lock.rayonics

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothManager
import android.content.Context
import core.http.download.Downloader
import core.lock.common.Constants
import core.lock.nordicfirmware.NordicFirmwareRepository
import core.lock.rayonics.models.AndroidRayonicsLock
import core.lock.rayonics.models.RayonicsLock
import core.lock.rayonics.models.RayonicsSearch
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan

class AndroidRayonicsManager(
    private val context: Application,
    private val logger: Logger,
    private val downloader: Downloader,
    private val nordic: NordicFirmwareRepository,
    dispatcher: CoroutineDispatcher
) : RayonicsManager {

    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(device: android.bluetooth.BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            if (
                device.name == null || (
                    device.name?.startsWith("RC") == false &&
                        device.name?.startsWith("REC") == false &&
                        device.name?.startsWith("RB") == false
                    )

            ) {
                return
            }
            val ble = AndroidRayonicsLock(
                name = device.name ?: "Bluetooth Device",
                macAddress = device.address,
                rssi = rssi,
                scanRecord = scanRecord,
                downloader = downloader,
                nordic = nordic,
                dispatcher = dispatcher,
                bluetoothManager = bluetoothManager,
                logger = logger,
                context = context,
                lastSeen = Clock.System.now().toEpochMilliseconds()
            )
            scope.launch { if (scope.isActive) bleScanFlow.update(ble) }
        }

        override fun finishScan() {}
    }

    private val bleScan = object : BluetoothLeScan(bluetoothManager.adapter, 0, bleScanCallback) {
        override fun enableBluetooth(): Boolean {
            return true
        }
    }

    private val scope = CoroutineScope(dispatcher)
    private val bleScanFlow = MutableStateFlow(RayonicsSearch(ArrayList()))

    private var listenJob: Job? = null

    override val stream: Flow<List<RayonicsLock>> = bleScanFlow
        .map { it.locks }
        .onStart { CoroutineScope(currentCoroutineContext()).launch { scan() } }
        .onCompletion { stopScan() }

    private suspend fun scan(timeoutInMillis: Long = Constants.BLUETOOTH_TIMEOUT_MILLIS): Unit = logger.async {
        bleScanFlow.emit(RayonicsSearch(ArrayList()))
        bleScan.startReceiver()
        listenJob = launch {
            while (isActive) {
                val now = Clock.System.now().toEpochMilliseconds()
                val locks = bleScanFlow.value.locks.filter { now - it.lastSeen <= 20000 }
                bleScanFlow.emit(RayonicsSearch(locks))
                delay(1000)
            }
        }
    }

    private fun stopScan(): Unit = logger.log {
        bleScan.stopReceiver()
        listenJob?.cancel()
    }

    override fun getByMac(mac: String): RayonicsLock = logger.log {
        return@log bleScanFlow.value.locks.first { it.macAddress == mac }
    }

    override fun getByName(name: String): RayonicsLock = logger.log {
        return@log bleScanFlow.value.locks.first { it.name == name }
    }

    private suspend fun MutableStateFlow<RayonicsSearch>.update(ble: AndroidRayonicsLock) {
        val list = value.locks.toMutableList()
        val index = list.indexOfFirst { it.macAddress == ble.macAddress }
        if (index == -1) list.add(ble) else list[index] = ble
        emit(RayonicsSearch(list))
    }
}