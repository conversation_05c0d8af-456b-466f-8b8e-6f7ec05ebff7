package core.lock.rayonics.models

import rayo.logicsdk.data.LockPermissionData
import java.util.Locale

class CustomLockPermissionData : LockPermissionData() {
    override fun findPermission(p0: PermissionData?) {
    }

    override fun setPermission(
        count: Int,
        pos: Int,
        p2: PermissionData?
    ) {
        println(String.format("POS:%d / Count:%d -- %s".lowercase(Locale.getDefault()), pos, count, p2.toString()))
    }
}