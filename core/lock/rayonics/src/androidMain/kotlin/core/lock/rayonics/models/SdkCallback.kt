package core.lock.rayonics.models

import core.common.error.toError
import core.common.platform.Platform
import core.common.result.AsyncResult
import core.lock.common.errors.lockException
import core.lock.common.toLocalDateTime
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import rayo.logicsdk.bean.LockTimeClass
import rayo.logicsdk.data.LockEventData
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean

internal val AndroidRayonicsLock.mBleLockSdkCallback
    get() = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>) {
            logger.log("Init result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun connect(resultBean: ResultBean<*>) {
            logger.log("Connect result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun authentication(resultBean: ResultBean<*>) {
            logger.log("Authentication result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) authenticationFlow.emit(resultBean.handle()) }
        }

        override fun disConnect(resultBean: ResultBean<*>) {
            logger.log("Disconnect result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (isActive) disconnectFlow.emit(resultBean.handle { true }) }
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            logger.log("Register lock result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) registerLockFlow.emit(resultBean.handle { true }) }
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
            logger.log("Lock info result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) lockInfoFlow.emit(resultBean.handle()) }
        }

        override fun setLockInfo(resultBean: ResultBean<*>) {
            logger.log("Set lock info result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) lockInfoFlow.emit(resultBean.handle()) }
        }

        override fun setLockTime(resultBean: ResultBean<*>) {
            logger.log("Set lock time result: ${resultBean.msg} ${resultBean.obj}")
            val time = resultBean.obj as LockTimeClass
            scope.launch {
                if (scope.isActive) setLockTimeFlow.emit(resultBean.handle { time.lockTime.toLocalDateTime() })
            }
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
            logger.log("Get lock office mode result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
            logger.log("Set lock office mode result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
            logger.log("Get lock hot point result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
            logger.log("Set lock hot point result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
            logger.log("Check lock hot point result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun getLockStatus(resultBean: ResultBean<*>) {
            logger.log("Get lock status result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) lockStatusFlow.emit(resultBean.handle()) }
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
            val events = (resultBean?.obj as? LockEventData)?.eventData
            logger.log(
                "Read lock event result: ${resultBean?.msg} ${(resultBean?.obj as? LockEventData)?.eventData?.count()}"
            )
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
            logger.log("Clean lock event result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun updateLockPermission(resultBean: ResultBean<*>) {
            logger.log("Update lock permission result: ${resultBean?.msg} ${resultBean?.obj}")
            scope.launch { if (scope.isActive) updateLockPermissionFlow.emit(resultBean.handle { resultBean.isRet }) }
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
            logger.log("Find lock permission result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
            logger.log("Clean lock permission result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            logger.log("Open lock result: ${resultBean.msg} ${resultBean.obj}")
            if (!resultBean.isRet) {
                val error = lockException(resultBean.msg).toError(Platform.executeLocation(), mapOf())
                scope.launch { if (scope.isActive) unlockFlow.emit(AsyncResult.Fail(error)) }
                return
            }
            scope.launch { if (scope.isActive) unlockFlow.emit(resultBean.handle { true }) }
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
            logger.log("Pincode open lock result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
            logger.log("Find lock blacklist result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
            logger.log("Set blacklist result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
            logger.log("Clean blacklist result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
            logger.log("Set calendar result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun resetLock(resultBean: ResultBean<*>) {
            logger.log("Reset lock result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) resetLockFlow.emit(resultBean.handle { true }) }
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
            logger.log("Set lock factory result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun resetLockFactory(resultBean: ResultBean<*>) {
            logger.log("Reset lock factory result: ${resultBean.msg} ${resultBean.obj}")
            scope.launch { if (scope.isActive) resetLockFlow.emit(resultBean.handle { true }) }
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
            logger.log("Set lock serial id result: ${resultBean?.msg}  ${resultBean?.obj}")
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
            logger.log("Read card by cylinder result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun onReport(resultBean: ResultBean<*>?) {
            logger.log("Report result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
            logger.log("Get reader info result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
            logger.log("Set reader serial id result: ${resultBean?.msg} ${resultBean?.obj}")
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
            logger.log("Get keyboard info result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>) {
            logger.log("Set keyboard info result: ${resultBean?.msg} ${resultBean?.obj}")
            scope.launch { if (scope.isActive) setKeyboardFlow.emit(resultBean.handle { resultBean.obj.toString() }) }
        }

        override fun disPlay(resultBean: ResultBean<*>) {
            logger.log("Display result: ${resultBean.msg} ${resultBean.obj}")
        }

        override fun setTempCard(p0: ResultBean<*>?) {
            logger.log("Set temp card result: ${p0?.msg} ${p0?.obj}")
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
            logger.log("Delete temp card result: ${p0?.msg} ${p0?.obj}")
        }

        override fun findTempCard(p0: ResultBean<*>?) {
            logger.log("Find temp card result: ${p0?.msg} ${p0?.obj}")
        }
    }

private fun <T> ResultBean<*>.handle(map: (() -> T?)? = null): AsyncResult<T> {
    return if (isRet) {
        AsyncResult.Success(map?.invoke() ?: this.obj as T)
    } else {
        AsyncResult.Fail(lockException("$msg with code: $obj"))
    }
}