package core.lock.ttlock.models

interface TTLock {
    val macAddress: String
    val name: String
    val rssi: Int
    val lastSeen: Long

    suspend fun unlock(lockData: String)

    suspend fun setPasscode(passcode: String, startDate: Long, endDate: Long, lockData: String)

    suspend fun editPasscode(oldPasscode: String, newPasscode: String, startDate: Long, endDate: Long, lockData: String)

    suspend fun deletePasscode(passcode: String, lockData: String)

    suspend fun resetLockTime(lockData: String)
}