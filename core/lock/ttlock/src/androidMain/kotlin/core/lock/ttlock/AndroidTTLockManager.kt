package core.lock.ttlock

import android.content.Context
import android.os.Looper
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ScanLockCallback
import com.ttlock.bl.sdk.entity.LockError
import core.common.error.KError
import core.common.error.toError
import core.common.platform.Platform
import core.lock.common.Constants
import core.lock.common.errors.LockException
import core.lock.ttlock.models.AndroidTTLock
import core.lock.ttlock.models.TTLock
import core.lock.ttlock.models.TTLockSearch
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

class AndroidTTLockManager(
    private val context: Context,
    private val logger: Logger,
    private val dispatcher: CoroutineDispatcher
) : TTLockManager {
    private val locksStream = MutableStateFlow(TTLockSearch(emptyList()))
    private val ttlock = TTLockClient.getDefault()

    override val stream: Flow<List<TTLock>> = locksStream
        .map { it.locks }
        .onStart { CoroutineScope(currentCoroutineContext()).launch { scan() } }
        .onCompletion { stopScan() }
    private val scope = CoroutineScope(dispatcher)
    private var listenJob: Job? = null

    private suspend fun scan(timeoutInMillis: Long = Constants.BLUETOOTH_TIMEOUT_MILLIS) = logger.async {
        try {
            Looper.prepare()
            ttlock.prepareBTService(context)
        } catch (ex: Exception) {
            ttlock.prepareBTService(context)
        }
        ttlock.startScanLock(
            object : ScanLockCallback {
                override fun onFail(p0: LockError?) = throw p0.toError().toError(Platform.executeLocation(), mapOf())

                override fun onScanLockSuccess(p0: ExtendedBluetoothDevice?) {
                    if (p0 == null) return else if (scope.isActive) scope.launch { locksStream.update(p0.toTTLock()) }
                }
            }
        )

        listenJob = launch {
            while (isActive) {
                val now = Clock.System.now().toEpochMilliseconds()
                val locks = locksStream.value.locks.filter { now - it.lastSeen <= 20000 }
                locksStream.emit(TTLockSearch(locks))
                delay(1000)
            }
        }
    }

    private fun stopScan() = logger.log {
        ttlock.stopScanLock()
        listenJob?.cancel()
    }

    override fun getByMac(mac: String): TTLock {
        return locksStream.value.locks.first { it.macAddress == mac }
    }

    override fun getByName(name: String): TTLock {
        return locksStream.value.locks.first { it.name == name }
    }

    private suspend fun MutableStateFlow<TTLockSearch>.update(ble: AndroidTTLock) {
        val list = value.locks.toMutableList()
        val index = list.indexOfFirst { it.macAddress == ble.macAddress }
        if (index == -1) list.add(ble) else list[index] = ble
        emit(TTLockSearch(list))
    }

    private fun ExtendedBluetoothDevice.toTTLock(): AndroidTTLock {
        return AndroidTTLock(
            macAddress = this.address,
            name = this.name,
            rssi = this.rssi,
            lastSeen = Clock.System.now().toEpochMilliseconds(),
            rawLock = this,
            ttlock = ttlock,
            logger = logger
        )
    }
}

internal fun LockError?.toError(): KError.Fatal {
    val msg = if (this?.name?.lowercase()?.contains("ble_server_not_init") == true) {
        "Need to enable Bluetooth to perform this action"
    } else {
        "${this?.name ?: ""} ${this?.description ?: "Lock error"}"
    }
    return LockException(
        message = msg,
        location = Platform.executeLocation(),
        details = mapOf()
    )
}