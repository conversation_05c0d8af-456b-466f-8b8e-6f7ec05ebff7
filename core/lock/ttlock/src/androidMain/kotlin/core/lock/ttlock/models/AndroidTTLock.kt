package core.lock.ttlock.models

import android.os.Looper
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ControlLockCallback
import com.ttlock.bl.sdk.callback.CreateCustomPasscodeCallback
import com.ttlock.bl.sdk.callback.DeletePasscodeCallback
import com.ttlock.bl.sdk.callback.ModifyPasscodeCallback
import com.ttlock.bl.sdk.callback.SetLockTimeCallback
import com.ttlock.bl.sdk.constant.ControlAction
import com.ttlock.bl.sdk.entity.ControlLockResult
import com.ttlock.bl.sdk.entity.LockError
import core.lock.ttlock.toError
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.datetime.Clock
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Suppress("InconsistentCommentForJavaParameter")
data class AndroidTTLock(
    override val macAddress: String,
    override val name: String,
    override val rssi: Int,
    override val lastSeen: Long,
    private val rawLock: ExtendedBluetoothDevice,
    private val ttlock: TTLockClient,
    private val logger: Logger
) : TTLock {

    override suspend fun resetLockTime(lockData: String): Unit = logger.async {
        Companion.resetLockTime(lockData)
    }

    override suspend fun unlock(lockData: String) = logger.async {
        Companion.unlock(lockData)
    }

    override suspend fun setPasscode(
        passcode: String,
        startDate: Long,
        endDate: Long,
        lockData: String
    ) = logger.async {
        Companion.setPasscode(passcode = passcode, startDate = startDate, endDate = endDate, lockData = lockData)
    }

    override suspend fun editPasscode(
        oldPasscode: String,
        newPasscode: String,
        startDate: Long,
        endDate: Long,
        lockData: String
    ) = logger.async {
        Companion.editPasscode(
            oldPasscode = oldPasscode,
            newPasscode = newPasscode,
            startDate = startDate,
            endDate = endDate,
            lockData = lockData
        )
    }

    override suspend fun deletePasscode(
        passcode: String,
        lockData: String
    ) = logger.async {
        Companion.deletePasscode(passcode = passcode, lockData = lockData)
    }

    companion object {
        suspend fun resetLockTime(lockData: String) {
            return suspendCancellableCoroutine { continuation ->
                TTLockClient
                    .getDefault()
                    .setLockTime(
                        Clock.System.now().toEpochMilliseconds(),
                        lockData,
                        object : SetLockTimeCallback {
                            override fun onSetTimeSuccess() = continuation.resume(Unit)
                            override fun onFail(error: LockError) = continuation.resumeWithException(error.toError())
                        }
                    )
            }
        }

        suspend fun unlock(lockData: String) {
            suspendCancellableCoroutine { continuation ->
                runCatching { Looper.prepare() }
                TTLockClient
                    .getDefault()
                    .controlLock(
                        ControlAction.UNLOCK,
                        lockData,
                        object : ControlLockCallback {
                            override fun onFail(p0: LockError?) = continuation.resumeWithException(p0.toError())
                            override fun onControlLockSuccess(p0: ControlLockResult?) {
                                if (p0 == null) return else continuation.resume(Unit)
                            }
                        }
                    )
            }
        }

        suspend fun setPasscode(
            passcode: String,
            startDate: Long,
            endDate: Long,
            lockData: String
        ) {
            return suspendCancellableCoroutine { continuation ->
                runCatching { Looper.prepare() }
                TTLockClient
                    .getDefault()
                    .createCustomPasscode(
                        /* passcode = */ passcode,
                        /* startDate = */ startDate,
                        /* endDate = */ endDate,
                        /* lockData = */ lockData,
                        /* callback = */ object : CreateCustomPasscodeCallback {
                            override fun onCreateCustomPasscodeSuccess(passcode: String) = continuation.resume(Unit)
                            override fun onFail(error: LockError) = continuation.resumeWithException(error.toError())
                        }
                    )
            }
        }

        suspend fun editPasscode(
            oldPasscode: String,
            newPasscode: String,
            startDate: Long,
            endDate: Long,
            lockData: String
        ) {
            suspendCancellableCoroutine { continuation ->
                runCatching { Looper.prepare() }
                TTLockClient
                    .getDefault()
                    .modifyPasscode(
                        /* oldPasscode = */ oldPasscode,
                        /* newPasscode = */ newPasscode,
                        /* startDate = */ startDate,
                        /* endDate = */ endDate,
                        /* lockData = */ lockData,
                        /* callback = */ object : ModifyPasscodeCallback {
                            override fun onModifyPasscodeSuccess() = continuation.resume(Unit)
                            override fun onFail(error: LockError) = continuation.resumeWithException(error.toError())
                        }
                    )
            }
        }

        suspend fun deletePasscode(
            passcode: String,
            lockData: String
        ) {
            suspendCancellableCoroutine { continuation ->
                runCatching { Looper.prepare() }
                TTLockClient
                    .getDefault()
                    .deletePasscode(
                        /* passcode = */ passcode,
                        /* lockData = */ lockData,
                        /* callback = */ object : DeletePasscodeCallback {
                            override fun onDeletePasscodeSuccess() = continuation.resume(Unit)
                            override fun onFail(error: LockError) = continuation.resumeWithException(error.toError())
                        }
                    )
            }
        }
    }
}