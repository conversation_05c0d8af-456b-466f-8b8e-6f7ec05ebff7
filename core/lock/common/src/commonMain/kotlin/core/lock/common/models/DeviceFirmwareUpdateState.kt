package core.lock.common.models

sealed interface DeviceFirmwareUpdateState {
    val device: String

    data class Idle(override val device: String) : DeviceFirmwareUpdateState {
        override fun toString(): String {
            return "Firmware: Idle(device='$device')"
        }
    }

    data class InProgress(val progress: Double, override val device: String) : DeviceFirmwareUpdateState {
        override fun toString(): String {
            return "Firmware: InProgress(progress=$progress, device='$device')"
        }
    }

    data class Error(val message: String, override val device: String) : DeviceFirmwareUpdateState {
        override fun toString(): String {
            return "Firmware: Error(message='$message', device='$device')"
        }
    }

    data class Finished(override val device: String) : DeviceFirmwareUpdateState {
        override fun toString(): String {
            return "Firmware: Finished(device='$device')"
        }
    }
}