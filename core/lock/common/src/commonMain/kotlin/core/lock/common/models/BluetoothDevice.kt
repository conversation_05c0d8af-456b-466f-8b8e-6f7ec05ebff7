package core.lock.common.models

data class BluetoothDevice(
    val name: String,
    val macAddress: String,
    val rssi: Int,
    val payloadData: ByteArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as BluetoothDevice

        if (name != other.name) return false
        if (macAddress != other.macAddress) return false
        if (rssi != other.rssi) return false
        if (!payloadData.contentEquals(other.payloadData)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = name.hashCode()
        result = 31 * result + macAddress.hashCode()
        result = 31 * result + rssi
        result = 31 * result + payloadData.contentHashCode()
        return result
    }
}