package core.lock.common.errors

import core.common.error.KError
import core.common.message.Message
import core.common.platform.Platform

open class LockException(
    location: String,
    cause: Exception,
    display: Message,
    details: Map<String, Any>
) : KError.Fatal(cause = cause, message = display, callLocation = location, details = details) {

    constructor(
        location: String,
        message: String,
        details: Map<String, Any>
    ) : this(location = location, cause = Exception(message), display = Message.fromString(message), details = details)

    constructor(
        cause: Exception,
        location: String,
        details: Map<String, Any>
    ) : this(
        location = location,
        cause = cause,
        display = Message.fromString(cause.message ?: "Lock error"),
        details = details
    )

    constructor(
        cause: Exception,
        message: String,
        location: String,
        details: Map<String, Any>
    ) : this(
        location = location,
        cause = cause,
        display = Message.fromString(message),
        details = details
    )
}

class LockTimeoutException(
    location: String,
    message: String,
    details: Map<String, Any>
) : LockException(message = message, location = location, details = details)

class LockConnectionException(
    location: String,
    message: String,
    details: Map<String, Any>
) : LockException(message = message, location = location, details = details)

class LockUnlockException(
    location: String,
    message: String,
    details: Map<String, Any>
) : LockException(message = message, location = location, details = details)

inline fun lockException(
    message: String,
    details: Map<String, Any> = mapOf()
): LockException {
    return LockException(location = Platform.executeLocation(), message = message, details = details)
}

inline fun lockException(
    cause: Exception,
    details: Map<String, Any> = mapOf()
): LockException {
    return LockException(location = Platform.executeLocation(), cause = cause, details = details)
}

inline fun lockException(
    message: String,
    cause: Exception,
    details: Map<String, Any> = mapOf()
): LockException {
    return LockException(location = Platform.executeLocation(), cause = cause, message = message, details = details)
}

inline fun timeoutException(
    message: String = "Lock operation timed out",
    details: Map<String, Any> = mapOf()
): LockTimeoutException {
    return LockTimeoutException(location = Platform.executeLocation(), message = message, details = details)
}

inline fun connectionException(
    message: String = "Lock connection failed",
    details: Map<String, Any> = mapOf()
): LockConnectionException {
    return LockConnectionException(location = Platform.executeLocation(), message = message, details = details)
}

inline fun unlockException(
    message: String = "Lock unlock failed",
    details: Map<String, Any> = mapOf()
): LockUnlockException {
    return LockUnlockException(location = Platform.executeLocation(), message = message, details = details)
}