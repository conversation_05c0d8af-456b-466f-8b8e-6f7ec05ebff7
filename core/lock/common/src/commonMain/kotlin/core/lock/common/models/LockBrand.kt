package core.lock.common.models

enum class Lock<PERSON>rand {
    Rayonics {
        override fun toString() = "Rayonics"
    },
    <PERSON><PERSON> {
        override fun toString() = "Keyless"
    },
    ISEO {
        override fun toString() = "ISEO"
    },
    LockWise {
        override fun toString() = "LockWise"
    },
    Messerschmitt {
        override fun toString() = "Messerschmitt"
    },
    TTLock {
        override fun toString() = "TTLock"
    },
    <PERSON>ji {
        override fun toString() = "Oji"
    },
    <PERSON><PERSON> {
        override fun toString() = "Linko"
    },
    Lock;

    companion object {
        fun fromString(value: String) = values().find { it.toString().lowercase() == value.lowercase() } ?: Lock
    }
}