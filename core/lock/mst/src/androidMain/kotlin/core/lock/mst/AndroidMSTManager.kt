package core.lock.mst

import android.content.Context
import core.common.lifecycle.Lifecycle
import core.lock.mst.models.AndroidMSTLock
import core.lock.mst.models.MSTLock
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.permissions.manager.BluetoothManager
import core.permissions.manager.models.BluetoothDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class AndroidMSTManager(
    private val applicationContent: Context,
    private val logger: Logger,
    private val lifecycle: Lifecycle,
    private val bluetooth: BluetoothManager
) : MSTManager {

    override val stream: Flow<List<BluetoothDevice>> = bluetooth
        .allBluetoothStream
        .map { it.filter { it.name.length == 8 } }

    override suspend fun getLock(
        accessKey: String,
        deviceUid: String,
        uniqueKey: String
    ): MSTLock = logger.async(Dispatchers.Default) {
        return@async AndroidMSTLock(
            applicationContext = applicationContent,
            accessKey = accessKey,
            deviceUid = deviceUid,
            uniqueKey = uniqueKey,
            lifecycle = lifecycle,
            logger = logger
        )
    }
}