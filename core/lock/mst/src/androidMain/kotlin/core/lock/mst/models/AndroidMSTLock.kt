package core.lock.mst.models

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.messerschmitt.mstblelib.MSTBleDevice
import com.messerschmitt.mstblelib.MSTBleInterface
import com.messerschmitt.mstblelib.MSTBleUtils
import com.messerschmitt.mstblelib.MSTsmartkey
import core.common.lifecycle.Lifecycle
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

class AndroidMSTLock(
    private val applicationContext: Context,
    private val logger: Logger,
    private val lifecycle: Lifecycle,
    private val accessKey: String,
    private val deviceUid: String,
    private val uniqueKey: String
) : MSTLock {

    private val TIMEOUT = 60_000L
    private val connectFlow = MutableSharedFlow<Result<Boolean>>()
    private val unlockFlow = MutableSharedFlow<Boolean>()
    private var device: MSTBleDevice? = null
    private val key = MSTsmartkey(accessKey, deviceUid.encodeToByteArray())
    private val utils = MSTBleUtils(lifecycle.activity, -85, -75)
    private val mst = runBlocking(Dispatchers.Main) { MSTBleInterface(utils, lifecycle.context, key) }
    private var connectRetry = 0

    private suspend fun connect() = logger.async {
        var result = false

        withTimeoutOrNull(TIMEOUT) {
            while (connectRetry != 3) {
                withContext(Dispatchers.Main) { mst.mstOpenDoor(uniqueKey) }

                val scan = connectFlow.collectFirst()
                if (scan.exceptionOrNull() is ScanTimeoutException) {
                    connectRetry += 1
                    delay(5000)
                } else {
                    result = scan.getOrNull() ?: false
                    break
                }
            }
        }
        connectRetry = 0
        if (!result) throw Exception("Couldn't connect to lock")
    }

    override suspend fun unlock() = logger.async {
        connect()
        delay(1000)
        withContext(Dispatchers.Main) { mst.openDoor(device) }
        val result = withTimeoutOrNull(TIMEOUT) { unlockFlow.collectFirst() } ?: false
        if (!result) throw Exception("Couldn't unlock lock")
    }

    private val MSTBleStatusChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val data = intent.getParcelableExtra<MSTBleDevice>("data")

            if (action == MSTBleInterface.ACTION_CONNECT_TO) {
                device = data
                runBlocking { connectFlow.emit(Result.success(device != null)) }
            }

            if (action == MSTBleInterface.ACTION_SCAN_TIMEOUT) {
                runBlocking { connectFlow.emit(Result.failure(ScanTimeoutException())) }
            }

            if (action == MSTBleInterface.ERROR_CONNECT) {
                runBlocking { connectFlow.emit(Result.success(false)) }
            }

            if (action == MSTBleInterface.ACTION_OPEN_THE_DOOR) {
                runBlocking { unlockFlow.emit(true) }
            }
        }
    }

    private fun makeGattUpdateIntentFilter(): IntentFilter {
        val intentFilter = IntentFilter()
        intentFilter.addAction(MSTBleInterface.ACTION_START_OPEN)
        intentFilter.addAction(MSTBleInterface.ACTION_SCAN_TIMEOUT)
        intentFilter.addAction(MSTBleInterface.ACTION_CONNECT_TO)
        intentFilter.addAction(MSTBleInterface.ACTION_SPECIAL_NAME)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR_FAILED)
        intentFilter.addAction(MSTBleInterface.ACTION_FINISH_OPEN)
        intentFilter.addAction(MSTBleInterface.ACTION_DATE_TIME)
        intentFilter.addAction(MSTBleInterface.ACTION_BATT_STATUS)
        intentFilter.addAction(MSTBleInterface.ERROR_DEVICE_RESPONSE)
        intentFilter.addAction(MSTBleInterface.ERROR_DISCOVER_SERVICES)
        intentFilter.addAction(MSTBleInterface.ERROR_CONNECT)
        intentFilter.addAction(MSTBleInterface.ERROR_DOOR_STATE)
        intentFilter.addAction(MSTBleInterface.ERROR_KEY_RESULT)
        intentFilter.addAction(MSTBleInterface.ERROR_ENABLE_NOTIFY)
        intentFilter.addAction(MSTBleInterface.ERROR_AUTH_FAILED)
        return intentFilter
    }

    private fun registerReceiver() {
        LocalBroadcastManager
            .getInstance(lifecycle.context)
            .registerReceiver(MSTBleStatusChangeReceiver, makeGattUpdateIntentFilter())
    }

    private fun unRegisterReceiver() {
        LocalBroadcastManager
            .getInstance(lifecycle.context)
            .unregisterReceiver(MSTBleStatusChangeReceiver)
    }

    private suspend fun <T> Flow<T>.collectFirst(): T {
        return onStart { registerReceiver() }.onCompletion { unRegisterReceiver() }.first()
    }

    private fun CoroutineScope.launch(delay: Long, block: suspend () -> Unit) = launch {
        delay(delay)
        block()
    }
}