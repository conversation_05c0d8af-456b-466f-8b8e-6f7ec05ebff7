package core.lock.iseo.models

import android.app.Application
import com.iseo.v364droidsdk.service.mobilecredentialservice.DroidMobileCredentialServiceFactory
import com.iseo.v364sdk.services.mobilecredentialservice.model.ILock
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.state.ErrorCode
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import core.caching.KeyValueCache
import core.common.error.KError
import core.common.error.toError
import core.common.platform.Platform
import core.lock.common.errors.lockException
import core.lock.iseo.IseoHelper
import core.lock.iseo.handler
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay

class AndroidIseoLock(
    private val context: Application,
    private val scanInfo: ILockScanInfo,
    private val settings: KeyValueCache,
    private val logger: Logger,
    private val scanService: IScanManagerService,

    override val name: String,
    override val macAddress: String,
    override val rssi: Int,
    override val lastSeen: Long
) : IseoLock {

    private val serviceFactory = DroidMobileCredentialServiceFactory(scanService)
    private val credentialsService = serviceFactory.createMobileCredentialService()
    private val connectService = serviceFactory.createMobileConnLockService()
    private val unlockService = serviceFactory.createMobileUserCmdService()
    private val hermesService = serviceFactory.createMobileHermesCmdService()

    private var connectedLock: ILock? = null

    init {
        IseoHelper.setContext(context)
        runCatching {
            credentialsService.refreshMobileCredential(
                settings.getString(IseoHelper.ISEO_URL_KEY, ""),
                settings.getString(IseoHelper.PLANT_NAME_KEY, "")
            )
        }.onFailure {
            logger.error(it.toError(Platform.executeLocation(), mapOf()) as KError.Fatal)
        }
    }

    override suspend fun unlock() = logger.async {
        connect()
        val response = handler(logger = logger, job = { unlockService.openLock(connectedLock) })

        if (response.errorCode?.code == ErrorCode.OUTOFDATEVALIDITY) {
            val error = lockException("Time Expired, Please Contact to Admin.")
            logger.error(error)
            throw error
        } else if (response.errorCode?.code == ErrorCode.OUTOFTIMERANGE) {
            val error = lockException("Out Of Time Range, Please Contact To Admin.")
            logger.error(error)
            throw error
        } else if (response.errorCode != null && response.errorCode.code != ErrorCode.SUCCESS) {
            val error = lockException("ISEO Lock error: ${response.errorCode.code.name}")
            logger.error(error)
            throw error
        }
    }

    override suspend fun resetLockTime() = logger.async {
        handler(
            logger = logger,
            job = {
                val lock = connectedLock ?: connectService.connect(scanInfo)
                hermesService.writeClock(lock)
            }
        )
    }

    override suspend fun getLockInfoString(): String = logger.async {
        handler(
            logger = logger,
            job = {
                val lock = connectedLock ?: connectService.connect(scanInfo)
                return@handler hermesService.readDeviceInfo(lock).string()
            }
        )
    }

    private suspend fun connect() = logger.async {
        return@async handler(
            logger = logger,
            job = {
                connectedLock = connectService.connect(scanInfo)
            }
        )
    }

    override suspend fun refreshCredentials() = logger.async(currentCoroutineContext() + Dispatchers.IO) {
        val url = settings.getString(IseoHelper.ISEO_URL_KEY, "")
        val plantName = settings.getString(IseoHelper.PLANT_NAME_KEY, "")

        val credentialsBefore = credentialsService.getMobileCredentials(url).mobileCredentials.forEach {
            println("[LOGGY] Credentials before: ${it.gateName}")
        }
        credentialsService.refreshMobileCredential(
            /* p0 = */ url,
            /* p1 = */ plantName
        )

        val credentialsAfter = credentialsService.getMobileCredentials(url).mobileCredentials.forEach {
            println("[LOGGY] Credentials after: ${it.gateName}")
        }
    }

    override suspend fun refreshCredentials(
        iseoUrl: String,
        plantName: String
    ) = logger.async(currentCoroutineContext() + Dispatchers.IO) {
        IseoHelper.saveCredentials(settings, iseoUrl, plantName)
        credentialsService.refreshMobileCredential(iseoUrl, plantName)
    }

    override suspend fun enterInMaintenanceMode() = logger.async(
        currentCoroutineContext() + Dispatchers.IO
    ) {
        delay(1000)
        refreshCredentials()
        delay(500)
        connect()
        hermesService.enterInMaintenanceMode(connectedLock)
    }

    override suspend fun exitFromMaintenanceMode() = logger.async(
        currentCoroutineContext() + Dispatchers.IO
    ) {
        if (connectedLock == null) connect()
        hermesService.disconnect(connectedLock)
        connectedLock = null
        delay(1000)
        refreshCredentials()
    }
}