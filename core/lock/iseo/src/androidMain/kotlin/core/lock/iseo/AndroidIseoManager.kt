package core.lock.iseo

import android.app.Application
import com.iseo.v364droidsdk.service.scanservice.DroidScanManagerServiceFactory
import com.iseo.v364sdk.services.scanservice.model.IKeyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILegacyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo
import com.iseo.v364sdk.services.scanservice.model.IScanBTManagerEvent
import core.caching.KeyValueCache
import core.lock.common.Constants
import core.lock.iseo.models.AndroidIseoLock
import core.lock.iseo.models.IseoLock
import core.lock.iseo.models.IseoSearch
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

class AndroidIseoManager(
    private val context: Application,
    private val logger: Logger,
    private val settings: KeyValueCache,
    private val dispatcher: CoroutineDispatcher
) : IseoManager, IScanBTManagerEvent {
    private val droidScanManagerServiceFactory = DroidScanManagerServiceFactory()
    private val scanService = droidScanManagerServiceFactory.createScanManager().apply {
        setScanBTManagerEvent(this@AndroidIseoManager)
    }
    private val scope = CoroutineScope(dispatcher)

    private val locks = MutableStateFlow<IseoSearch>(IseoSearch(arrayListOf()))
    override val stream: Flow<List<IseoLock>> = locks
        .map { it.locks }
        .onStart { CoroutineScope(currentCoroutineContext()).launch { scan() } }
        .onCompletion { stopScan() }

    private var listenJob: Job? = null

    private suspend fun scan(timeoutInMillis: Long = Constants.BLUETOOTH_TIMEOUT_MILLIS) = logger.async {
        locks.update { IseoSearch(arrayListOf()) }
        handler(logger = logger, job = { scanService.startScanLock(false) })
        listenJob = launch {
            while (isActive) {
                val now = Clock.System.now().toEpochMilliseconds()
                val iseos = locks.value.locks.filter { now - it.lastSeen <= 20000 }
                locks.emit(IseoSearch(iseos))
                delay(1000)
            }
        }
        Unit
    }

    private fun stopScan() = logger.log {
        handler(logger = logger, job = { scanService.stopScan() })
        listenJob?.cancel()
        Unit
    }

    override fun getByMac(mac: String): IseoLock {
        return locks.value.locks.find { it.macAddress == mac } ?: throw NoSuchElementException("No lock with mac $mac")
    }

    override fun getByName(name: String): IseoLock {
        return locks.value.locks.find { it.name == name } ?: throw NoSuchElementException("No lock with name $name")
    }

    override fun onScanStarted() {
        logger.log("Iseo Scan started")
    }

    override fun onScanStopped() {
        logger.log("Iseo Scan stopped")
    }

    override fun onF9000Found(p0: IKeyScanInfo?) {
        logger.log("Iseo F9000 found")
    }

    override fun onLockFound(scanInfo: ILockScanInfo?, info: IMobileCredentialScanInfo?) {
        if (scanInfo == null) return
        val lock = AndroidIseoLock(
            context = context,
            logger = logger,
            scanInfo = scanInfo,
            settings = settings,
            scanService = scanService,
            name = scanInfo.lockName,
            macAddress = scanInfo.macAddress,
            rssi = scanInfo.rssi,
            lastSeen = Clock.System.now().toEpochMilliseconds()
        )
        scope.launch { locks.update(lock) }
    }

    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {
        logger.log("Iseo Legacy device found")
    }

    private suspend fun MutableStateFlow<IseoSearch>.update(lock: AndroidIseoLock) = logger.async {
        val list = value.locks.toMutableList()
        val index = list.indexOfFirst { it.macAddress == lock.macAddress }
        if (index == -1) list.add(lock) else list[index] = lock
        emit(IseoSearch(list))
    }
}