package core.lock.iseo.models

interface IseoLock {

    val macAddress: String
    val name: String
    val rssi: Int
    val lastSeen: Long

    suspend fun refreshCredentials()

    suspend fun enterInMaintenanceMode()

    suspend fun refreshCredentials(iseoUrl: String, plantName: String)

    suspend fun exitFromMaintenanceMode()

    suspend fun getLockInfoString(): String

    suspend fun unlock()

    suspend fun resetLockTime()
}