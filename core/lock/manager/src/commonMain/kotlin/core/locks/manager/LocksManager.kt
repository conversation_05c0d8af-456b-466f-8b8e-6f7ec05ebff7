package core.locks.manager

import core.location.common.Location
import core.location.common.LocationRepository
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.models.AirBnkLock
import core.lock.common.models.LockBrand
import core.lock.iseo.IseoManager
import core.lock.iseo.models.IseoLock
import core.lock.mst.MSTManager
import core.lock.mst.models.MSTLock
import core.lock.rayonics.RayonicsManager
import core.lock.rayonics.models.RayonicsLock
import core.lock.ttlock.TTLockManager
import core.lock.ttlock.models.TTLock
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import core.permissions.manager.BluetoothManager
import core.permissions.manager.NetworkManager
import data.tedee.repositories.TedeeRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach

class LocksManager(
    private val airBnk: AirBnkManager,
    private val iseo: IseoManager,
    private val rayonics: RayonicsManager,
    private val ttlock: TTLockManager,
    private val tedee: TedeeRepository,
    private val bluetooth: BluetoothManager,
    private val network: NetworkManager,
    private val location: LocationRepository,
    private val mst: MSTManager,
    val logger: Logger
) {

    private var rayonicsLocks = listOf<RayonicsLock>()
    private var iseoLocks = listOf<IseoLock>()
    private var ttLocks = listOf<TTLock>()

    fun scan(
        bluetoothNames: List<String> = listOf(),
        lockBrands: List<LockBrand> = listOf(
            LockBrand.Rayonics,
            LockBrand.ISEO,
            LockBrand.TTLock,
            LockBrand.Messerschmitt
        )
    ): Flow<BluetoothLock> = logger.log {
        var locks = listOf<BluetoothLock>()

        val combined = getFlow(bluetoothNames, lockBrands)
            .map {
                val lock = compare(locks, it)
                locks = compare(locks, lock)
                lock
            }
            .filterNotNull()

        return@log combined
    }

    fun getRayonicsLock(uniqueKey: String): RayonicsLock? = logger.log {
        return@log rayonicsLocks.find { it.name == uniqueKey }
    }

    fun getIseoLock(uniqueKey: String): IseoLock? = logger.log {
        return@log iseoLocks.find { it.name == uniqueKey }
    }

    fun getTTLock(uniqueKey: String): TTLock? = logger.log {
        return@log ttLocks.find { it.name == uniqueKey }
    }

    suspend fun getAirBnkLock(accessKey: String): AirBnkLock = logger.async {
        return@async airBnk.connect(accessKey)
    }

    suspend fun getMSTLock(accessKey: String, lockUid: String, uniqueKey: String): MSTLock = logger.async {
        return@async mst.getLock(accessKey = accessKey, deviceUid = lockUid, uniqueKey = uniqueKey)
    }

    fun getTedeeRepository(): TedeeRepository {
        return tedee
    }

    suspend fun getTedeeLock(lockId: String, authentication: String, role: String) = logger.async {
        return@async tedee.sync(lockId = lockId, authentication = authentication, role = role)
    }

    suspend fun bluetoothCheck(): Boolean = logger.async {
        return@async bluetooth.enableBluetooth()
    }

    suspend fun networkCheck(): Boolean = logger.async {
        return@async network.enableNetwork()
    }

    suspend fun getLocation(): Location = logger.async {
        return@async location.getCurrentLocation()
    }

    private fun compare(locks: List<BluetoothLock>, new: List<BluetoothLock>): BluetoothLock? {
        for (newLock in new) {
            val oldLock = locks.find { it.macAddress == newLock.macAddress }

            if (oldLock == null || newLock.lastSeen > oldLock.lastSeen) {
                return newLock
            }
        }

        return null
    }

    private fun compare(locks: List<BluetoothLock>, lock: BluetoothLock?): List<BluetoothLock> {
        if (lock == null) return locks
        val index = locks.indexOfFirst { it.macAddress == lock.macAddress }

        return if (index == -1) {
            locks + lock
        } else {
            locks.toMutableList().apply {
                removeAt(index)
                add(index, lock)
            }
        }
    }

    private fun getFlow(
        bluetoothNames: List<String>,
        lockBrands: List<LockBrand>
    ): Flow<List<BluetoothLock>> {
        val rayonicsLocks = rayonics
            .stream.drop(1).onEach { this.rayonicsLocks = it }.map { it.map { it.toBluetoothLock() } }
        val iseoLocks = iseo.stream.drop(1).onEach { this.iseoLocks = it }.map { it.map { it.toBluetoothLock() } }
        val ttLocks = ttlock.stream.drop(1).onEach { this.ttLocks = it }.map { it.map { it.toBluetoothLock() } }

        val bluetoothLocks = bluetooth
            .leBluetoothStream
            .filter { it.name.length == 8 || bluetoothNames.contains(it.name) }
            .map { listOf(it.toBluetoothLock(LockBrand.Messerschmitt)) }

        var merged = flowOf<List<BluetoothLock>>()

        if (lockBrands.contains(LockBrand.Rayonics)) {
            merged = merge(rayonicsLocks, merged)
        }
        if (lockBrands.contains(LockBrand.ISEO)) {
            merged = merge(iseoLocks, merged)
        }
        if (lockBrands.contains(LockBrand.TTLock)) {
            merged = merge(ttLocks, merged)
        }
        if (lockBrands.contains(LockBrand.Messerschmitt)) {
            merged = merge(bluetoothLocks, merged)
        }

        return merged
    }
}