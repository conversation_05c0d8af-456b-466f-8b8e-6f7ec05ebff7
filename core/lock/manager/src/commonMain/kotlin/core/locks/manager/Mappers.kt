package core.locks.manager

import core.lock.common.models.LockBrand
import core.lock.iseo.models.IseoLock
import core.lock.rayonics.models.RayonicsLock
import core.lock.ttlock.models.TTLock
import core.permissions.manager.models.BluetoothDevice
import kotlinx.datetime.Clock

internal fun RayonicsLock.toBluetoothLock(): BluetoothLock {
    return BluetoothLock(
        name = name,
        macAddress = macAddress,
        rssi = rssi,
        lastSeen = lastSeen,
        brand = LockBrand.Rayonics
    )
}

internal fun IseoLock.toBluetoothLock(): BluetoothLock {
    return BluetoothLock(
        name = name,
        macAddress = macAddress,
        rssi = rssi,
        lastSeen = lastSeen,
        brand = LockBrand.ISEO
    )
}

internal fun TTLock.toBluetoothLock(): BluetoothLock {
    return BluetoothLock(
        name = name,
        macAddress = macAddress,
        rssi = rssi,
        lastSeen = lastSeen,
        brand = LockBrand.TTLock
    )
}

internal fun BluetoothDevice.toBluetoothLock(brand: <PERSON><PERSON><PERSON>): BluetoothLock {
    return BluetoothLock(
        name = name,
        macAddress = macAddress,
        rssi = rssi,
        lastSeen = Clock.System.now().toEpochMilliseconds(),
        brand = brand
    )
}