package core.locks.logs.repostiories

import core.http.client.HttpResponse
import core.locks.logs.models.LockAccessLog
import core.locks.logs.models.LockLog

interface LockLogsRemoteRepository {

    suspend fun syncLogs(
        logs: List<LockLog>,
        internalId: String,
        deviceType: String = "Android",
        authentication: String
    ): HttpResponse

    suspend fun lockAccessLog(
        logs: List<LockAccessLog>,
        authentication: String
    ): HttpResponse
}