package core.locks.logs.repostiories

import core.locks.logs.models.LockAccessLog
import core.locks.logs.models.LockLog

interface LockLogsLocalRepository {

    suspend fun insert(
        actionType: String,
        lockInfoData: String,
        lockInternalId: String,
        message: String,
        provider: String,
        user: String
    )

    suspend fun getAllByInternalId(internalId: String): List<LockLog>

    suspend fun deleteById(id: Long)

    suspend fun logLockAccess(log: LockAccessLog)

    suspend fun getAllAccessLogs(): List<LockAccessLog>

    suspend fun deleteAccessLogs()
}