package core.locks.logs.models

import kotlinx.serialization.Serializable

@Serializable
data class LockLog(
    val localId: Long,

    val actionType: String,
    val lockInfoData: String,
    val lockInternalId: String,
    val message: String,
    val provider: String,
    val timeStampDateInMillis: Long,
    val user: String
)

enum class LockLogActionType {
    Connected {
        override fun toString(): String = "Connected"
    },
    Maintenance {
        override fun toString(): String = "Maintenance"
    },
    Unlock {
        override fun toString(): String = "Unlock"
    },
    UnlockError {
        override fun toString(): String = "Unlock Error"
    },
    ScanLock {
        override fun toString(): String = "Scan Lock"
    },
    ScanError {
        override fun toString(): String = "Scan Error"
    },
    RoutineHours {
        override fun toString(): String = "Routine Hours"
    },
    SelectCard {
        override fun toString(): String = "Select Card"
    },
    AddLock {
        override fun toString(): String = "Add Lock"
    },
    SetTime {
        override fun toString(): String = "Set Time"
    };

    fun fromString(value: String): LockLogActionType? = values().find { it.toString() == value }
}