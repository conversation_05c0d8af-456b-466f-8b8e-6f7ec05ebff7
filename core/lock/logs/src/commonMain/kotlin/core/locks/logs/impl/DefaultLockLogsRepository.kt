package core.locks.logs.impl

import core.common.serialization.json
import core.locks.logs.models.LockAccessLog
import core.locks.logs.repostiories.LockLogsLocalRepository
import core.locks.logs.repostiories.LockLogsRemoteRepository
import core.locks.logs.repostiories.LockLogsRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

class DefaultLockLogsRepository(
    private val remote: LockLogsRemoteRepository,
    private val local: LockLogsLocalRepository,
    private val logger: Logger
) : LockLogsRepository {

    override suspend fun log(
        actionType: String,
        lockInfoData: String,
        lockInternalId: String,
        message: String,
        provider: String,
        user: String
    ) = logger.async {
        local.insert(
            actionType = actionType,
            lockInfoData = lockInfoData,
            lockInternalId = lockInternalId,
            message = message,
            provider = provider,
            user = user
        )
    }

    override suspend fun lockAccessLog(log: LockAccessLog, authentication: String): Unit = logger.async {
        local.logLockAccess(log)
        remote.lockAccessLog(local.getAllAccessLogs(), authentication)
        local.deleteAccessLogs()
    }

    override suspend fun syncLogs(
        internalId: String,
        authentication: String
    ) = logger.async {
        val logs = local.getAllByInternalId(internalId)
        val response = remote.syncLogs(logs = logs, internalId = internalId, authentication = authentication)
        val body = json.parseToJsonElement(response.body)
        return@async body.jsonObject["message"]?.jsonPrimitive?.content
            ?: body.jsonObject["msg"]?.jsonPrimitive?.content ?: ""
    }

    override suspend fun getLogs(
        internalId: String
    ) = logger.async {
        return@async local.getAllByInternalId(internalId)
    }
}