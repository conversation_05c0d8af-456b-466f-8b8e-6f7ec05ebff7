package core.lock.airbnk

import android.annotation.SuppressLint
import android.database.sqlite.SQLiteDatabase
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import com.airbnk.blellibNew.datafactory.LockerModel
import com.airbnk.blellibNew.sqlite.DBHelper
import com.airbnk.sdk.MainApi
import com.airbnk.sdk.StatusCode
import com.airbnk.sdk.callback.IConnectDeviceCallback
import com.airbnk.sdk.callback.IDeviceStatusCallback
import core.common.error.toFatal
import core.common.lifecycle.BindLifecycle
import core.common.platform.Platform
import core.lock.airbnk.models.AirBnkLock
import core.lock.airbnk.models.AirBnkStatus
import core.lock.airbnk.models.AndroidAirBnkLock
import core.lock.common.errors.connectionException
import core.lock.common.errors.timeoutException
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.job
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.io.ByteArrayInputStream
import java.io.ObjectInputStream
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class AndroidAirBnkManager(
    private val logger: Logger
) : AirBnkManager, BindLifecycle {

    private val locks = mutableMapOf<String, AndroidAirBnkLock>()
    private val jobs: ArrayList<Job> = arrayListOf()
    override var owner: LifecycleOwner? = null

    override suspend fun connect(accessKey: String, timeout: Long) = logger.cancelableSuspend(
        timeout = timeout
    ) { coroutine, continuation ->
        if (locks[accessKey] != null && locks[accessKey]!!.isConnected) {
            continuation.resume(locks[accessKey]!!)
            return@cancelableSuspend
        }
        jobs.add(coroutine.job)
        var iteration = 0
        var receivedCallback = true
        runCatching { Looper.prepare() }
        val mainApi = MainApi(context, accessKey, "airbnkHaha123456")
        val sn = mainApi.getSnFromSnInfo(accessKey, "airbnkHaha123456")

        handleAirBnkDatabase(sn)

        val model = mainApi.getLockTypeFromSnInfo(accessKey, "airbnkHaha123456")
        val flow = MutableStateFlow<AirBnkStatus?>(null)
        val device = IDeviceStatusCallback { status ->
            flow.update { status.statusCode().toAirBnkStatus() }
        }

        val connect = object : IConnectDeviceCallback {
            override fun onSuccess() {
                receivedCallback = true
                if (continuation.isActive) {
                    val lock = AndroidAirBnkLock(
                        lockName = "$model-$sn",
                        mainApi = mainApi,
                        accessKey = accessKey,
                        logger = logger,
                        flow = flow.filterNotNull()
                    )
                    locks[accessKey] = lock
                    continuation.resume(lock)
                }
            }

            override fun onFailed(p0: String?) {
                if (p0 == "-5" && continuation.isActive) {
                    continuation.resumeWithException(connectionException())
                }
                receivedCallback = true
            }
        }

        while (continuation.isActive) {
            if (receivedCallback) {
                mainApi.connect(connect, device)
            }
            receivedCallback = false
            iteration++
            runBlocking { delay(2500) }
        }
        if (continuation.isActive) continuation.resumeWithException(timeoutException())
    }

    override fun getByKey(accessKey: String): AirBnkLock {
        return locks[accessKey] ?: throw Exception("No lock with access key: $accessKey")
    }

    override fun disconnect() = logger.log {
        locks.values.forEach { it.disconnect() }
        locks.clear()
        jobs.forEach { it.cancel() }
        jobs.clear()
    }

    @SuppressLint("Range")
    private fun handleAirBnkDatabase(sn: String): Unit = logger.log {
        val db = DBHelper(context).writableDatabase
        var bytes: ByteArrayInputStream? = null
        var obj: ObjectInputStream? = null
        val select = db.rawQuery("select lockModel FROM sdk_lockmodel where lockSn = ?", arrayOf(sn))

        try {
            val lockBlob = select
                .use { if (it.moveToNext()) it.getBlob(it.getColumnIndex("lockModel")) else null } ?: return@log
            bytes = ByteArrayInputStream(lockBlob)
            obj = ObjectInputStream(bytes)
            obj.readObject() as LockerModel
        } catch (ex: Exception) {
            ex.printStackTrace()
            logger.error(ex.toFatal(Platform.executeLocation(), mapOf()))
            deleteDatabase(sn, db)
        } finally {
            runCatching { bytes?.close() }
            runCatching { obj?.close() }
            runCatching { db.close() }
        }
    }

    private fun deleteDatabase(sn: String, db: SQLiteDatabase) = logger.log {
        try {
            db.delete("sdk_lockmodel", "lockSn = ?", arrayOf(sn))
        } catch (ex: Exception) {
            ex.printStackTrace()
            logger.error(ex.toFatal(Platform.executeLocation(), mapOf()))
        }
    }
}

internal suspend inline fun <T> Logger.cancelableSuspend(
    timeout: Long = 0,
    crossinline onTimeout: () -> Unit = {},
    crossinline block: (context: CoroutineContext, continuation: CancellableContinuation<T>) -> Unit
): T {
    return async(Dispatchers.IO) {
        val context = currentCoroutineContext()

        if (timeout <= 0) {
            suspendCancellableCoroutine { block(context, it) }
        } else {
            val result = withTimeoutOrNull(timeout) {
                suspendCancellableCoroutine { block(context, it) }
            }

            if (result == null) {
                onTimeout()
                throw timeoutException()
            }
            result
        }
    }
}

internal fun StatusCode.toAirBnkStatus(): AirBnkStatus = when (this) {
    StatusCode.UNLOCKED -> AirBnkStatus.UNLOCKED
    StatusCode.LOCKED -> AirBnkStatus.LOCKED
    StatusCode.LOCKED_OPENED -> AirBnkStatus.LOCKED_OPENED
    StatusCode.UNLOCKED_OPENED -> AirBnkStatus.UNLOCKED_OPENED
    StatusCode.LOCKED_CLOSED -> AirBnkStatus.LOCKED_CLOSED
    StatusCode.UNLOCKED_CLOSED -> AirBnkStatus.UNLOCKED_CLOSED
    StatusCode.JAMMED -> AirBnkStatus.JAMMED
    else -> throw Exception("Unknown status: $this")
}

internal fun Int.statusCode(): StatusCode {
    return StatusCode.values().find { it.code == this } ?: throw Exception("Unknown status: $this")
}