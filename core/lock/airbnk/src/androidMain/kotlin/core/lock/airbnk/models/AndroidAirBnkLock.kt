package core.lock.airbnk.models

import com.airbnk.sdk.DeviceConfigInfo
import com.airbnk.sdk.MainApi
import com.airbnk.sdk.callback.IConfigCallback
import com.airbnk.sdk.callback.IGetDeviceConfigCallback
import com.airbnk.sdk.callback.IGetDeviceStatusCallback
import com.airbnk.sdk.callback.IGetDeviceVoltageCallback
import com.airbnk.sdk.callback.IGettimeCallback
import com.airbnk.sdk.callback.ILockCallback
import com.airbnk.sdk.callback.ISynctimeCallback
import com.airbnk.sdk.callback.IUnlockCallback
import core.lock.airbnk.cancelableSuspend
import core.lock.airbnk.statusCode
import core.lock.airbnk.toAirBnkStatus
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import kotlinx.coroutines.flow.Flow
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class AndroidAirBnkLock(
    override val lockName: String,
    internal val mainApi: MainApi,
    private val accessKey: String,
    private val logger: Logger,
    private val flow: Flow<AirBnkStatus>
) : AirBnkLock {

    internal var isConnected = true
        private set

    fun listenStatus(): Flow<AirBnkStatus> = flow

    private val timeoutInMillis = 20_000L

    override fun disconnect(): Unit = logger.log {
        mainApi.disconnect()
        isConnected = false
    }

    override suspend fun unlock(): Unit = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IUnlockCallback {
            override fun onSuccess() { if (continuation.isActive) continuation.resume(Unit) }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.unlock(accessKey, callback)
    }

    override suspend fun lock(): Unit = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : ILockCallback {
            override fun onSuccess() {
                if (continuation.isActive) continuation.resume(Unit)
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.lock(accessKey, callback)
    }

    override suspend fun batteryLevel(): Int = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IGetDeviceVoltageCallback {
            override fun voltage(p0: Int) {
                if (continuation.isActive) continuation.resume(p0)
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.getDeviceVoltage(accessKey, callback)
    }

    override suspend fun status(): AirBnkStatus = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IGetDeviceStatusCallback {
            override fun states(p0: Int) {
                if (continuation.isActive) continuation.resume(p0.statusCode().toAirBnkStatus())
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.getDeviceStatus(accessKey, callback)
    }

    override suspend fun getLockTime(): Long = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IGettimeCallback {
            override fun onSuccess(p0: Long) {
                if (continuation.isActive) continuation.resume(p0)
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.getTime(accessKey, callback)
    }

    override suspend fun setLockTime(lockTime: Long): Unit = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : ISynctimeCallback {
            override fun onSuccess() {
                if (continuation.isActive) continuation.resume(Unit)
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.syncTime(accessKey, lockTime, callback)
    }

    override suspend fun setLockDirection(
        direction: AirBnkLockDirection
    ): Unit = logger.async {
        val lockConfig = getLockConfig()
        logger.cancelableSuspend(timeout = timeoutInMillis, onTimeout = { disconnect() }) { coroutine, continuation ->
            val callback = object : IConfigCallback {
                override fun onSuccess() {
                    if (continuation.isActive) continuation.resume(Unit)
                }
                override fun onFailed(p0: String?) {
                    if (continuation.isActive) continuation.resumeWithException(Exception(p0))
                }
            }

            mainApi.config(
                accessKey,
                /* open direction */ if (direction == AirBnkLockDirection.LEFT) 0 else 1,
                /* autoLock: false = 0, true = 1 */ if (lockConfig.isAutoLock) 1 else 0,
                /* auto lock time */ lockConfig.autoLockTime.toLong(),
                /* enable magnet door sensor */ 0,
                /* magnet door sensor time */ 3,
                /* auto rotate 0 = enable, 1 = disable */ if (lockConfig.autoRotation) 0 else 1,
                callback
            )
        }
    }

    override suspend fun autoRotation(enable: Boolean): Unit = logger.async {
        val lockConfig = getLockConfig()
        logger.cancelableSuspend(timeout = timeoutInMillis, onTimeout = { disconnect() }) { coroutine, continuation ->
            val callback = object : IConfigCallback {
                override fun onSuccess() {
                    if (continuation.isActive) continuation.resume(Unit)
                }
                override fun onFailed(p0: String?) {
                    if (continuation.isActive) continuation.resumeWithException(Exception(p0))
                }
            }
            mainApi.config(
                accessKey,
                /* open direction */ lockConfig.lockDirection.ordinal,
                /* autoLock: false = 0, true = 1 */ if (lockConfig.isAutoLock) 1 else 0,
                /* auto lock time */ lockConfig.autoLockTime.toLong(),
                /* enable magnet door sensor */ 0,
                /* magnet door sensor time */ 3,
                /* auto rotate 0 = enable, 1 = disable */ if (enable) 0 else 1,
                callback
            )
        }
    }

    override suspend fun config(
        lockDirection: AirBnkLockDirection,
        autoRotation: Boolean,
        isAutoLock: Boolean,
        autoLockTime: Int
    ): Unit = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IConfigCallback {
            override fun onSuccess() {
                if (continuation.isActive) continuation.resume(Unit)
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.config(
            accessKey,
            /* open direction */ if (lockDirection == AirBnkLockDirection.LEFT) 0 else 1,
            /* autoLock: false = 0, true = 1 */ if (isAutoLock) 1 else 0,
            /* auto lock time */ autoLockTime.toLong() / 10,
            /* enable magnet door sensor */ 0,
            /* magnet door sensor time */ 3,
            /* auto rotate 0 = enable, 1 = disable */ if (autoRotation) 0 else 1,
            callback
        )
    }

    override suspend fun getLockConfig(): AirBnkLockConfig = logger.cancelableSuspend(
        timeout = timeoutInMillis,
        onTimeout = { disconnect() }
    ) { coroutine, continuation ->
        val callback = object : IGetDeviceConfigCallback {
            override fun onSuccess(p0: DeviceConfigInfo?) {
                if (continuation.isActive) {
                    val direction = maxOf((p0?.direction ?: 0) - 1, 0)
                    continuation.resume(
                        AirBnkLockConfig(
                            lockDirection = if (direction == 0) {
                                AirBnkLockDirection.LEFT
                            } else {
                                AirBnkLockDirection.RIGHT
                            },
                            autoRotation = p0?.autoSpin == 0,
                            isAutoLock = p0?.autoCloseMark == 1,
                            autoLockTime = p0?.autoCloseTime ?: (10 / 10)
                        )
                    )
                }
            }
            override fun onFailed(p0: String?) {
                if (continuation.isActive) continuation.resumeWithException(Exception(p0))
            }
        }
        mainApi.getDeviceConfigInfo(accessKey, callback)
    }
}