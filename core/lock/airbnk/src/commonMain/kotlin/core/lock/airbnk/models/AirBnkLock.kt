package core.lock.airbnk.models

interface AirBnkLock {

    val lockName: String

    fun disconnect()

    suspend fun unlock()

    suspend fun lock()

    suspend fun batteryLevel(): Int

    suspend fun status(): AirBnkStatus

    suspend fun getLockTime(): Long

    suspend fun setLockTime(lockTime: Long)

    suspend fun setLockDirection(direction: AirBnkLockDirection)

    suspend fun autoRotation(enable: Boolean)

    suspend fun config(
        lockDirection: AirBnkLockDirection = AirBnkLockDirection.LEFT,
        autoRotation: <PERSON>olean = false,
        isAutoLock: Boolean = false,
        autoLockTime: Int = 10
    )

    suspend fun getLockConfig(): AirBnkLockConfig
}