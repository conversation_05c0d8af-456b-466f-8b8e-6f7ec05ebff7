package data.tedee.models.dtos

import kotlinx.serialization.Serializable

@Serializable
internal data class OperateDto(
    val result: OperateResultDto?,
    val success: Boolean,
    val errorMessages: List<String>?,
    val message: String?
) {
    fun unwrapResult(): String {
        if (!success) throw Exception(errorMessages?.joinToString("\n") ?: message)

        return result!!.operationId
    }
}

@Serializable
internal data class OperateResultDto(
    val operationId: String
)