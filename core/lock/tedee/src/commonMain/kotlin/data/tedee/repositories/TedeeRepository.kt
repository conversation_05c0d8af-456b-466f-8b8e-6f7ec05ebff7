package data.tedee.repositories

import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.tedee.models.Operation
import data.tedee.models.dtos.OperateDto
import data.tedee.models.dtos.OperationDto
import data.tedee.models.dtos.SyncDto

class TedeeRepository(
    client: HttpClient,
    hostname: String,

    private val logger: Logger
) {

    private val remote = RemoteTedeeRepository(client = client, logger = logger, hostname = hostname)
    private val timeoutInMillis: Long = 30_000

    suspend fun lock(
        lockId: String,
        authentication: String,
        role: String,
        latitude: Double,
        longitude: Double
    ) = logger.async {
        val operationId = remote
            .lock(
                lockId = lockId,
                authentication = authentication,
                role = role,
                latitude = latitude,
                longitude = longitude
            )
            .value(OperateDto.serializer())
            .unwrapResult()

        runCatching { waitForOperation(operationId = operationId, authentication = authentication, role = role) }

        return@async sync(lockId, authentication, role = role)
    }

    suspend fun unlock(
        lockId: String,
        authentication: String,
        role: String,
        latitude: Double,
        longitude: Double
    ) = logger.async {
        val operationId = remote
            .unlock(
                lockId = lockId,
                authentication = authentication,
                role = role,
                latitude = latitude,
                longitude = longitude
            )
            .value(OperateDto.serializer())
            .unwrapResult()

        runCatching { waitForOperation(operationId = operationId, authentication = authentication, role = role) }

        return@async sync(lockId, authentication, role = role)
    }

    suspend fun unlockAssignment(lockId: String, authentication: String) = logger.async {
        val operationId = remote.unlockAssignment(lockId = lockId, authentication = authentication)
            .value(OperateDto.serializer())
            .unwrapResult()

        runCatching { waitForOperation(operationId = operationId, authentication = authentication, role = "company") }

        return@async sync(lockId, authentication, role = "company")
    }

    suspend fun sync(lockId: String, authentication: String, role: String) = logger.async {
        return@async remote
            .sync(lockId = lockId, authentication = authentication, role = role)
            .value(SyncDto.serializer())
            .unwrapToModel()
    }

    private suspend fun waitForOperation(operationId: String, authentication: String, role: String) = logger.async {
        kotlinx.coroutines.withTimeoutOrNull(timeoutInMillis) {
            while (true) {
                kotlinx.coroutines.delay(350)

                val operation = remote
                    .operationStatus(operationId, authentication, role = role)
                    .value(OperationDto.serializer())
                    .unwrapResult()

                if (operation == Operation.COMPLETED) {
                    kotlinx.coroutines.delay(1500)
                    break
                }
            }
        }
    }
}