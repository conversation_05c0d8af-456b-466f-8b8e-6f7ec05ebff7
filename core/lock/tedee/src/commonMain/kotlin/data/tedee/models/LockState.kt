package data.tedee.models

enum class LockState {
    Uncalibrated,
    Calibrating,
    Unlocked,
    SemiLocked,
    Unlocking,
    Locking,
    Locked,
    Pulled,
    Pulling,
    Unknown,
    Updating;

    val isUnlocked
        get() = when (this) {
            Unlocked, Unlocking, Pulled, Pulling -> true
            else -> false
        }

    companion object {
        internal fun fromResponse(state: Int): LockState {
            return when (state) {
                0 -> Uncalibrated
                1 -> Calibrating
                2 -> Unlocked
                3 -> SemiLocked
                4 -> Unlocking
                5 -> Locking
                6 -> Locked
                7 -> Pulled
                8 -> Pulling
                18 -> Unknown
                else -> LockState.Unknown
            }
        }
    }
}