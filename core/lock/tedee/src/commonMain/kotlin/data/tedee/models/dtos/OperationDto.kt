package data.tedee.models.dtos

import data.tedee.models.Operation
import kotlinx.serialization.Serializable

@Serializable
internal data class OperationDto(
    val result: OperationResultDto?,
    val success: Boolean,
    val errorMessages: List<String>?,
    val message: String?
) {

    fun unwrapResult(): Operation {
        if (!success) throw Exception(errorMessages?.joinToString("\n") ?: message)

        return Operation.valueOf(result!!.status)
    }
}

@Serializable
internal data class OperationResultDto(
    val status: String
)