package data.tedee.models.dtos

import data.tedee.models.LockState
import data.tedee.models.Sync
import kotlinx.serialization.Serializable

@Serializable
internal data class SyncDto(
    val success: Boolean,
    val result: SyncResultDto?,
    val errorMessages: List<String>?,
    val message: String?,
    val statusCode: Int?
) {
    fun unwrapToModel(): Sync {
        val errorMessage = errorMessages?.joinToString("\n")?.takeIf { it.isNotBlank() }
            ?: message
            ?: statusCode?.let { "Status code: $it" }
        if (!success) throw Exception(errorMessage)
        if (result?.isConnected == false) throw Exception("Lock is not connected")

        return Sync(
            isConnected = result!!.isConnected,
            state = LockState.fromResponse(result.lockProperties!!.state),
            batteryLevel = when (result.lockProperties.batteryLevel) {
                in 0..25 -> 0
                in 26..50 -> 1
                in 51..75 -> 2
                in 75..100 -> 3
                else -> 0
            }
        )
    }
}

@Serializable
internal data class SyncResultDto(
    val isConnected: Boolean,
    val lockProperties: LockPropertiesDto?
)

@Serializable
internal data class LockPropertiesDto(
    val state: Int,
    val batteryLevel: Int
)