package data.tedee.repositories

import core.http.client.HttpClient
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

internal class RemoteTedeeRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    internal suspend fun lock(
        lockId: String,
        authentication: String,
        role: String,
        latitude: Double,
        longitude: Double
    ) = logger.async {
        val place = if (role.lowercase() == "guest") "user" else "company"
        val url = "$hostname/$place/tedee/operation-lock/$lockId"
        val body = buildJsonObject {
            put("latitude", latitude)
            put("longitude", longitude)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authentication),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        client.request(request)
    }

    internal suspend fun unlock(
        lockId: String,
        authentication: String,
        role: String,
        latitude: Double,
        longitude: Double
    ) = logger.async {
        val place = if (role.lowercase() == "guest") "user" else "company"
        val url = "$hostname/$place/tedee/operation-unlock/$lockId"
        val body = buildJsonObject {
            put("latitude", latitude)
            put("longitude", longitude)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authentication),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        client.request(request)
    }

    internal suspend fun unlockAssignment(
        lockId: String,
        authentication: String
    ) = logger.async {
        val url = "$hostname/company/check-allotment/$lockId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authentication)
            )
        )

        client.request(request)
    }

    internal suspend fun operationStatus(operationId: String, authentication: String, role: String) = logger.async {
        val place = if (role.lowercase() == "guest") "user" else "company"
        val url = "$hostname/$place/tedee/device-operation/$operationId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authentication))
        )

        client.request(request)
    }

    internal suspend fun sync(lockId: String, authentication: String, role: String) = logger.async {
        val place = if (role.lowercase() == "guest") "user" else "company"
        val url = "$hostname/$place/tedee/sync-lock/$lockId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = authentication))
        )

        client.request(request)
    }
}