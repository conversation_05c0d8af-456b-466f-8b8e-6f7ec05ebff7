package core.common.error

import core.common.message.Message
import kotlinx.datetime.Clock

sealed class KError(val msg: Message, cause: Throwable) : Exception(msg.en, cause) {

    class Info(message: Message) : KError(msg = message, cause = Exception("Info: ${message.en}")) {
        constructor(message: String) : this(Message.fromString(message))
    }

    open class Fatal(
        message: Message,
        cause: Throwable,
        callLocation: String,
        details: Map<String, Any?>,
        val errorUid: String = Clock.System.now().toEpochMilliseconds().toString()
    ) : KError(msg = message, cause = cause) {
        private var isReported = false
        var callLocation = callLocation
        var details = details

        fun reported() {
            isReported = true
        }

        fun isReported(): Boolean {
            return isReported
        }

        inline fun regression(
            location: String = core.common.platform.Platform.executeLocation(),
            details: Map<String, Any?>
        ): Fatal {
            this.callLocation = "$location\n$callLocation"
            this.details += details.toList()

            return this
        }
    }
}