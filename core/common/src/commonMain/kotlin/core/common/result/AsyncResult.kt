package core.common.result

import core.common.error.KError

sealed interface AsyncResult<T> {

    class Success<T>(override val result: T) : AsyncResult<T>

    class Fail<T>(val error: KError, val data: T? = null) : AsyncResult<T>

    fun <V> map(transform: (T) -> V): AsyncResult<V> {
        return when (this) {
            is Fail -> Fail(error, data?.let(transform))
            is Success -> Success(transform(result))
        }
    }

    val result: T
        get() {
            return when (this) {
                is Fail -> throw error
                is Success -> (this as Success).result
            }
        }

    val resultOrNull: T?
        get() {
            return when (this) {
                is Fail -> data
                is Success -> (this as Success).result
            }
        }
}