package core.common.coroutines

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

interface AbstractCoroutineDispatcher {
    val main: CoroutineDispatcher
    val default: CoroutineDispatcher
    val io: CoroutineDispatcher
    val backgroundWork: CoroutineDispatcher
}

class CoroutineDispatchers(
    override val main: CoroutineDispatcher = Dispatchers.Main,
    override val default: CoroutineDispatcher = Dispatchers.Default,
    override val io: CoroutineDispatcher = Dispatchers.IO,
    override val backgroundWork: CoroutineDispatcher = Dispatchers.Default
) : AbstractCoroutineDispatcher