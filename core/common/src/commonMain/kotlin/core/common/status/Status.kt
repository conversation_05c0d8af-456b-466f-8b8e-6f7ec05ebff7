package core.common.status

import core.common.error.KError
import core.common.message.Message

sealed interface Status {

    val message: Message

    data class Info(override val message: Message) : Status

    data class Loading(override val message: Message) : Status

    data class Intermediate(override val message: Message) : Status

    data class Success(override val message: Message) : Status

    data class Fail(val error: KError, override val message: Message = error.msg) : Status
}