package core.common.status

import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.error.KError
import core.common.message.Message
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class CommonStatus(
    override val coroutines: AbstractCoroutineDispatcher
) : StatusRepository {

    private val flow = MutableStateFlow<List<Status>>(listOf())

    override val stream: StateFlow<List<Status>>
        get() = flow

    override suspend fun info(message: Message): Status.Info {
        return Status.Info(message).also {
            flow.emit(flow.value.toMutableList().apply { add(it) })
            delayRemove(it)
        }
    }

    override suspend fun load(message: Message): Status.Loading {
        return Status.Loading(message).also {
            flow.emit(flow.value.toMutableList().apply { add(it) })
        }
    }

    override suspend fun intermediate(message: Message): Status.Intermediate {
        return Status.Intermediate(message).also {
            flow.emit(flow.value.toMutableList().apply { add(it) })
        }
    }

    override suspend fun success(message: Message): Status.Success {
        return Status.Success(message).also {
            flow.emit(flow.value.toMutableList().apply { add(it) })
            delayRemove(it)
        }
    }

    override suspend fun fail(error: KError): Status.Fail {
        return Status.Fail(error).also {
            flow.emit(flow.value.toMutableList().apply { add(it) })
            delayRemove(it)
        }
    }

    override suspend fun removeStatus(status: Status) {
        if (status is Status.Loading || status is Status.Intermediate) return

        flow.emit(flow.value.toMutableList().apply { remove(status) })
    }

    override suspend fun removeLoading(status: Status) {
        flow.emit(flow.value.toMutableList().apply { remove(status) })
    }

    override suspend fun clear() {
        flow.emit(flow.value.filter { status -> status is Status.Loading || status is Status.Intermediate })
    }

    private fun delayRemove(status: Status) {
        CoroutineScope(coroutines.backgroundWork).launch {
            delay(500 + status.message.length * 100L)
            flow.emit(flow.value.toMutableList().apply { remove(status) })
        }
    }
}