package core.common.status

import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.error.KError
import core.common.error.toError
import core.common.message.Message
import core.common.result.AsyncResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

interface StatusRepository {
    val coroutines: AbstractCoroutineDispatcher

    val stream: StateFlow<List<Status>>

    suspend fun info(message: Message): Status.Info

    suspend fun load(message: Message): Status.Loading

    suspend fun intermediate(message: Message): Status.Intermediate

    suspend fun success(message: Message): Status.Success

    suspend fun fail(error: KError): Status.Fail

    suspend fun removeStatus(status: Status)

    suspend fun removeLoading(status: Status)

    suspend fun clear()
}

suspend inline fun <T> StatusRepository.execute(
    loading: Message,
    success: Message,
    job: suspend () -> T
): AsyncResult<T> {
    return execute(
        loading = loading,
        onSuccess = { success },
        job = job
    )
}

suspend inline fun <T> StatusRepository.execute(
    loading: Message,
    onSuccess: suspend (T) -> Message,
    job: suspend () -> T
): AsyncResult<T> {
    val location = core.common.platform.Platform.executeLocation()
    val loadingStatus = load(message = loading)

    return try {
        val result = job()
        val successStatus = success(message = onSuccess(result))
        CoroutineScope(coroutines.backgroundWork).launch {
            if (isActive) delay(500L + successStatus.message.length * 100)
            if (isActive) removeStatus(successStatus)
        }
        AsyncResult.Success(result)
    } catch (ex: Exception) {
        AsyncResult.Fail(ex.toError(location, mapOf("execution" to loading.en)))
    } finally {
        removeLoading(loadingStatus)
    }
}