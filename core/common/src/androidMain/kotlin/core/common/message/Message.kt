package core.common.message

import android.content.Context

actual class Message actual constructor(actual val en: String, private val ar: String) {

    actual val length: Int = if (en.isNotBlank() || ar.isNotBlank()) maxOf(en.length, ar.length) else 100

    private var resId: Int? = null
    private var args: Array<out Any> = arrayOf()

    constructor(resId: Int, vararg args: Any) : this("", "") {
        this.resId = resId
        this.args = args
    }

    fun text(context: Context): String {
        return if (resId == null) {
            val local = context.resources.configuration.locales[0].language
            if (local == "ar") ar else en
        } else {
            context.getString(resId!!, *args)
        }
    }

    actual companion object {
        actual fun fromString(message: String): Message {
            return Message(en = message, ar = message)
        }
    }
}