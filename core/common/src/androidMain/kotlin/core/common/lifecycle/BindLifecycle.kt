package core.common.lifecycle

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import core.common.error.toError
import core.common.platform.Platform

interface BindLifecycle {

    private val observer: LifecycleEventObserver
        get() {
            return LifecycleEventObserver { source, event ->
                onLifeCycleEvent(source, event)
            }
        }

    var owner: LifecycleOwner?

    val context: Context
        get() {
            if (owner == null) {
                throw IllegalStateException("Lifecycle is not bound").toError(Platform.executeLocation(), mapOf())
            }
            return owner!!.getContext()
        }

    fun bind(owner: LifecycleOwner) {
        if (
            owner.lifecycle.currentState != Lifecycle.State.CREATED &&
            owner.lifecycle.currentState != Lifecycle.State.INITIALIZED
        ) {
            throw IllegalStateException("Bind must be called on create").toError(Platform.executeLocation(), mapOf())
        }
        this.owner = owner
        this.owner?.lifecycle?.addObserver(observer)
    }

    private
    fun onLifeCycleEvent(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> onCreate(source)
            Lifecycle.Event.ON_START -> onStart(source)
            Lifecycle.Event.ON_RESUME -> onResume(source)
            Lifecycle.Event.ON_PAUSE -> onPause(source)
            Lifecycle.Event.ON_STOP -> onStop(source)
            Lifecycle.Event.ON_DESTROY -> {
                onDestroy(source)
                owner?.lifecycle?.removeObserver(observer)
                owner = null
            }

            Lifecycle.Event.ON_ANY -> onAnyLifeCycle(source)
        }
    }

    fun onAnyLifeCycle(source: LifecycleOwner) = Unit
    fun onPause(source: LifecycleOwner) = Unit
    fun onResume(source: LifecycleOwner) = Unit
    fun onStop(source: LifecycleOwner) = Unit
    fun onStart(source: LifecycleOwner) = Unit
    fun onDestroy(source: LifecycleOwner) = Unit
    fun onCreate(source: LifecycleOwner) = Unit
}

internal fun LifecycleOwner.getContext(): Context {
    return if (this is Fragment) requireContext() else this as Context
}