package core.common.lifecycle

import android.app.Activity
import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import core.common.error.toError
import core.common.platform.Platform

object Lifecycle {

    private val observer: LifecycleEventObserver = LifecycleEventObserver { source, event ->
        onLifeCycleEvent(source, event)
    }
    private var owner: LifecycleOwner? = null
    private var pause: (LifecycleOwner) -> Unit = {}
    private var create: (LifecycleOwner) -> Unit = {}
    private var resume: (LifecycleOwner) -> Unit = {}
    private var destroy: (LifecycleOwner) -> Unit = {}
    private var start: (LifecycleOwner) -> Unit = {}
    private var stop: (LifecycleOwner) -> Unit = {}
    private var any: (LifecycleOwner) -> Unit = {}

    val context: Context
        get() {
            if (owner == null) {
                throw IllegalStateException("Lifecycle is not bound").toError(Platform.executeLocation(), mapOf())
            }
            return owner!!.getContext()
        }
    val activity: Activity
        get() {
            return owner as? Activity ?: (owner as? Fragment)?.requireActivity()
                ?: throw Exception("Lifecycle is not bound to activity").toError(Platform.executeLocation(), mapOf())
        }

    fun bind(owner: LifecycleOwner) {
        if (
            owner.lifecycle.currentState != Lifecycle.State.CREATED &&
            owner.lifecycle.currentState != Lifecycle.State.INITIALIZED
        ) {
            throw IllegalStateException("Bind must be called on create").toError(Platform.executeLocation(), mapOf())
        }
        this.owner = owner
        this.owner?.lifecycle?.addObserver(observer)
    }

    private fun onLifeCycleEvent(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_CREATE -> create(source)
            Lifecycle.Event.ON_START -> start(source)
            Lifecycle.Event.ON_RESUME -> resume(source)
            Lifecycle.Event.ON_PAUSE -> pause(source)
            Lifecycle.Event.ON_STOP -> stop(source)
            Lifecycle.Event.ON_DESTROY -> {
                destroy(source)
                cleanup()
            }

            Lifecycle.Event.ON_ANY -> any(source)
        }
    }

    private fun onAnyLifeCycle(block: (source: LifecycleOwner) -> Unit) {
        any = block
    }

    private fun onPause(block: (source: LifecycleOwner) -> Unit) {
        pause = block
    }

    private fun onResume(block: (source: LifecycleOwner) -> Unit) {
        resume = block
    }

    private fun onStop(block: (source: LifecycleOwner) -> Unit) {
        stop = block
    }

    private fun onStart(block: (source: LifecycleOwner) -> Unit) {
        start = block
    }

    private fun onDestroy(block: (source: LifecycleOwner) -> Unit) {
        destroy = block
    }

    private fun onCreate(block: (source: LifecycleOwner) -> Unit) {
        create = block
    }

    private fun cleanup() {
        pause = {}
        any = {}
        resume = {}
        stop = {}
        start = {}
        destroy = {}
        create = {}
        owner?.lifecycle?.removeObserver(observer)
        owner = null
    }
}