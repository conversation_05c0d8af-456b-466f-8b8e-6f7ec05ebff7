plugins {
    alias(keyless.plugins.android.application) apply false
    alias(keyless.plugins.android.library) apply false
    alias(keyless.plugins.dagger.hilt) apply false
    alias(keyless.plugins.compose.compiler) apply false
    alias(keyless.plugins.firebase.crashlytics) apply false
    alias(keyless.plugins.google.play.services) apply false
    alias(keyless.plugins.kotlin.android) apply false
    alias(keyless.plugins.kotlin.kapt) apply false
    alias(keyless.plugins.kotlin.serialization) apply false
    alias(keyless.plugins.ktlint.gradle) apply false
    alias(keyless.plugins.sqldelight) apply false
}

allprojects {
    apply(plugin = rootProject.keyless.plugins.ktlint.gradle.get().pluginId)

    configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
        filter {
            exclude("*.gradle.kts")
            exclude {
                it.file.path.contains("${buildDir}/generated/")
            }
        }
    }


    tasks.withType<Test>().configureEach {
        if (!project.hasProperty("createReports")) {
            reports.html.required.set(false)
            reports.junitXml.required.set(false)
        }

        testLogging {
            showCauses = true
            showExceptions = true
            showStackTraces = true
            showStandardStreams = true
        }
    }
}

task("build") {
    dependsOn("v364CoreSdk:build")
    dependsOn("v364DroidSdk:build")
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
}