package presentation.home.dashboard.feature

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import data.keyless.home.LockSummary
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.common.feature.theme.AppTheme
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.SideEffect

class DashboardFragment : Fragment() {

    private val viewModel: DashboardAndroidViewModel by viewModel()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.sideEffects.collect(::onSideEffect)
            }
        }
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToChangePassword -> {
            TODO()
        }

        is SideEffect.NavToLockDetails -> {
            // Handle navigation to lock details
            navigateToLockDetails(sideEffect.lockSummary)
        }
    }

    private fun navigateToLockDetails(lockSummary: LockSummary) {
        TODO()
    }

    @Composable
    private fun Screen() {
        DashboardScreen(state = viewModel.state, onEvent = viewModel::onEvent)
    }

    override fun onResume() {
        super.onResume()
        viewModel.onEvent(ScreenEvent.Init)
    }
}
