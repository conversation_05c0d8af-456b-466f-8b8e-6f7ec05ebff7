package presentation.home.welcome.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import keyless.presentation.common.R
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppCarousel
import presentation.common.feature.components.AppCarouselIndicator
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppVideo
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.theme.AppTheme
import presentation.home.welcome.domain.Event

@Composable
internal fun WelcomeScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        VideoBackground()

        AppColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    horizontal = DesignSystem.Screen.Default.horizontalPadding,
                    vertical = DesignSystem.Screen.Default.verticalPadding * 4
                ),
            arrangement = Arrangement.SpaceBetween
        ) {
            AppLogo()

            Carousel(state = state)

            Actions(onEvent = onEvent)
        }
    }
}


@Composable
private fun BoxScope.VideoBackground() {
    AppVideo(
        modifier = Modifier
            .fillMaxSize()
            .scale(1.3f),
        res = R.raw.onboarding
    )
    Box(
        Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.3f))
    )
}

@Composable
private fun AppLogo(modifier: Modifier = Modifier) {
    Box(modifier.fillMaxWidth(), contentAlignment = Alignment.Center) { AppImage(res = R.drawable.keyless_logo) }
}

@Composable
private fun Carousel(modifier: Modifier = Modifier, state: StateHolder) {
    val pagerState = rememberPagerState(pageCount = { state.ui.carouselCount })

    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium, Alignment.CenterVertically),
        alignment = Alignment.CenterHorizontally
    ) {
        AppCarousel(
            modifier = modifier,
            count = state.ui.carouselCount,
            contentPadding = PaddingValues(DesignSystem.Padding.medium),
            gridSpacing = 0.dp,
            state = pagerState,
            content = { CarouselContent(it) }
        )

        AppCarouselIndicator(count = state.ui.carouselCount, currentPage = pagerState.currentPage)
    }
}

@Composable
private fun ColumnScope.CarouselContent(index: Int) {
    when (index) {
        0 -> CarouselItem(
            title = stringResource(R.string.access),
            text = stringResource(R.string.all_your_locks)
        )

        1 -> CarouselItem(
            title = stringResource(R.string.unlock),
            text = stringResource(R.string.anywhere_anytime)
        )

        2 -> CarouselItem(
            title = stringResource(R.string.history),
            text = stringResource(R.string.all_access_history)
        )
    }
}

@Composable
private fun CarouselItem(title: String, text: String) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium, Alignment.CenterVertically),
        alignment = Alignment.CenterHorizontally
    ) {
        AppPageTitleText(
            text = title,
            design = DesignSystem.Text.PageTitle.copy(color = MaterialTheme.colorScheme.background)
        )
        AppBodyText(
            text = text,
            design = DesignSystem.Text.Body.copy(
                color = MaterialTheme.colorScheme.background,
                alignment = TextAlign.Center,
                minLines = 2
            )
        )
    }
}

@Composable
private fun Actions(modifier: Modifier = Modifier, onEvent: (Event) -> Unit) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium, Alignment.CenterVertically),
        alignment = Alignment.CenterHorizontally
    ) {
        AppButton(text = stringResource(R.string.login)) { onEvent(Event.NavToLogin) }

        AppBodyText(
            modifier = Modifier.hiddenClickable { onEvent(Event.NavToSignup) },
            text = signupText(),
            design = DesignSystem.Text.Body.copy(color = MaterialTheme.colorScheme.background)
        )
    }
}

@Composable
private fun signupText(): AnnotatedString = buildAnnotatedString {
    withStyle(SpanStyle(color = MaterialTheme.colorScheme.background)) {
        append("${stringResource(R.string.not_have_account)} ")
    }

    withStyle(SpanStyle(color = MaterialTheme.colorScheme.background, textDecoration = TextDecoration.Underline)) {
        append(stringResource(R.string.signup))
    }
}

@Preview
@Composable
private fun Preview() {
    AppTheme { WelcomeScreen(StateHolder(), onEvent = {}) }
}