package presentation.home.welcome.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.common.feature.theme.AppTheme
import presentation.home.welcome.domain.SideEffect

class WelcomeScreenFragment: Fragment() {

    private val viewModel by viewModel<WelcomeAndroidViewModel>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }
    }

    @Composable
    private fun Screen() {
        val state = remember { viewModel.stateHolder() }

        WelcomeScreen(state, onEvent = viewModel::onEvent)
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToLogin -> {
            val intent = Intent(requireContext(), AuthenticationActivity::class.java)
            intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.LOGIN)
            startActivity(intent)
        }
        is SideEffect.NavToSignup -> {
            val intent = Intent(requireContext(), AuthenticationActivity::class.java)
            intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.SIGNUP)
            startActivity(intent)
        }
    }
}