package presentation.home.dashboard.domain

import data.common.preferences.Preferences
import data.keyless.guest.ClaimKeyRequest
import data.keyless.guest.LogServicePostRequest
import data.keyless.users.models.CheckUserRequest

internal fun UserEvent.ClaimKey.request() = ClaimKeyRequest(bookingNumber)

internal fun ScreenEvent.Init.checkUserRequest() = CheckUserRequest(
    uid = Preferences.uuid.get(),
    isAdmin = Preferences.isAdminLogin()
)

internal fun UserEvent.LogService.request() = LogServicePostRequest(service)
