package presentation.home.dashboard.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.keyless.guest.GuestRepository
import data.keyless.users.UserRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.SideEffect

internal class UseCases(
    private val userRepository: UserRepository,
    private val guestRepository: GuestRepository,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    val initScreen = InitScreenUseCase(
        repository = userRepository,
        screen = screenData,
        sideEffect = sideEffects,
        logger = logger,
        status = status
    )

    val onLockClick = OnLockClickUseCase(
        logger = logger,
        status = status,
        sideEffects = sideEffects
    )

    val claimKey = ClaimKeyUseCase(
        guestRepository = guestRepository,
        logger = logger,
        status = status,
        initScreen = initScreen
    )

    val logService = LogServiceUseCase(
        guestRepository = guestRepository,
        logger = logger,
        status = status
    )
}
