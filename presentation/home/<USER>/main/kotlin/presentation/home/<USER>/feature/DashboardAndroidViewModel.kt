package presentation.home.dashboard.feature

import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import presentation.common.feature.state.DerivedState
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.ViewModel

internal class DashboardAndroidViewModel(
    private val domain: ViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    private val onClear: () -> Unit
) : androidx.lifecycle.ViewModel() {

    val state = StateHolder(data = DerivedState(ScreenData.empty, domain.screenDataStream))
    val sideEffects = domain.sideEffectsStream

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    override fun onCleared() {
        super.onCleared()
        onClear()
    }
}
