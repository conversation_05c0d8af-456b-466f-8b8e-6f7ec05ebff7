package presentation.home.dashboard.domain

import core.common.status.Status
import data.keyless.home.LockSummary
import data.keyless.users.models.CheckUserResponse

data class ScreenData(
    val userCheck: CheckUserResponse?,
    val status: List<Status>
) {
    companion object {
        val empty = ScreenData(userCheck = null, status = emptyList())
    }
}

sealed interface SideEffect {
    object NavToChangePassword : SideEffect
    data class NavToLockDetails(val lockSummary: LockSummary) : SideEffect
}

sealed interface Event

sealed interface UserEvent : Event {
    data class OnLockClick(val lockSummary: LockSummary) : UserEvent
    data class ClaimKey(val bookingNumber: String) : UserEvent
    data class LogService(val service: String) : UserEvent
}

sealed interface ScreenEvent: Event {
    object Init : ScreenEvent
    class DismissStatus(val status: Status) : ScreenEvent
}