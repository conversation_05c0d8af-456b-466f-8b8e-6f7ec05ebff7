package presentation.home.dashboard.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import data.common.utils.ensureEndsWith
import data.keyless.home.LockDetails
import data.keyless.home.LockSummary
import data.keyless.home.PropertyDetails
import keyless.presentation.common.R
import keyless.presentation.home.ConfigValues
import presentation.common.feature.components.AppCarouselIndicator
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppFlowRow
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.NoLocksIllustration
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.UserAction

@Composable
internal fun RowScope.GuestTopBarContent(state: StateHolder) {
    GuestTopBarTitle(state)

    GuestTopBarNotificationButton()

    GuestTopBarSettingsButton()
}

@Composable
private fun GuestTopBarSettingsButton() {
    AppIcon(res = R.drawable.iv_settings)
}

@Composable
private fun GuestTopBarNotificationButton() {
    AppIcon(res = R.drawable.iv_notifications)
}

@Composable
private fun RowScope.GuestTopBarTitle(state: StateHolder) {
    AppPageTitleText(
        modifier = Modifier.weight(1f),
        text = state.ui.welcomeText(),
        design = DesignSystem.Text.PageTitle.copy(overflow = TextOverflow.Ellipsis)
    )
}

@Composable
internal fun ClaimYourKeyButton(onClaimKey: () -> Unit) {
    AppLabelText(
        modifier = Modifier
            .clip(CircleShape)
            .border(1.dp, MaterialTheme.colorScheme.primary, CircleShape)
            .padding(horizontal = DesignSystem.Padding.medium, vertical = DesignSystem.Padding.small)
            .clickable(onClick = onClaimKey),
        text = stringResource(R.string.claim_your_key),
        design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.primary, weight = FontWeight.Bold)
    )
}

@Composable
internal fun YourKeysText() {
    AppLabelText(
        text = stringResource(R.string.your_keys),
        design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
    )
}

@Composable
internal fun ChangePasswordText(state: ChangePasswordState.Show) {
    AppLabelText(
        text = stringResource(R.string.tap_to_update_your_password_before, state.dueDate),
        design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.error, alignment = TextAlign.Center)
    )
}

@Composable
internal fun LockItem(
    modifier: Modifier = Modifier,
    lock: LockSummary,
    onEvent: (Event) -> Unit = {}
) {
    AppSurface(
        modifier = modifier
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, MaterialTheme.shapes.medium)
            .clickable {
                onEvent(UserAction.OnLockClick(lock))
            },
        paddings = PaddingValues(0.dp),
        color = Color.Transparent,
        shape = MaterialTheme.shapes.medium
    ) {
        LockItemLayout(lock)

        if (!lock.isLockActiveForUser) LockItemInActive()
    }
}

@Composable
private fun LockItemLayout(lock: LockSummary) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .padding(DesignSystem.Padding.screen),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        LockItemLockIcon(lock.lock)

        LockItemLockName(lock)

        LockItemLockAddress(lock.propertyDetails)

        LockItemPropertyName(lock)
    }
}

@Composable
private fun LockItemLockName(lock: LockSummary) {
    AppLabelText(
        text = lock.lock.name,
        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, weight = FontWeight.Bold)
    )
}

@Composable
private fun LockItemPropertyName(lock: LockSummary) {
    AppLabelText(
        text = lock.propertyDetails.name, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun LockItemLockAddress(property: PropertyDetails) {
    val context = LocalContext.current
    val place = remember(property) {
        val comma = context.getString(R.string.comma)
        if (property.appartmentNumber.isEmpty()) property.floor + " " + context.getString(R.string.floor)
        else property.appartmentNumber + "$comma " + property.floor + " " + context.getString(R.string.floor)
    }

    AppLabelText(text = place, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center))
}

@Composable
private fun LockItemInActive() {
    Box(
        modifier = Modifier
            .size(40.dp)
            .rotate(-45f),
        contentAlignment = Alignment.Center
    ) {
        LockItemInactiveRibbon()

        LockItemInactiveText()
    }
}

@Composable
private fun LockItemInactiveText() {
    AppLabelText(
        text = stringResource(R.string.inactive),
        design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.onPrimary, size = 8.sp)
    )
}

@Composable
private fun LockItemInactiveRibbon() {
    Box(
        modifier = Modifier
            .width(40.dp)
            .height(12.dp)
            .scale(scaleX = 2f, scaleY = 1f)
            .background(MaterialTheme.colorScheme.primary)
    )
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
private fun LockItemLockIcon(lock: LockDetails) {
    val icon = remember { lock.icon.firstOrNull()?.icon }
    if (icon?.isNotBlank() == true) {
        GlideImage(
            model = ConfigValues.imageUrl.ensureEndsWith("/") + icon,
            contentDescription = null,
            modifier = Modifier.size(DesignSystem.Icon.medium)
        )
    } else {
        LockItemIconPlaceholder()
    }
}

@Composable
private fun LockItemIconPlaceholder() {
    AppIcon(
        modifier = Modifier.size(DesignSystem.Icon.medium),
        res = R.drawable.iv_other_icon,
        tint = MaterialTheme.colorScheme.primary
    )
}

@Composable
internal fun GuestCarouselItem(modifier: Modifier = Modifier, index: Int, titleRes: Int, textRes: Int) {
    AppSurface(
        modifier = modifier, paddings = PaddingValues(DesignSystem.Padding.screen),
        color = MaterialTheme.colorScheme.tertiary
    ) {
        GuestCarouselLayout(titleRes, textRes, index)
    }
}

@Composable
internal fun GuestCarouselLayout(titleRes: Int, textRes: Int, index: Int) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large),
        alignment = Alignment.CenterHorizontally
    ) {
        GuestCarouselItemLayout(titleRes, textRes)

        GuestCarouselIndicator(index)
    }
}

@Composable
private fun GuestCarouselIndicator(index: Int) {
    AppCarouselIndicator(count = 3, currentPage = index, color = MaterialTheme.colorScheme.primary)
}

@Composable
private fun GuestCarouselItemLayout(titleRes: Int, textRes: Int) {
    AppRow(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterVertically
    ) {
        GuestCarouselItemLogo()

        GuestCarouselItemTextArea(titleRes, textRes)
    }
}

@Composable
private fun RowScope.GuestCarouselItemTextArea(titleRes: Int, textRes: Int) {
    AppColumn(
        modifier = Modifier.weight(1f),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterHorizontally
    ) {
        GuestCarouselItemTitle(titleRes)

        GuestCarouselItemBody(textRes)
    }
}

@Composable
private fun GuestCarouselItemLogo() {
    AppImage(res = R.drawable.keyless_logo_2)
}

@Composable
private fun GuestCarouselItemBody(textRes: Int) {
    AppLabelText(
        text = stringResource(textRes),
        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, minLines = 3)
    )
}

@Composable
private fun GuestCarouselItemTitle(titleRes: Int) {
    AppPageTitleText(text = stringResource(titleRes))
}

@Composable
internal fun QuickServicesText() {
    AppLabelText(
        modifier = Modifier.padding(horizontal = DesignSystem.Padding.screen),
        text = stringResource(R.string.quick_services),
        design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
    )
}

@Composable
internal fun QuickServiceItem(modifier: Modifier = Modifier, imageRes: Int, textRes: Int) {
    AppSurface(
        modifier = modifier,
        color = MaterialTheme.colorScheme.primary,
        contentColor = MaterialTheme.colorScheme.onPrimary,
        shape = MaterialTheme.shapes.medium
    ) {
        QuickServiceItemLayout(modifier, imageRes, textRes)
    }
}

@Composable
internal fun QuickServiceItemLayout(modifier: Modifier = Modifier, imageRes: Int, textRes: Int) {
    AppColumn(
        modifier = modifier
            .size(160.dp)
            .padding(bottom = DesignSystem.Padding.small),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterHorizontally
    ) {
        QuickServicesItemImage(modifier = Modifier.weight(1f), imageRes)

        QuickServicesItemText(textRes)
    }
}

@Composable
private fun QuickServicesItemText(textRes: Int) {
    AppLabelText(text = stringResource(textRes))
}

@Composable
private fun QuickServicesItemImage(modifier: Modifier = Modifier, imageRes: Int) {
    AppImage(res = imageRes, contentScale = ContentScale.Crop, modifier = modifier)
}