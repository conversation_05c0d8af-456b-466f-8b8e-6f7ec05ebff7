package presentation.home.welcome.feature

import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import presentation.home.welcome.domain.Event
import presentation.home.welcome.domain.ViewModel

internal class WelcomeAndroidViewModel(
    private val domain: ViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON>rrorHandler,
    private val onClear: () -> Unit
): androidx.lifecycle.ViewModel() {
    private var stateHolder: StateHolder? = null
    val sideEffects = domain.sideEffectsStream

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    fun stateHolder(): StateHolder {
        if (stateHolder == null) stateHolder = StateHolder()
        return stateHolder!!
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}