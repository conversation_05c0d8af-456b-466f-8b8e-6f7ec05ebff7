package presentation.home.dashboard.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.guest.GuestRepository
import presentation.home.dashboard.domain.UserEvent
import presentation.home.dashboard.domain.request

class LogServiceUseCase(
    private val guestRepository: GuestRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: UserEvent.LogService) = logger.async {
        runCatching { job(event) }
    }

    private suspend fun job(event: UserEvent.LogService) = logger.async {
        guestRepository.logService(event.request())
    }
}