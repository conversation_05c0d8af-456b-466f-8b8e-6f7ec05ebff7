package presentation.home.dashboard.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.device
import keyless.presentation.common.R
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.SideEffect
import presentation.home.dashboard.domain.UserEvent

internal class OnLockClickUseCase(
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserEvent.OnLockClick) = logger.async {
        val lockSummary = event.lockSummary

        if (lockSummary.isLockActiveForUser) {
            sideEffects.emit(SideEffect.NavToLockDetails(lockSummary))
        } else {
            status.fail(
                KError.Info(
                    Message(
                        R.string.this_lock_will_be_accessible_on,
                        lockSummary.assignment?.assignmentData?.validFromDateTime?.device()?.date?.toString() ?: ""
                    )
                )
            )
        }
    }
}
