package presentation.home.dashboard.domain.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.guest.GuestRepository
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.UserEvent
import presentation.home.dashboard.domain.request

internal class ClaimKeyUseCase(
    private val guestRepository: GuestRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val initScreen: InitScreenUseCase
) {

    suspend fun execute(event: UserEvent.ClaimKey) = logger.async {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: UserEvent.ClaimKey) = logger.async {
        guestRepository.claimKey(event.request())
        initScreen.execute(ScreenEvent.Init)
    }
}