package presentation.home.dashboard.domain.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.guest.GuestRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.UserAction

internal class ClaimKeyUseCase(
    private val guestRepository: GuestRepository,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: UserAction.ClaimKey) = logger.async {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: UserAction.ClaimKey) = logger.async {
        guestRepository.claimKey(event.request)
        screenData.update { it.copy(showClaimKeyDialog = false) }
    }
}