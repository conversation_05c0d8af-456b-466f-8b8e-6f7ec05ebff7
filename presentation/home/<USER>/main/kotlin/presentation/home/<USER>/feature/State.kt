package presentation.home.dashboard.feature

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import data.keyless.home.LockSummary
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.ItemState
import presentation.common.feature.state.ListState
import presentation.common.feature.state.StringState
import presentation.home.dashboard.domain.ScreenData

internal class StateHolder(
    val data: DerivedState<ScreenData>,
    val ui: UIState = UIState(data = data),
)

internal class UIState(
    val name: StringState = StringState(""),
    val locks: ListState<LockSummary> = ListState(emptyList()),

    data: ItemState<ScreenData>
) {
    val changePassword = DerivedState(ChangePasswordState.None, data.stream.map { it.changePasswordState() })
    val claimKey = DerivedState(ClaimKeyState.None, data.stream.map { it.claimKeyState() })

    @Composable
    fun welcomeText(): String {
        val hour = currentHour()
        val firstName = getFirstName()
        val res = guestWelcomeResource(hour, firstName)
        return if (firstName.isNotBlank()) stringResource(res) + " " + firstName else stringResource(res)
    }

    private fun getFirstName() = if (name.get().isNotBlank()) name.get().split(" ").first() else ""
    private fun currentHour(): Int = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).hour
}

internal sealed interface ChangePasswordState {
    object None : ChangePasswordState
    data class Show(val dueDate: String) : ChangePasswordState
}

internal sealed interface ClaimKeyState {
    object None : ClaimKeyState
    data class Show(val retrievalCode: StringState = StringState("")) : ClaimKeyState
}