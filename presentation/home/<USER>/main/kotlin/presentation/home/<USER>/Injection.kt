package presentation.home.welcome

import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.welcome.domain.SideEffect
import presentation.home.welcome.domain.ViewModel
import presentation.home.welcome.feature.WelcomeAndroidViewModel

internal data object WelcomeInjectionScope

val welcomeInjection = module {
    scope<WelcomeInjectionScope> {
        scoped { SideEffectsRepository<SideEffect>() }
        scoped { ViewModel(sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<WelcomeInjectionScope>(WelcomeInjectionScope.getScopeId())
        WelcomeAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}