package presentation.home.dashboard.feature

import keyless.presentation.common.R
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.UserEvent

internal fun ScreenData.changePasswordState(): ChangePasswordState {
    if (userCheck == null || !userCheck.forcePasswordRequire) return ChangePasswordState.None
    val dueDate = runCatching { Instant.parse(userCheck!!.dueDate) }.getOrNull() ?: return ChangePasswordState.None

    return ChangePasswordState.Show(dueDate.toLocalDateTime(TimeZone.currentSystemDefault()).date.toString())
}

internal fun guestWelcomeResource(hour: Int, firstName: String): Int = when {
    hour in 0..11 && firstName.isNotBlank() -> R.string.text_good_morning
    hour in 0..11 && firstName.isBlank() -> R.string.good_morning_guest
    hour in 12..16 && firstName.isNotBlank() -> R.string.text_good_afternoon
    hour in 12..16 && firstName.isBlank() -> R.string.good_afternoon_guest
    hour in 17..23 && firstName.isNotBlank() -> R.string.text_good_evening
    hour in 17..23 && firstName.isBlank() -> R.string.good_evening_guest
    else -> R.string.good_evening_guest
}

internal fun ClaimKeyState.Show.event() = UserEvent.ClaimKey(retrievalCode.get())