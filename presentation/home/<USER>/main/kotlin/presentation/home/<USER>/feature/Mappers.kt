package presentation.home.dashboard.feature

import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import presentation.home.dashboard.domain.ScreenData

internal fun ScreenData.changePasswordState(): ChangePasswordState {
    if (userCheck == null || !userCheck.forcePasswordRequire) return ChangePasswordState.None
    val dueDate = runCatching { Instant.parse(userCheck!!.dueDate) }.getOrNull() ?: return ChangePasswordState.None

    return ChangePasswordState.Show(dueDate.toLocalDateTime(TimeZone.currentSystemDefault()).date.toString())
}