package presentation.home.dashboard.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.flowOf
import presentation.common.feature.components.AppBasePage
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.state.DerivedState
import presentation.common.feature.theme.AppTheme
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.UserAction

@Composable
internal fun DashboardScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit = {}
) {
    AppBasePage(
        paddingValues = PaddingValues(0.dp),
        status = state.data.value.status,
        onCancelStatus = { onEvent(ScreenEvent.DismissStatus(it)) },
        topBar = { HomeTopBar(state) }
    ) {
        AppScrollColumn(
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
        ) {
            item {
                GuestCarousel(
                    modifier = Modifier
                        .padding(top = DesignSystem.Padding.screen)
                        .padding(horizontal = DesignSystem.Padding.screen)
                )
            }

            if (state.ui.changePassword.get() is ChangePasswordState.Show) {
                item {
                    ChangePasswordRow(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = DesignSystem.Padding.screen),
                        state = state.ui.changePassword.get() as ChangePasswordState.Show,
                        onChangePassword = {}
                    )
                }
            }

            item {
                YourKeysRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = DesignSystem.Padding.screen),
                    onClaimKey = { onEvent(UserAction.ShowClaimKeyDialog) })
            }

            item {
                LocksSection(
                    modifier = Modifier.padding(horizontal = DesignSystem.Padding.screen),
                    state = state,
                    onEvent = onEvent
                )
            }

            item { QuickServicesSection() }
        }
    }

    // Show Claim Key Dialog
    if (state.ui.claimKey.get() is ClaimKeyState.Show) {
        ClaimYourKeyDialog(
            state = state.ui.claimKey.get() as ClaimKeyState.Show,
            onConfirm = { retrievalCode ->
                onEvent(UserAction.ClaimKey(retrievalCode))
            },
            onDismiss = {
                onEvent(UserAction.DismissClaimKeyDialog)
            }
        )
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember {
        val state = StateHolder(data = DerivedState(ScreenData.empty, flowOf()))
        state
    }
    AppTheme { DashboardScreen(state, onEvent = {}) }
}