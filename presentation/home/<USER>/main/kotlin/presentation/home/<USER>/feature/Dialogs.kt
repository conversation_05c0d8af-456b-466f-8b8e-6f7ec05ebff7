package presentation.home.dashboard.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.Dialog
import keyless.presentation.common.R
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppHorizontalDivider
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppTextButton
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.AppVerticalDivider
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.state.StringState
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.UserEvent

@Composable
internal fun ClaimYourKeyDialog(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onDismiss: () -> Unit = { state.ui.claimKey.update(ClaimKeyState.None) }
) {
    val state = remember { state.ui.claimKey.get() as ClaimKeyState.Show }
    Dialog(onDismissRequest = onDismiss) {
        ClaimYourKeyDialogContent(
            state = state,
            onConfirm = { onEvent(UserEvent.ClaimKey(state.retrievalCode.get())) },
            onDismiss = onDismiss
        )
    }
}

@Composable
private fun ClaimYourKeyDialogContent(
    state: ClaimKeyState.Show,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(DesignSystem.Corner.medium))
            .background(Color.White),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        ClaimYourKeyDialogTitle()

        ClaimYourKeyDialogDescription()

        ClaimYourKeyDialogTextField(state = state.retrievalCode)

        AppHorizontalDivider()

        ClaimYourKeyDialogButtons(onConfirm = onConfirm, onDismiss = onDismiss)
    }
}

@Composable
private fun ClaimYourKeyDialogTitle() {
    AppPageTitleText(
        modifier = Modifier.padding(top = DesignSystem.Padding.large),
        text = stringResource(R.string.claim_your_key),
        design = DesignSystem.Text.PageTitle.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun ClaimYourKeyDialogDescription() {
    AppBodyText(
        modifier = Modifier.padding(horizontal = DesignSystem.Padding.large),
        text = stringResource(R.string.please_enter_your_retrieval_code_to_claim_your_key),
        design = DesignSystem.Text.Body.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun ClaimYourKeyDialogTextField(state: StringState) {
    AppTextField(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.medium),
        state = state,
        hint = stringResource(R.string.enter_retrieval_code_number),
        singleLine = true
    )
}

@Composable
private fun ClaimYourKeyDialogButtons(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AppRow(
        modifier = Modifier.fillMaxWidth(),
        alignment = Alignment.CenterVertically
    ) {
        ClaimYourKeyDialogCancelButton(onDismiss)

        AppVerticalDivider()

        ClaimYourKeyDialogProceedButton(onConfirm)
    }
}

@Composable
private fun RowScope.ClaimYourKeyDialogProceedButton(onConfirm: () -> Unit) {
    AppTextButton(
        modifier = Modifier.weight(1f),
        text = stringResource(R.string.proceed),
        onClick = onConfirm
    )
}

@Composable
private fun RowScope.ClaimYourKeyDialogCancelButton(onDismiss: () -> Unit) {
    AppTextButton(
        modifier = Modifier.weight(1f),
        text = stringResource(R.string.text_cancel),
        onClick = onDismiss
    )
}

@Composable
internal fun GroceriesDialog(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onDismiss: () -> Unit = { state.ui.groceriesDialog.update(GroceriesDialogState.None) }
) {
    val dialogState = remember { state.ui.groceriesDialog.get() as GroceriesDialogState.Show }
    Dialog(onDismissRequest = onDismiss) {
        GroceriesDialogContent(
            type = dialogState.type,
            onItemClick = { /* TODO: Handle item clicks */ },
            onDismiss = onDismiss
        )
    }
}

@Composable
private fun GroceriesDialogContent(
    type: String,
    onItemClick: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(DesignSystem.Corner.medium))
            .background(Color.White),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        GroceriesDialogTitle()

        if (type == "groceries") {
            GroceriesDialogGroceriesItems(onItemClick)
        } else {
            GroceriesDialogTaxiItems(onItemClick)
        }

        AppHorizontalDivider()

        GroceriesDialogCancelButton(onDismiss)
    }
}

@Composable
private fun GroceriesDialogTitle() {
    AppPageTitleText(
        modifier = Modifier.padding(top = DesignSystem.Padding.large),
        text = stringResource(R.string.quick_services),
        design = DesignSystem.Text.PageTitle.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun GroceriesDialogGroceriesItems(onItemClick: (String) -> Unit) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)
    ) {
        GroceriesDialogItem(
            text = stringResource(R.string.instashop),
            onClick = { onItemClick("instashop") }
        )
        GroceriesDialogItem(
            text = stringResource(R.string.noon_daily),
            onClick = { onItemClick("noon_daily") }
        )
        GroceriesDialogItem(
            text = stringResource(R.string.groceries),
            onClick = { onItemClick("groceries") }
        )
    }
}

@Composable
private fun GroceriesDialogTaxiItems(onItemClick: (String) -> Unit) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)
    ) {
        GroceriesDialogItem(
            text = stringResource(R.string.xxride),
            onClick = { onItemClick("xxride") }
        )
        GroceriesDialogItem(
            text = stringResource(R.string.hala_taxi),
            onClick = { onItemClick("hala_taxi") }
        )
        GroceriesDialogItem(
            text = stringResource(R.string.taxi),
            onClick = { onItemClick("taxi") }
        )
    }
}

@Composable
private fun GroceriesDialogItem(
    text: String,
    onClick: () -> Unit
) {
    AppTextButton(
        modifier = Modifier.fillMaxWidth(),
        text = text,
        onClick = onClick
    )
}

@Composable
private fun GroceriesDialogCancelButton(onDismiss: () -> Unit) {
    AppTextButton(
        modifier = Modifier.fillMaxWidth(),
        text = stringResource(R.string.text_cancel),
        onClick = onDismiss
    )
}