package presentation.home.welcome.domain

import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel (
    private val sideEffects: SideEffectsRepository<SideEffect>
) {
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is Event.NavToLogin -> sideEffects.emit(SideEffect.NavToLogin)
        is Event.NavToSignup -> sideEffects.emit(SideEffect.NavToSignup)
    }
}