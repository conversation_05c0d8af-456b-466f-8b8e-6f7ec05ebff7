package presentation.home.dashboard.domain.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.isAfter
import data.common.lastServerTime
import data.common.localDate
import data.common.preferences.Preferences
import data.keyless.users.UserRepository
import data.keyless.users.models.CheckUserResponse
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.dashboard.domain.ScreenData
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.SideEffect

internal class InitScreenUseCase(
    private val repository: UserRepository,
    private val screen: ScreenDataRepository<ScreenData>,
    private val sideEffect: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async {
        checkUser(event)
    }

    private suspend fun checkUser(event: ScreenEvent.Init) = logger.async {
        val response = repository.check(event.checkUserRequest)
        handleCheckUserSuccess(response)
    }

    private suspend fun handleCheckUserSuccess(response: CheckUserResponse) = logger.async {
        Preferences.timeZoneName.set(response.timezoneName)
        Preferences.timeZoneOffset.set(response.timezone)
        screen.update { it.copy(userCheck = response) }

        val dueDate = response.dueDateInstant?.localDate ?: return@async
        val serverDate = lastServerTime()?.localDate ?: return@async

        if (dueDate.isAfter(serverDate)) sideEffect.emit(SideEffect.NavToChangePassword)
    }
}