package presentation.test

import android.app.Activity
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.test.ExperimentalTestApi
import androidx.compose.ui.test.SemanticsMatcher
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.SemanticsNodeInteractionCollection
import androidx.compose.ui.test.assert
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.hasScrollAction
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.isRoot
import androidx.compose.ui.test.junit4.ComposeTestRule
import androidx.compose.ui.test.onChildAt
import androidx.compose.ui.test.onRoot
import androidx.compose.ui.test.performScrollTo
import androidx.compose.ui.test.performScrollToNode
import androidx.compose.ui.test.printToString
import androidx.fragment.app.Fragment
import androidx.test.core.app.ActivityScenario
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runCurrent
import kotlinx.coroutines.test.runTest
import presentation.common.feature.components.BackgroundColorSemanticProperty
import presentation.common.feature.components.ContentColorSemanticProperty
import presentation.common.feature.components.DrawableResSemanticProperty
import presentation.common.feature.components.IconTintSemanticProperty
import presentation.common.feature.components.TextColorSemanticProperty
import kotlin.coroutines.CoroutineContext
import kotlin.math.abs
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

fun ComposeTestRule.hang(timeoutMillis: Long = 15_000) {
    waitUntil(timeoutMillis) { false }
}

fun ComposeTestRule.tagged(tag: String, unMergedTree: Boolean = true): ComposeTestNode {
    return ComposeTestNode(compose = this, tag = tag, unMergedTree = unMergedTree)
}

fun ComposeTestRule.allTagged(tag: String, unMergedTree: Boolean = true): SemanticsNodeInteractionCollection {
    return onAllNodes(hasTestTag(tag), unMergedTree)
}

@OptIn(ExperimentalTestApi::class)
internal fun ComposeTestRule.waitTagExists(timeoutMillis: Long = 5_000, tag: String, useUnmergedTree: Boolean = false) {
    waitThrow(timeoutMillis, hasTestTag(tag), useUnmergedTree)
}

fun SemanticsNodeInteraction.assertBackgroundColor(color: Color) = assert(hasBackgroundColor(color))
fun SemanticsNodeInteraction.assertContentColor(color: Color) = assert(hasContentColor(color))
fun SemanticsNodeInteraction.assertIconTint(tint: Color) = assert(hasIconTint(tint))
fun SemanticsNodeInteraction.assertDrawableRes(res: Int) = assert(hasIconRes(res))
fun SemanticsNodeInteraction.assertTextColor(textColor: Color) = assert(hasTextColor(textColor))

private fun hasBackgroundColor(color: Color) = SemanticsMatcher.expectValue(BackgroundColorSemanticProperty, color)
private fun hasContentColor(contentColor: Color) = SemanticsMatcher.expectValue(ContentColorSemanticProperty, contentColor)
private fun hasIconTint(iconTint: Color) = SemanticsMatcher.expectValue(IconTintSemanticProperty, iconTint)
private fun hasIconRes(res: Int) = SemanticsMatcher.expectValue(DrawableResSemanticProperty, res)
private fun hasTextColor(textColor: Color) = SemanticsMatcher.expectValue(TextColorSemanticProperty, textColor)

@OptIn(ExperimentalTestApi::class)
internal fun ComposeTestRule.waitTextExists(
    timeoutMillis: Long = 5_000,
    text: String,
    ignoreCase: Boolean = false,
    useUnmergedTree: Boolean = false,
) {
    waitThrow(timeoutMillis, hasText(text, ignoreCase), useUnmergedTree)
}

fun SemanticsNodeInteraction.getChild(block: (SemanticsNodeInteraction) -> Unit): SemanticsNodeInteraction {
    for (child in children) {
        val result = runCatching { block(child) }
        if (result.isSuccess) return child
    }

    throw AssertionError("Assert failed: none of the children matched the assertion")
}

internal fun SemanticsNodeInteraction.anyChild(block: (SemanticsNodeInteraction) -> Unit): SemanticsNodeInteraction {
    getChild(block)
    return this
}

val SemanticsNodeInteraction.children: List<SemanticsNodeInteraction>
    get() = flattenChildren()


inline fun ComposeTestRule.wait(timeoutMillis: Long = 5_000, crossinline block: () -> Unit) {
    waitThrow(timeoutMillis) { block() }
}

internal fun SemanticsNodeInteraction.displayedOrScroll(): SemanticsNodeInteraction {
    return runCatching { assertIsDisplayed() }.getOrNull() ?: performScrollTo()
}

internal fun ComposeTestRule.displayedOrScrollTo(matcher: SemanticsMatcher): SemanticsNodeInteraction {
    return runCatching { onNode(matcher).assertIsDisplayed() }.getOrNull() ?: onNode(hasScrollAction()).performScrollToNode(matcher)
}

private fun SemanticsNodeInteraction.flattenChildren(): List<SemanticsNodeInteraction> {
    val list = mutableListOf<SemanticsNodeInteraction>()
    var counter = 0

    while (true) {
        val child = getChildAt(counter) ?: break
        list.add(child)
        child.flattenChildren().forEach { list.add(it) }
        counter++
    }

    return list
}

private fun SemanticsNodeInteraction.getChildAt(index: Int): SemanticsNodeInteraction? {
    return runCatching { onChildAt(index).assertExists() }.getOrNull()
}

@OptIn(ExperimentalTestApi::class)
internal fun ComposeTestRule.waitThrow(timeoutMillis: Long, matcher: SemanticsMatcher, useUnmergedTree: Boolean) {
    runCatching {
        val result = runCatching { waitUntilAtLeastOneExists(matcher, timeoutMillis) }
        val error = result.exceptionOrNull() ?: return

        if (!error.isUnmergableError() || !useUnmergedTree) throw error

        waitThrow(timeoutMillis) { onNode(matcher, useUnmergedTree).assertExists() }
    }.onFailure {
        val description = matcher.description
        throw Exception("Timed out $timeoutMillis ms waiting for: ${description}, unmerged: $useUnmergedTree", it)
    }
}

private fun Color.compare(other: Color, tolerance: Float): Boolean {
    return abs(red - other.red) < tolerance && abs(green - other.green) < tolerance && abs(blue - other.blue) < tolerance
}

fun ComposeTestRule.waitThrow(timeoutMillis: Long, block: () -> Unit) {
    var innerError: Throwable? = null
    runCatching { waitUntil(timeoutMillis) { runCatching { block() }.onFailure { innerError = it }.isSuccess } }
        .onFailure {
            printAllTags()
            throw it.initCause(innerError).also { it.printStackTrace() }
        }
}

private fun Throwable.isUnmergableError() = message?.contains("merge function called on unmergeable property") == true

private val testTimeout = 15.seconds

fun ComposeTestRule.printAllTags() {
    runCatching { println(onRoot(true).printToString()) }.onSuccess { return }
    runCatching {
        var counter = 0
        val nodes = onAllNodes(isRoot(), true)

        while (true) {
            println(nodes[counter].printToString())
            counter++
        }
    }
}

fun commonRunTest(
    context: CoroutineContext = testDispatcher.scheduler,
    timeout: Duration = testTimeout,
    testBody: suspend TestScope.() -> Unit
) = runTest(context, timeout, testBody)

inline fun <reified T : Fragment, reified A : AppCompatActivity> ActivityScenario<A>.fragment(): T {
    return withActivity {
        val navHost = supportFragmentManager.primaryNavigationFragment!!
        navHost.childFragmentManager.fragments.find { it::class == T::class }!!
    } as T
}

inline fun <reified A : Activity, T : Any> ActivityScenario<A>.withActivity(
    crossinline block: A.() -> T
): T {
    lateinit var value: T
    var err: Throwable? = null
    onActivity { activity ->
        try {
            value = block(activity)
        } catch (t: Throwable) {
            err = t
        }
    }
    err?.let { throw it }
    return value
}

fun TestScope.progressScope() {
    runCurrent()
    advanceTimeBy(1.minutes)
    advanceUntilIdle()
    advanceUntilIdle()
}