package presentation.test

import android.app.Application
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.dp
import androidx.test.platform.app.InstrumentationRegistry
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.FirebaseCrashlytics
import feature.injection.appModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.KoinApplication
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import presentation.common.feature.components.DesignSystem

class TestApp : Application() {

    override fun onCreate() {
        super.onCreate()
        val app =
            FirebaseApp.initializeApp(InstrumentationRegistry.getInstrumentation().targetContext.applicationContext)
        app?.get(FirebaseCrashlytics::class.java)?.setCrashlyticsCollectionEnabled(false)
        DesignSystem.Corner.buttonShape = RoundedCornerShape(12.dp)
        if (koinApplication == null) {
            try {

                koinApplication = startKoin {
                    androidLogger(Level.ERROR)
                    androidContext(this@TestApp)
                    modules(appModule)
                    modules(testModule)
                }
            } catch (ex: Exception) {

            }
        }
    }

    companion object {
        private var koinApplication: KoinApplication? = null
    }
}