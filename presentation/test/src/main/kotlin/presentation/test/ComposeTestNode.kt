package presentation.test

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.test.SemanticsMatcher
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.TouchInjectionScope
import androidx.compose.ui.test.assertHasClickAction
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsEnabled
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.assertIsNotEnabled
import androidx.compose.ui.test.assertIsNotSelected
import androidx.compose.ui.test.assertIsSelected
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.ComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onParent
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performImeAction
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.compose.ui.test.performTouchInput
import androidx.compose.ui.text.AnnotatedString

class ComposeTestNode {
    private val compose: ComposeTestRule
    private val node: SemanticsNodeInteraction
    val parent: ComposeTestNode get() = ComposeTestNode(compose, node.onParent())
    private var matcher: SemanticsMatcher? = null

    constructor(compose: ComposeTestRule, tag: String, unMergedTree: Boolean = false) {
        this.compose = compose
        this.matcher = hasTestTag(tag)
        node = compose.onNodeWithTag(tag, unMergedTree)
    }

    constructor(compose: ComposeTestRule, text: String, ignoreCase: Boolean = false, unMergedTree: Boolean = false) {
        this.compose = compose
        this.matcher = hasText(text, ignoreCase)
        node = compose.onNode(matcher!!, unMergedTree)
    }

    constructor(compose: ComposeTestRule, node: SemanticsNodeInteraction) {
        this.compose = compose
        this.node = node
    }

    fun displayedOrScroll(): ComposeTestNode = wait {
        if (matcher != null) compose.displayedOrScrollTo(matcher!!) else node.displayedOrScroll()
    }
    fun assertExists(): ComposeTestNode = wait { node.assertExists() }
    fun assertDoesNotExist(): ComposeTestNode = wait { node.assertDoesNotExist() }
    fun assertIsDisplayed(): ComposeTestNode = wait { node.assertIsDisplayed() }
    fun assertIsNotDisplayed(): ComposeTestNode = wait { node.assertIsNotDisplayed() }
    fun assertIsEnabled(): ComposeTestNode = wait { node.assertIsEnabled() }
    fun assertIsNotEnabled(): ComposeTestNode = wait { node.assertIsNotEnabled() }
    fun assertHasClickAction(): ComposeTestNode = wait { node.assertHasClickAction() }
    fun assertTextEquals(text: String): ComposeTestNode = wait { node.assertTextEquals(text) }
    fun assertBackgroundColor(color: Color): ComposeTestNode = wait { node.assertBackgroundColor(color) }
    fun assertContentColor(color: Color): ComposeTestNode = wait { node.assertContentColor(color) }
    fun assertIconTint(tint: Color): ComposeTestNode = wait { node.assertIconTint(tint) }
    fun assertDrawableRes(res: Int): ComposeTestNode = wait { node.assertDrawableRes(res) }
    fun assertIsSelected(): ComposeTestNode = wait { node.assertIsSelected() }
    fun assertIsNotSelected(): ComposeTestNode = wait { node.assertIsNotSelected() }
    fun waitExists(timeoutMillis: Long = 5_000) = wait(timeoutMillis) { assertExists() }
    fun anyChild(block: (SemanticsNodeInteraction) -> Unit) = wait { node.anyChild { block(it) } }

    fun getChild(block: (SemanticsNodeInteraction) -> Unit) = ComposeTestNode(compose, node.getChild {
        block(it)
    })

    fun performClick(confirmation: () -> Unit): ComposeTestNode = wait {
        node.performClick()
        confirmation()
    }

    fun performImeAction(confirmation: () -> Unit) = wait {
        node.performImeAction()
        confirmation()
    }

    fun performTouchInput(block: TouchInjectionScope.() -> Unit, confirmation: () -> Unit) = wait {
        node.performTouchInput(block)
        confirmation()
    }

    fun performTextClearance() = wait {
        node.performTextClearance()
        node.assertTextEquals("")
    }

    fun performTextInput(text: String, isPassword: Boolean = false) = wait {
        node.performTextClearance()
        node.performTextInput(text)
        node.assertTextEquals(if (!isPassword) text else text.passwordText())
    }

    private fun String.passwordText(mask: Char = '\u2022'): String {
        return AnnotatedString(mask.toString().repeat(length)).text
    }

    private fun wait(timeoutMillis: Long = 5_000, block: () -> Unit): ComposeTestNode {
        compose.wait(timeoutMillis, block)
        return this
    }
}