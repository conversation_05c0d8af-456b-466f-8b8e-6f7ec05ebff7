package presentation.test

import app.cash.sqldelight.async.coroutines.synchronous
import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.android.AndroidSqliteDriver
import core.caching.KeyValueCache
import core.caching.test.MockKeyValueCache
import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.coroutines.CoroutineDispatchers
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.location.common.LocationRepository
import core.location.common.test.MockLocationRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.test.CLILogger
import core.permissions.manager.PermissionsManager
import core.permissions.manager.test.MockPermissionsManager
import data.database.extensions.createOrMigrate
import keyless.data.database.Keyless
import kotlinx.coroutines.runBlocking
import org.koin.dsl.module

internal val testModule = module {
    single<Logger> { CLILogger(Unit) }
    single<KeyValueCache> { MockKeyValueCache(get()) }
    single<SqlDriver> {
        AndroidSqliteDriver(Keyless.Schema.synchronous(), get(), null).apply {
            runCatching { runBlocking { Keyless.Schema.createOrMigrate(this@apply) } }
        }
    }
    single<HttpClient> { MockKtorHttpClient(get()) }
    single<LocationRepository> { MockLocationRepository() }
    single<PermissionsManager> { MockPermissionsManager(get()) }
    single<AbstractCoroutineDispatcher> {
        CoroutineDispatchers(
            main = testDispatcher,
            default = testDispatcher,
            io = testDispatcher,
            backgroundWork = testDispatcher
        )
    }
}