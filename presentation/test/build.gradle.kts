plugins {
    id("keyless.android.feature")
    id("keyless.android.compose")
}

android {
    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            animationsDisabled = true
            all {
                it.jvmArgs("-noverify")

            }
        }
        animationsDisabled = true
    }
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-http-client-test"))
    implementation(project(":core-monitoring-test"))
    implementation(project(":core-caching-key-value-test"))
    implementation(project(":core-location-common-test"))
    implementation(project(":core-permissions-manager-test"))

    implementation(project(":data-common"))
    implementation(project(":data-database"))

    implementation(project(":feature-injection"))
    implementation(project(":presentation-common"))

    implementation(keyless.sqldelight.runtime)
    implementation(keyless.sqldelight.android.driver)

    implementation(platform(keyless.firebase.bom))
    implementation(keyless.firebase.crashlytics.ktx)

    api(keyless.androidx.test.core.ktx)
    api(keyless.androidx.test.junit.ktx)
    api(keyless.androidx.test.rules)
    api(keyless.androidx.test.runner)


    api(keyless.koin.test)
    api(keyless.koin.test.junit)
    api(keyless.kotlin.test)
    api(keyless.kotlinx.coroutines.test)


    implementation("junit:junit:4.13.2")
    api(keyless.androidx.compose.ui.test.junit4)
    debugImplementation("androidx.compose.ui:ui-test-manifest:1.6.4")
}