package presentation.authentication.verifyotp.test

import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.runner.RunWith
import org.robolectric.annotation.Config
import presentation.test.TestApp


@RunWith(AndroidJUnit4::class)
@Config(application = TestApp::class, sdk = [28], qualifiers = "w1080dp-h1920dp-xxhdpi")
class VerifyOtpSharedUnitTests : VerifyOtpSharedTests() {
    override val isUnitTest = true
}
