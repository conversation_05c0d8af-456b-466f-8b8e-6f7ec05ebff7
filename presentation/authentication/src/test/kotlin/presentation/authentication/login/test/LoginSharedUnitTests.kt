package presentation.authentication.login.test

import android.app.Activity
import androidx.test.core.app.ActivityScenario
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import org.junit.runner.RunWith
import org.robolectric.Shadows
import org.robolectric.annotation.Config
import presentation.common.feature.components.WebActivity
import presentation.test.TestApp
import kotlin.coroutines.resume
import kotlin.test.assertEquals

@RunWith(AndroidJUnit4::class)
@Config(application = TestApp::class, sdk = [28], qualifiers = "w1080dp-h1920dp-xxhdpi")
internal class LoginSharedUnitTests : LoginSharedTests() {

    override fun assertLaunchWebActivity() {
        val activity = runBlocking {
            suspendCancellableCoroutine { continuation -> scenario.onActivity { continuation.resume(it) } }
        }
        val shadow = Shadows.shadowOf(activity)

        assertEquals("https://login.phase1-staging.keyless-testing.com/forgot-password/", shadow.nextStartedActivity.getStringExtra("url"))
        scenario.close()

        @Suppress("UNCHECKED_CAST")
        scenario = ActivityScenario.launch(WebActivity::class.java as Class<Activity>)
    }
}