package presentation.authentication.verifyphone.domain.models

import core.common.status.Status
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.updateAndGet
import kotlinx.coroutines.launch

internal data class ScreenData(
    val status: List<Status>,
    private val timer: MutableStateFlow<Int>
) {
    val timerStream = timer.asStateFlow()

    fun launchTimer(dispatcher: CoroutineDispatcher) {
        timer.update { 60 }

        CoroutineScope(dispatcher).launch {
            runCatching {
                while (timerStream.value > 0) {
                    ensureActive()
                    delay(1000)
                    timer.updateAndGet { it - 1 }
                }
            }
        }
    }


    companion object {
        val empty = ScreenData(status = emptyList(), timer = MutableStateFlow(60))
    }
}
