package presentation.authentication.verifyphone.domain.usecases

import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.utils.isValidMobile
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.SendOtpResponse
import keyless.presentation.common.R
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SendOtpEvent
import presentation.authentication.verifyphone.domain.models.SideEffect
import presentation.authentication.verifyphone.domain.models.toRequest
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class SendOtpUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val screen: ScreenDataRepository<ScreenData>,
    private val dispatchers: AbstractCoroutineDispatcher,
) {

    suspend fun execute(event: SendOtpEvent) = logger.async {
        if (!validate(event.mobileNumber)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: SendOtpEvent) = logger.async {
        val response = remoteJob(event)
        screen.current.launchTimer(dispatchers.backgroundWork)
        if (!response.isSuccess) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun remoteJob(event: SendOtpEvent) = logger.async {
        when (event) {
            is SendOtpEvent.SendOtpSms -> repository.sendOtpSms(event.toRequest())
            is SendOtpEvent.SendOtpWhatsApp -> repository.sendOtpWhatsApp(event.toRequest())
            is SendOtpEvent.SendOtpPhoneCall -> repository.sendOtpPhoneCall(event.toRequest())
        }
    }

    private suspend fun validate(mobileNumber: String): Boolean = logger.async {
        if (mobileNumber.isBlank()) {
            status.info(Message(R.string.please_enter_mobile_number))
            return@async false
        } else if (!mobileNumber.isValidMobile()) {
            status.info(Message(R.string.please_enter_valid_mobile_number))
            return@async false
        }

        return@async true
    }

    private suspend fun handleFailResponse(response: SendOtpResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: SendOtpResponse): Unit = logger.async {
        sideEffects.emit(SideEffect.NavToVerifyOtp)
    }
}
