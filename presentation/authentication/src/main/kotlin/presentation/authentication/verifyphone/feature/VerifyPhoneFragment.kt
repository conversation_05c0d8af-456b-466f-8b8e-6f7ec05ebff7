package presentation.authentication.verifyphone.feature

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import core.common.status.StatusRepository
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.verifyotp.feature.VerifyOtpFragment
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SideEffect
import presentation.common.feature.components.safeNavigate
import presentation.common.feature.components.successDialogWithOkButton
import presentation.common.feature.theme.AppTheme

class VerifyPhoneFragment : Fragment() {

    private val viewModel by viewModel<VerifyPhoneAndroidViewModel>()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }

        requireActivity().onBackPressedDispatcher.addCallback(this) {
            if (viewModel.ui.isSelectCountry.get()) viewModel.ui.isSelectCountry.update(false)
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val data = viewModel.screenData.collectAsStateWithLifecycle(initialValue = ScreenData.empty)
        val state = remember { viewModel.stateHolder(data) }

        VerifyPhoneScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToVerifyOtp -> {
            navigateToVerifyOtp()
        }
    }

    private fun navigateToVerifyOtp() {
        val args = Bundle().apply {
            putString(VerifyOtpFragment.countryCodeKey, viewModel.ui.countryCode())
            putString(VerifyOtpFragment.phoneNumberKey, viewModel.ui.mobileNumber())
        }
        findNavController().safeNavigate(
            keyless.presentation.authentication.R.id.verifyPhoneFragment,
            keyless.presentation.authentication.R.id.action_verifyPhone_to_verifyOtp,
            args
        )
    }
}