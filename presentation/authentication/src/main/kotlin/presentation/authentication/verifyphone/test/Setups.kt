package presentation.authentication.verifyphone.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.SendOtpResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.login.test.LoginNodes.emailInput
import presentation.authentication.login.test.LoginNodes.loginButton
import presentation.authentication.login.test.LoginNodes.passwordInput
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screenTitle

internal object Setups: KoinTest {

    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupGuestLoginPhone() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 0, userType = "Guest")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)

        compose.emailInput.performTextInput("username")
        compose.passwordInput.performTextInput("password", true)
        compose.loginButton.performClick { compose.screenTitle.assertExists() }
    }

    fun TestsSuite.setupSendOtpMobile() {
        val url = "${ConfigValues.baseUrl}/user/sendotp_mobile"
        val response = MockHttpResponse(url = url, method = "POST", body = MockResponses.postUserSendOtpMobile.raw)
        client.setup(response)
    }

    fun TestsSuite.setupSendOtpWhatsApp() {
        val url = "${ConfigValues.baseUrl}/user/sendotp-whatsapp"
        val response = MockHttpResponse(url = url, method = "POST", body = MockResponses.postUserSendOtpWhatsapp.raw)
        client.setup(response)
    }

    fun TestsSuite.setupSendOtpPhoneCall() {
        val url = "${ConfigValues.baseUrl}/user/send-voice-otp"
        val response = MockHttpResponse(url = url, method = "POST", body = MockResponses.postUserSendOtpCall.raw)
        client.setup(response)
    }

    fun TestsSuite.setupSendOtpFailWaitAMinute(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/sendotp_mobile"
        val body = MockResponses.postUserSendOtpCallFailWaitAMinute
        val response = MockHttpResponse(url = url, method = "POST", body = body.raw)
        client.setup(response)
        return body.value<SendOtpResponse>()
    }

    fun TestsSuite.setupSendOtpFailWaitAMinuteWhatsApp() {
        val url = "${ConfigValues.baseUrl}/user/sendotp-whatsapp"
        val body = MockResponses.postUserSendOtpCallFailWaitAMinute.raw
        val response = MockHttpResponse(url = url, method = "POST", body = body)
        client.setup(response)
    }

    fun TestsSuite.setupSendOtpFailWaitAMinutePhoneCall() {
        val url = "${ConfigValues.baseUrl}/user/send-voice-otp"
        val body = MockResponses.postUserSendOtpCallFailWaitAMinute.raw
        val response = MockHttpResponse(url = url, method = "POST", body = body)
        client.setup(response)
    }
}