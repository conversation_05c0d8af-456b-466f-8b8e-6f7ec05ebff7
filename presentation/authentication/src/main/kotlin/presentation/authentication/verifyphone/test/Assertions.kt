package presentation.authentication.verifyphone.test

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.assertCountEquals
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.performClick
import keyless.presentation.common.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import presentation.authentication.verifyotp.test.VerifyOtpNodes.screen
import presentation.authentication.verifyphone.test.Setups.setupSendOtpFailWaitAMinute
import presentation.authentication.verifyphone.test.Setups.setupSendOtpFailWaitAMinutePhoneCall
import presentation.authentication.verifyphone.test.Setups.setupSendOtpFailWaitAMinuteWhatsApp
import presentation.authentication.verifyphone.test.Setups.setupSendOtpWhatsApp
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.callButton
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.countryCode
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.mobileNumber
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screenDisclaimer
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screenSubTitle
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screenTitle
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.selectCountryItem
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.selectCountryScreen
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.selectCountrySearch
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.smsButton
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.whatsappButton
import presentation.common.feature.theme.color_on_whatsapp_light
import presentation.common.feature.theme.color_whatsapp_light
import presentation.common.feature.theme.lightColorScheme
import presentation.common.test.assertBackButtonClick
import presentation.common.test.assertBackButtonLayout
import presentation.common.test.assertDialogReturnButton
import presentation.common.test.assertToast
import presentation.test.ComposeTestNode
import presentation.test.allTagged
import presentation.test.assertDrawableRes

internal fun TestsSuite.assertVerifyPhoneScreenLayout() {
    assertScreenTitleLayout()
    assertScreenSubTitleLayout()
    assertScreenDisclaimerLayout()
    assertCountryCodeLayout()
    assertMobileNumberLayout()
    assertWhatsappButtonLayout(isGreenColor = false)
    assertSmsButtonLayout()
    assertCallButtonLayout()
}

internal fun TestsSuite.selectCountryScreenLayout() {
    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    compose.selectCountryScreen.assertExists()
    assertSelectCountrySearchLayout()
    compose.assertBackButtonLayout(true)
    assertSelectCountryItemLayout("Bahrain", com.hbb20.R.drawable.flag_bahrain, "+973")
    assertSelectCountryItemLayout("Egypt", com.hbb20.R.drawable.flag_egypt, "+20")
    assertSelectCountryItemLayout("United Arab Emirates (UAE)", com.hbb20.R.drawable.flag_uae, "+971")
}

internal fun TestsSuite.assertSelectCountrySearch() {
    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    assertSelectCountrySearchInput("Bahrain")
    assertSelectCountrySearchInput("United Arab Emirates (UAE)")
}

internal fun TestsSuite.assertSelectCountryItemClick() {
    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    assertSelectCountryItemClick("Egypt") { assertCountryCodeLayout(com.hbb20.R.drawable.flag_egypt) }

    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    assertSelectCountryItemClick("Bahrain") { assertCountryCodeLayout(com.hbb20.R.drawable.flag_bahrain) }

    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    assertSelectCountryItemClick("United Arab Emirates (UAE)") { assertCountryCodeLayout() }
}

internal fun TestsSuite.assertScreenBackDisabled() {
    compose.assertBackButtonLayout(false)

    (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed()
    assertScreenTitleLayout()
}

internal fun TestsSuite.assertSelectCountryBackEnabled() {
    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed()
    assertScreenTitleLayout()

    assertCountryCodeClick { compose.selectCountryScreen.assertExists() }
    compose.assertBackButtonClick { compose.selectCountryScreen.assertDoesNotExist() }
    assertScreenTitleLayout()
}

internal fun TestsSuite.asserMobileNumberInputInteractionAndButtonColors() {
    assertMobileNumberInput("123456")
    assertWhatsappButtonLayout(isGreenColor = false)
    assertSmsButtonLayout()
    assertCallButtonLayout()

    assertMobileNumberInput("1234567")
    assertWhatsappButtonLayout(isGreenColor = true)
    assertSmsButtonLayout()
    assertCallButtonLayout()

    assertMobileNumberInput("123456789012345")
    assertWhatsappButtonLayout(isGreenColor = true)

    assertMobileNumberInput("1234567890123456")
    assertWhatsappButtonLayout(isGreenColor = false)
}

internal fun TestsSuite.assertMobileNumberInputValidation() {
    assertActionButton(compose.whatsappButton)
    assertActionButton(compose.smsButton)
    assertActionButton(compose.callButton)
}

internal fun TestsSuite.assertActionButton(node: ComposeTestNode) {
    node.assertIsDisplayed().assertIsEnabled()

    assertMobileNumberInput("")
    node.performClick { compose.assertToast(activity.getString(R.string.please_enter_mobile_number)) }
    clearToast()

    assertMobileNumberInput("123456")
    node.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_mobile_number)) }
    clearToast()

    assertMobileNumberInput("1234567")
    node.performClick { compose.screen.assertExists() }
    testScope.launch(Dispatchers.Main) { (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed() }

    assertMobileNumberInput("1234567890123456")
    node.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_mobile_number)) }
    clearToast()
}

internal fun TestsSuite.assertFailedWaitAMinute() {
    setupSendOtpWhatsApp()

    assertMobileNumberInput("1234567")
    assertWhatsappButtonLayout(isGreenColor = true)
    compose.whatsappButton.assertIsDisplayed().assertIsEnabled().performClick { compose.screen.assertExists() }

    compose.assertBackButtonClick { compose.screen.assertDoesNotExist() }
    assertScreenTitleLayout()

    val response = setupSendOtpFailWaitAMinute()
    setupSendOtpFailWaitAMinuteWhatsApp()
    setupSendOtpFailWaitAMinutePhoneCall()

    compose.smsButton.performClick { compose.assertDialogReturnButton(response.message).performClick() }
    compose.callButton.performClick { compose.assertDialogReturnButton(response.message).performClick() }
    compose.whatsappButton.performClick { compose.assertDialogReturnButton(response.message).performClick() }
}

internal fun TestsSuite.assertScreenTitleLayout() {
    val node = compose.screenTitle

    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.add_your_mobile_number))
}

internal fun TestsSuite.assertScreenSubTitleLayout() {
    val node = compose.screenSubTitle

    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.we_ll_need_to_confirmation))
}

internal fun TestsSuite.assertScreenDisclaimerLayout() {
    val node = compose.screenDisclaimer

    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.by_continuing_you_confirm_that_you))
}

internal fun TestsSuite.assertCountryCodeLayout(res: Int = com.hbb20.R.drawable.flag_uae) {
    val node = compose.countryCode
    val image = node.getChild { it.assertIsDisplayed().assertDrawableRes(res) }

    node.assertIsDisplayed().assertIsEnabled()
    image.assertIsDisplayed()
}

internal fun TestsSuite.assertCountryCodeClick(confirmation: () -> Unit) {
    val node = compose.countryCode

    node.assertIsDisplayed().assertIsEnabled().performClick(confirmation)
}

internal fun TestsSuite.assertMobileNumberLayout() {
    val node = compose.mobileNumber

    node.assertIsDisplayed().assertIsEnabled()
}

internal fun TestsSuite.assertMobileNumberInput(input: String) {
    val node = compose.mobileNumber

    node.assertIsDisplayed().assertIsEnabled().performTextClearance().performTextInput(input).assertTextEquals(input)
}

internal fun TestsSuite.assertWhatsappButtonLayout(isGreenColor: Boolean) {
    val node = compose.whatsappButton

    node.assertIsDisplayed().assertIsEnabled()

    if (!isGreenColor) {
        node.assertBackgroundColor(lightColorScheme.tertiary).assertContentColor(lightColorScheme.onTertiary)
    } else {
        node.assertBackgroundColor(color_whatsapp_light).assertContentColor(color_on_whatsapp_light)
    }
}

internal fun TestsSuite.assertSmsButtonLayout() {
    val node = compose.smsButton

    node.assertIsDisplayed().assertIsEnabled()
        .assertBackgroundColor(lightColorScheme.tertiary).assertContentColor(lightColorScheme.onTertiary)
}

internal fun TestsSuite.assertCallButtonLayout() {
    val node = compose.callButton

    node.assertIsDisplayed().assertIsEnabled()
        .assertBackgroundColor(lightColorScheme.tertiary).assertContentColor(lightColorScheme.onTertiary)
}

internal fun TestsSuite.assertSelectCountrySearchLayout() {
    val node = compose.selectCountrySearch
    val hint = node.getChild { it.assertTextEquals(activity.getString(R.string.search)) }

    node.assertIsDisplayed().assertIsEnabled()
    hint.assertIsDisplayed()
}

internal fun TestsSuite.assertSelectCountrySearchInput(input: String) {
    val node = compose.selectCountrySearch
    val item = compose.selectCountryItem(input)

    node.assertIsDisplayed().assertIsEnabled().performTextInput(input).assertTextEquals(input)
    item.assertIsDisplayed()
    compose.allTagged(TestTags.selectCountryItem).assertCountEquals(1)
    node.performTextClearance()
}

internal fun TestsSuite.assertSelectCountryItemLayout(name: String, flag: Int, code: String) {
    val node = compose.selectCountryItem(name)
    node.displayedOrScroll()

    val image = node.getChild { it.assertDrawableRes(flag) }
    val text = node.getChild { it.assertTextEquals(name) }
    val code = node.getChild { it.assertTextEquals(code) }

    image.assertIsDisplayed()
    text.assertIsDisplayed()
    code.assertIsDisplayed()
}

internal fun TestsSuite.assertSelectCountryItemClick(name: String, confirmation: () -> Unit) {
    val node = compose.selectCountryItem(name)
    node.displayedOrScroll()

    node.assertIsDisplayed().performClick(confirmation)
}

internal fun TestsSuite.clearToast() {
    advanceBackground(100000)
    compose.assertToast(null)
}