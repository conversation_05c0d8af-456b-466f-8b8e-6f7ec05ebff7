package presentation.authentication.verifyphone.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object TestTags {
    val tag = this::class.java.name
    val screen = tag + ".screen"
    val screenTitle = tag + ".screenTitle"
    val screenSubTitle = tag + ".screenSubTitle"
    val screenDisclaimer = tag + ".screenDisclaimer"
    val countryCode = tag + ".countryCode"
    val mobileNumber = tag + ".mobileNumber"
    val whatsappButton = tag + ".whatsappButton"
    val smsButton = tag + ".smsButton"
    val callButton = tag + ".callButton"

    val selectCountryScreen = tag + ".selectCountryScreen"
    val selectCountrySearch = tag + ".selectCountrySearch"
    val selectCountryItem = tag + ".selectCountryItem"
    fun selectCountryItem(name: String) = tag + ".selectCountryItem.$name"
}

internal object VerifyPhoneNodes {
    val ComposeTestRule.screen get() = tagged(TestTags.screen)
    val ComposeTestRule.screenTitle get() = tagged(TestTags.screenTitle)
    val ComposeTestRule.screenSubTitle get() = tagged(TestTags.screenSubTitle)
    val ComposeTestRule.screenDisclaimer get() = tagged(TestTags.screenDisclaimer)
    val ComposeTestRule.countryCode get() = tagged(TestTags.countryCode)
    val ComposeTestRule.mobileNumber get() = tagged(TestTags.mobileNumber)
    val ComposeTestRule.whatsappButton get() = tagged(TestTags.whatsappButton)
    val ComposeTestRule.smsButton get() = tagged(TestTags.smsButton)
    val ComposeTestRule.callButton get() = tagged(TestTags.callButton)

    val ComposeTestRule.selectCountryScreen get() = tagged(TestTags.selectCountryScreen)
    val ComposeTestRule.selectCountrySearch get() = tagged(TestTags.selectCountrySearch)
    fun ComposeTestRule.selectCountryItem(name: String) = tagged(TestTags.selectCountryItem(name))
}


internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: VerifyPhoneSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}