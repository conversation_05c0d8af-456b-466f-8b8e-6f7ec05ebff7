package presentation.authentication.verifyphone.domain.models

internal sealed interface Event

internal sealed class SendOtpEvent(val countryCode: String, val mobileNumber: String) : Event {
    class SendOtpSms(countryCode: String, mobileNumber: String) : SendOtpEvent(countryCode, mobileNumber)

    class SendOtpWhatsApp(countryCode: String, mobileNumber: String) : SendOtpEvent(countryCode, mobileNumber)

    class SendOtpPhoneCall(countryCode: String, mobileNumber: String) : SendOtpEvent(countryCode, mobileNumber)
}

internal sealed interface ScreenEvent : Event
