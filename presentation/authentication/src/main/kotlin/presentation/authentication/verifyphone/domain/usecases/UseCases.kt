package presentation.authentication.verifyphone.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.common.coroutines.AbstractCoroutineDispatcher
import data.keyless.authentication.AuthenticationRepository
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SideEffect
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class UseCases(
    private val authentication: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val dispatchers: AbstractCoroutineDispatcher
) {

    val sendOtp = SendOtpUseCase(authentication, logger, status, sideEffects, dispatchers =  dispatchers, screen = screenData)
}
