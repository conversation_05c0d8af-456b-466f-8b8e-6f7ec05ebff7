package presentation.authentication.verifyphone.domain

import presentation.authentication.verifyphone.domain.models.Event
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SendOtpEvent
import presentation.authentication.verifyphone.domain.models.SideEffect
import presentation.authentication.verifyphone.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is SendOtpEvent -> onUserAction(event)
    }

    private suspend fun onUserAction(event: SendOtpEvent) = when (event) {
        is SendOtpEvent.SendOtpSms -> useCases.sendOtp.execute(event)
        is SendOtpEvent.SendOtpWhatsApp -> useCases.sendOtp.execute(event)
        is SendOtpEvent.SendOtpPhoneCall -> useCases.sendOtp.execute(event)
    }
}
