package presentation.authentication.verifyphone

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.verifyphone.domain.ViewModel
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SideEffect
import presentation.authentication.verifyphone.domain.usecases.UseCases
import presentation.authentication.verifyphone.feature.VerifyPhoneAndroidViewModel
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object VerifyPhoneInjectionScope

val verifyPhoneInjection = module {
    scope<VerifyPhoneInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped {
            UseCases(
                authentication = get(),
                logger = get(),
                status = get(),
                screenData = get(),
                sideEffects = get(),
                dispatchers = get()
            )
        }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<VerifyPhoneInjectionScope>(VerifyPhoneInjectionScope.getScopeId())
        VerifyPhoneAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
