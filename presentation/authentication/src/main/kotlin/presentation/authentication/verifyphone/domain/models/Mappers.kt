package presentation.authentication.verifyphone.domain.models

import data.common.preferences.Preferences
import data.keyless.authentication.models.SendOtpMobileRequest
import data.common.utils.phoneNumberWithPlus

internal fun SendOtpEvent.SendOtpSms.toRequest() = SendOtpMobileRequest(
    countryCode = countryCode.phoneNumberWithPlus(),
    mobileNumber = mobileNumber,
    userType = Preferences.userRole.get(),
    userId = Preferences.userId.get()
)

internal fun SendOtpEvent.SendOtpWhatsApp.toRequest() = SendOtpMobileRequest(
    countryCode = countryCode.phoneNumberWithPlus(),
    mobileNumber = mobileNumber,
    userType = Preferences.userRole.get(),
    userId = Preferences.userId.get()
)

internal fun SendOtpEvent.SendOtpPhoneCall.toRequest() = SendOtpMobileRequest(
    countryCode = countryCode.phoneNumberWithPlus(),
    mobileNumber = mobileNumber,
    userType = Preferences.userRole.get(),
    userId = Preferences.userId.get()
)