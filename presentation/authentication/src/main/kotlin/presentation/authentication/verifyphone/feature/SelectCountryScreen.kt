package presentation.authentication.verifyphone.feature

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.hbb20.CCPCountry
import keyless.presentation.common.R
import kotlinx.coroutines.flow.map
import presentation.authentication.verifyphone.test.TestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppHorizontalDivider
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.AppSearchField
import presentation.common.feature.components.AppTitledPage
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState
import presentation.common.feature.theme.AppTheme

private class CountriesStateHolder(val ui: CountriesUIStateHolder = CountriesUIStateHolder())
private class CountriesUIStateHolder(
    val search: StringState = StringState(""),
) {
    private val allCountries = CCPCountry.getLibraryMasterCountriesEnglish()

    val filtered = DerivedState(
        allCountries,
        search.stream.map { search -> allCountries.sortedBy { it.name }.filter { it.name.contains(search, true) } }
    )
}

@Composable
internal fun SelectCountryScreen(
    onBackClick: () -> Unit,
    onSelect: (CCPCountry) -> Unit
) {
    val state = remember { CountriesStateHolder() }

    AppTitledPage(
        modifier = Modifier.testTag(TestTags.selectCountryScreen),
        title = stringResource(R.string.country_list),
        isBackEnabled = true,
        onBackPress = onBackClick,
        status = emptyList(),
        onCancelStatus = {}
    ) {

        ScreenLayout(state = state, onSelect = onSelect)
    }
}

@Composable
private fun ScreenLayout(state: CountriesStateHolder, onSelect: (CCPCountry) -> Unit) {
    val countries = state.ui.filtered.value

    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        AppSearchField(
            modifier = Modifier.fillMaxWidth().testTag(TestTags.selectCountrySearch),
            state = state.ui.search,
            onClear = { state.ui.search.update("") }
        )

        AppScrollColumn {
            itemsIndexed(countries, { index, item -> item.name }) { index, country ->
                CountryItem(
                    modifier = Modifier
                        .clickable { onSelect(country) }
                        .padding(vertical = DesignSystem.Padding.medium),
                    country = country
                )

                if (index < countries.size - 1) AppHorizontalDivider()
            }
        }
    }
}

@Composable
private fun CountryItem(modifier: Modifier = Modifier, country: CCPCountry) {
    AppRow(
        modifier = modifier
            .fillMaxWidth()
            .testTag(TestTags.selectCountryItem(country.name.trim())),
        arrangement = Arrangement.SpaceBetween,
        alignment = Alignment.CenterVertically
    ) {
        AppRow(
            modifier = Modifier.weight(1f).testTag(TestTags.selectCountryItem),
            alignment = Alignment.CenterVertically
        ) {
            CountryImage(country)

            AppBodyText(text = country.name.trim())
        }

        AppBodyText(text = "+${country.phoneCode}")
    }
}

@Composable
internal fun CountryImage(country: CCPCountry) {
    AppImage(
        modifier = Modifier
            .size(36.dp)
            .clip(CircleShape),
        res = country.flagID,
        contentScale = ContentScale.FillBounds
    )
}

@Preview
@Composable
private fun Preview() {
    AppTheme { SelectCountryScreen(onBackClick = {}, onSelect = {}) }
}