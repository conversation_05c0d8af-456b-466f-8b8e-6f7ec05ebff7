package presentation.authentication.verifyphone.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.login.loginInjection
import presentation.authentication.verifyotp.verifyOtpInjection
import presentation.authentication.verifyphone.test.Setups.setupGuestLoginPhone
import presentation.authentication.verifyphone.test.Setups.setupSendOtpMobile
import presentation.authentication.verifyphone.test.Setups.setupSendOtpPhoneCall
import presentation.authentication.verifyphone.test.Setups.setupSendOtpWhatsApp
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class VerifyPhoneSharedTests : KoinTest {
    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.LOGIN } },
                loginInjection, verifyPhoneInjection, verifyOtpInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        scenario.close()
        Setups.clear()
    }

    @Test
    fun assertVerifyPhoneScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertVerifyPhoneScreenLayout()
    }

    @Test
    fun assertSelectCountryScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.selectCountryScreenLayout()
    }

    @Test
    fun assertSelectCountrySearch() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertSelectCountrySearch()
    }

    @Test
    fun assertSelectCountryItemClick() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertSelectCountryItemClick()
    }

    @Test
    fun assertSelectCountryBackEnabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertSelectCountryBackEnabled()
    }

    @Test
    fun assertScreenBackDisabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertScreenBackDisabled()
    }

    @Test
    fun asserMobileNumberInputInteractionAndButtonColors() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.asserMobileNumberInputInteractionAndButtonColors()
    }

    @Test
    fun assertMobileNumberInputValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.setupSendOtpMobile()
        suite.setupSendOtpWhatsApp()
        suite.setupSendOtpPhoneCall()
        suite.assertMobileNumberInputValidation()
    }

    @Test
    fun assertFailedWaitAMinute() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyPhoneSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertFailedWaitAMinute()
    }
}