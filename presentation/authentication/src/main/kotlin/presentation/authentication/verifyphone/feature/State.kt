package presentation.authentication.verifyphone.feature

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import com.hbb20.CCPCountry
import data.common.utils.isValidMobile
import data.common.utils.phoneNumberWithPlus
import kotlinx.coroutines.flow.map
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.MutableItemState
import presentation.common.feature.state.StringState
import presentation.common.feature.theme.onWhatsapp
import presentation.common.feature.theme.whatsapp

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>
) {

    fun countryCode() = ui.countryCode()
    fun mobileNumber() = ui.mobileNumber()
}

internal class UIStateHolder(
    val phoneNumber: StringState = StringState(""),
    val isSelectCountry: BooleanState = BooleanState(false),
    val country: MutableItemState<CCPCountry> = MutableItemState(
        CCPCountry.getLibraryMasterCountriesEnglish().first { it.nameCode.equals("AE", true) }
    )
) {

    val actionButtonEnabled = DerivedState(
        phoneNumber.get().isValidMobile(),
        phoneNumber.stream.map { it.isValidMobile() }
    )

    val whatsappBackgroundColor
        @Composable get() = if (actionButtonEnabled.value) MaterialTheme.colorScheme.whatsapp
        else MaterialTheme.colorScheme.tertiary
    val whatsappContentColor
        @Composable get() = if (actionButtonEnabled.value) MaterialTheme.colorScheme.onWhatsapp
        else MaterialTheme.colorScheme.onTertiary

    fun countryCode() = country.get().phoneCode.phoneNumberWithPlus()
    fun mobileNumber() = phoneNumber.get()
}