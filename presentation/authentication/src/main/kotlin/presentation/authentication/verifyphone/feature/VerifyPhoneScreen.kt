package presentation.authentication.verifyphone.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import keyless.presentation.common.R
import presentation.authentication.verifyphone.domain.models.Event
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.authentication.verifyphone.domain.models.SendOtpEvent
import presentation.authentication.verifyphone.test.TestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.components.verticalScrollAdaptToIme
import presentation.common.feature.theme.AppTheme

@Composable
internal fun VerifyPhoneScreen(
    state: StateHolder,
    onCancelStatus: (Status) -> Unit,
    onEvent: (Event) -> Unit
) {
    if (!state.ui.isSelectCountry.value) {
        AppLogoPage(
            modifier = Modifier.testTag(TestTags.screen),
            status = state.data.value.status,
            isBackEnabled = false,
            onCancelStatus = onCancelStatus,
            onBackPress = {}
        ) {
            ScreenLayout(
                state, onToggleCountryList = { state.ui.isSelectCountry.update(it) },
                onEvent = onEvent
            )
        }
    } else {
        SelectCountryScreen(
            onBackClick = { state.ui.isSelectCountry.update(false) },
            onSelect = {
                state.ui.country.update(it)
                state.ui.isSelectCountry.update(false)
            }
        )
    }
}

@Composable
private fun ScreenLayout(state: StateHolder, onEvent: (Event) -> Unit, onToggleCountryList: (Boolean) -> Unit) {
    AppColumn(
        modifier = Modifier
            .fillMaxHeight()
            .verticalScrollAdaptToIme(),
        arrangement = Arrangement.SpaceBetween
    ) {
        MobileNumberSection(state = state, toggleCountryList = onToggleCountryList)

        SendActionSection(state, onEvent)
    }
}

@Composable
private fun MobileNumberSection(
    state: StateHolder,
    toggleCountryList: (Boolean) -> Unit
) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        AppPageTitleText(modifier = Modifier.testTag(TestTags.screenTitle), text = stringResource(R.string.add_your_mobile_number))

        AppBodyText(modifier = Modifier.testTag(TestTags.screenSubTitle), text = stringResource(R.string.we_ll_need_to_confirmation))

        MobileTextField(state, toggleCountryList = toggleCountryList)

        AppLabelText(modifier = Modifier.testTag(TestTags.screenDisclaimer), text = stringResource(R.string.by_continuing_you_confirm_that_you))
    }
}

@Composable
private fun SendActionSection(state: StateHolder, onEvent: (Event) -> Unit) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        AppButton(
            modifier = Modifier.testTag(TestTags.whatsappButton),
            text = stringResource(R.string.receive_by_whatsapp),
            design = DesignSystem.Button.Normal.copy(
                backgroundColor = state.ui.whatsappBackgroundColor,
                contentColor = state.ui.whatsappContentColor
            ),
            onClick = {
                onEvent(
                    SendOtpEvent.SendOtpWhatsApp(countryCode = state.countryCode(), mobileNumber = state.mobileNumber())
                )
            }
        )
        AppButton(
            modifier = Modifier.testTag(TestTags.smsButton),
            text = stringResource(R.string.receive_by_phone_sms),
            design = DesignSystem.Button.Normal.copy(
                backgroundColor = MaterialTheme.colorScheme.tertiary,
                contentColor = MaterialTheme.colorScheme.onTertiary
            ),
            onClick = {
                onEvent(
                    SendOtpEvent.SendOtpSms(countryCode = state.countryCode(), mobileNumber = state.mobileNumber())
                )
            }
        )
        AppButton(
            modifier = Modifier.testTag(TestTags.callButton),
            text = stringResource(R.string.receive_by_phone_call),
            design = DesignSystem.Button.Normal.copy(
                backgroundColor = MaterialTheme.colorScheme.tertiary,
                contentColor = MaterialTheme.colorScheme.onTertiary
            ),
            onClick = {
                onEvent(
                    SendOtpEvent.SendOtpPhoneCall(
                        countryCode = state.countryCode(),
                        mobileNumber = state.mobileNumber()
                    )
                )
            }
        )
    }
}

@Composable
private fun MobileTextField(state: StateHolder, toggleCountryList: (Boolean) -> Unit) {
    AppRow(
        modifier = Modifier.height(IntrinsicSize.Min),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
    ) {
        CountryPicker(
            modifier = Modifier
                .fillMaxHeight()
                .hiddenClickable { toggleCountryList(true) }
                .testTag(TestTags.countryCode),
            state = state
        )

        AppTextField(
            modifier = Modifier
                .testTag(TestTags.mobileNumber),
            state = state.ui.phoneNumber,
            hint = stringResource(R.string.enter_mobile_number),
            singleLine = true,
            design = DesignSystem.TextField.NormalTextField.copy(
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone)
            )
        )
    }
}

@Composable
private fun CountryPicker(modifier: Modifier = Modifier, state: StateHolder) {
    AppSurface(
        modifier = modifier,
        paddings = PaddingValues(DesignSystem.Padding.small),
        color = DesignSystem.TextField.NormalTextField.backgroundColor,
        shape = DesignSystem.TextField.NormalTextField.shape
    ) {
        AppRow(
            modifier = Modifier.fillMaxHeight(),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
            alignment = Alignment.CenterVertically
        ) {
            CountryImage(state.ui.country.value)

            AppIcon(vector = Icons.Default.KeyboardArrowDown)
        }
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember { StateHolder(data = mutableStateOf(ScreenData.empty)) }

    AppTheme { VerifyPhoneScreen(state, onEvent = {}, onCancelStatus = {}) }
}