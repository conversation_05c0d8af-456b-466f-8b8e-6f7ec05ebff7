package presentation.authentication.signup.feature

import androidx.compose.runtime.State
import kotlinx.coroutines.flow.map
import presentation.authentication.signup.domain.models.ScreenData
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>
)

class UIStateHolder(
    val firstName: StringState = StringState(""),
    val lastName: StringState = StringState(""),
    val username: StringState = StringState(""),
    val password: PasswordStateHolder = PasswordStateHolder(),
    val confirmPassword: PasswordStateHolder = PasswordStateHolder()
) {
    val passwordMinLength= DerivedState(password.text.get().length >= 8, password.text.stream.map { it.length >= 8 })
    val passwordContainsLetter = DerivedState(
        initial = password.text.get().any { it.isLetter() },
        flow = password.text.stream.map { it.any { it.isLetter() } }
    )
    val passwordContainsNumber = DerivedState(
        initial = password.text.get().any { it.isDigit() },
        flow = password.text.stream.map { it.any { it.isDigit() } }
    )

}

class PasswordStateHolder(
    val text: StringState = StringState(""),
    val hide: BooleanState = BooleanState(true)
)

