package presentation.authentication.signup.domain.models

internal sealed interface Event

internal sealed interface UserAction : Event {
    class Signup(
        val firstName: String,
        val lastName: String,
        val username: String,
        val password: String,
        val confirmPassword: String
    ) : UserAction
}

internal sealed interface ScreenEvent : Event {
    object NavToTerms : ScreenEvent
}
