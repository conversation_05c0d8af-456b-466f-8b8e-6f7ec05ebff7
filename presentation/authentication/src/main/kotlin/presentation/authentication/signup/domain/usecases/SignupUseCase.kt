package presentation.authentication.signup.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.SignupResponse
import keyless.presentation.common.R
import presentation.authentication.signup.domain.models.SideEffect
import presentation.authentication.signup.domain.models.UserAction
import presentation.authentication.signup.domain.models.toRequest
import presentation.common.domain.repositories.SideEffectsRepository

internal class SignupUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.Signup) = logger.async {
        if (!validate(event)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { signup(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun validate(event: UserAction.Signup): Boolean = logger.async {
        if (event.firstName.isBlank()) {
            status.info(Message(R.string.please_enter_first_name))
            return@async false
        }

        if (event.lastName.isBlank()) {
            status.info(Message(R.string.please_enter_last_name))
            return@async false
        }

        if (event.username.isBlank()) {
            status.info(Message(R.string.please_enter_username))
            return@async false
        }

        if (event.password.isBlank()) {
            status.info(Message(R.string.please_enter_password))
            return@async false
        }

        if (event.confirmPassword.isBlank()) {
            status.info(Message(R.string.please_enter_confirm_password))
            return@async false
        }

        if (event.password.length < 8) {
            status.info(Message(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
            return@async false
        }

        if (event.confirmPassword != event.password) {
            status.info(Message(R.string.password_not_match))
            return@async false
        }

        if (!isValidPassword(event.password)) {
            status.info(Message(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
            return@async false
        }

        return@async true
    }

    private fun isValidPassword(password: String): Boolean {
        val letterRegex = Regex("[a-zA-Z]")
        val numericRegex = Regex("\\d")

        if (password.length < 8) return false
        if (!password.contains(letterRegex)) return false
        if (!password.contains(numericRegex)) return false

        return true
    }

    private suspend fun signup(event: UserAction.Signup) = logger.async {
        val response = repository.signup(event.toRequest())
        if (!response.success) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun handleFailResponse(response: SignupResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: SignupResponse): Unit = logger.async {
        Preferences.userId.set(response.id)
        Preferences.userRole.set(response.userType)
        sideEffects.emit(SideEffect.NavToVerifyPhone)
    }
}
