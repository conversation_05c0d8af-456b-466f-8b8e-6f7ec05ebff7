package presentation.authentication.signup

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.signup.domain.ViewModel
import presentation.authentication.signup.domain.models.ScreenData
import presentation.authentication.signup.domain.models.SideEffect
import presentation.authentication.signup.domain.usecases.UseCases
import presentation.authentication.signup.feature.SignupAndroidViewModel
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal object SignupInjectionScope

val signupInjection = module {
    scope<SignupInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped { UseCases(authentication = get(), logger = get(), status = get(), sideEffects = get()) }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<SignupInjectionScope>(SignupInjectionScope.getScopeId())
        SignupAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
