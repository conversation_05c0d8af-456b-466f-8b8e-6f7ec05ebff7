package presentation.authentication.signup.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.SignupResponse
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get

internal object Setups: KoinTest {

    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupSignupSuccess(): SignupResponse {
        val url = "${ConfigValues.baseUrl}/user/username"
        val response = SignupResponse(
            success = true,
            message = "Signup successful",
            id = "12345",
            userType = "Guest",
            token = "sample-token"
        )
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupSignupFail(): SignupResponse {
        val url = "${ConfigValues.baseUrl}/user/username"
        val response = SignupResponse(
            success = false,
            message = "Username already exists"
        )
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }
}
