package presentation.authentication.signup.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import core.common.status.StatusRepository
import keyless.presentation.common.R
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.signup.domain.models.ScreenData
import presentation.authentication.signup.domain.models.SideEffect
import presentation.common.feature.components.WebActivity
import presentation.common.feature.components.safeNavigate
import presentation.common.feature.theme.AppTheme

class SignupFragment : Fragment() {

    private val viewModel: SignupAndroidViewModel by viewModel()
    private val status: StatusRepository by inject()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val screenData = viewModel.screenData.collectAsState(initial = ScreenData.empty)
        val state = remember(screenData) { viewModel.stateHolder(screenData) }

        SignupScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onBackClick = { requireActivity().onBackPressedDispatcher.onBackPressed() },
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToTerms -> {
            val intent = Intent(requireActivity(), WebActivity::class.java)
                .putExtra("url", "https://keyless.ae/terms-and-conditions/")
                .putExtra("title", getString(R.string.terms_of_use))

            requireActivity().startActivity(intent)
        }
        is SideEffect.NavToVerifyPhone -> {
            findNavController().safeNavigate(
                keyless.presentation.authentication.R.id.signupFragment,
                keyless.presentation.authentication.R.id.action_signup_to_verifyPhone
            )
        }
    }
}