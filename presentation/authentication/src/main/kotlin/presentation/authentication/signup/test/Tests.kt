package presentation.authentication.signup.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object TestTags {
    val tag = this::class.java.name
    val screen = tag + ".screen"
    val screenTitle = tag + ".screenTitle"
    val firstNameInput = tag + ".firstNameInput"
    val firstNameLabel = tag + ".firstNameLabel"
    val lastNameInput = tag + ".lastNameInput"
    val lastNameLabel = tag + ".lastNameLabel"
    val usernameInput = tag + ".usernameInput"
    val usernameLabel = tag + ".usernameLabel"
    val passwordInput = tag + ".passwordInput"
    val passwordLabel = tag + ".passwordLabel"
    val confirmPasswordInput = tag + ".confirmPasswordInput"
    val confirmPasswordLabel = tag + ".confirmPasswordLabel"
    val termsText = tag + ".termsText"
    val signupButton = tag + ".signupButton"
    val passwordRequirements = tag + ".passwordRequirements"
    val passwordMinLengthRequirement = tag + ".passwordMinLengthRequirement"
    val passwordLetterRequirement = tag + ".passwordLetterRequirement"
    val passwordNumberRequirement = tag + ".passwordNumberRequirement"
}

internal object SignupNodes {
    val ComposeTestRule.screen get() = tagged(TestTags.screen)
    val ComposeTestRule.screenTitle get() = tagged(TestTags.screenTitle)
    val ComposeTestRule.firstNameInput get() = tagged(TestTags.firstNameInput)
    val ComposeTestRule.firstNameLabel get() = tagged(TestTags.firstNameLabel)
    val ComposeTestRule.lastNameInput get() = tagged(TestTags.lastNameInput)
    val ComposeTestRule.lastNameLabel get() = tagged(TestTags.lastNameLabel)
    val ComposeTestRule.usernameInput get() = tagged(TestTags.usernameInput)
    val ComposeTestRule.usernameLabel get() = tagged(TestTags.usernameLabel)
    val ComposeTestRule.passwordInput get() = tagged(TestTags.passwordInput)
    val ComposeTestRule.passwordLabel get() = tagged(TestTags.passwordLabel)
    val ComposeTestRule.confirmPasswordInput get() = tagged(TestTags.confirmPasswordInput)
    val ComposeTestRule.confirmPasswordLabel get() = tagged(TestTags.confirmPasswordLabel)
    val ComposeTestRule.termsText get() = tagged(TestTags.termsText)
    val ComposeTestRule.signupButton get() = tagged(TestTags.signupButton)
    val ComposeTestRule.passwordRequirements get() = tagged(TestTags.passwordRequirements)
    val ComposeTestRule.passwordMinLengthRequirement get() = tagged(TestTags.passwordMinLengthRequirement)
    val ComposeTestRule.passwordLetterRequirement get() = tagged(TestTags.passwordLetterRequirement)
    val ComposeTestRule.passwordNumberRequirement get() = tagged(TestTags.passwordNumberRequirement)
}

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: SignupSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}
