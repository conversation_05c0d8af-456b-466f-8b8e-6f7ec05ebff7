package presentation.authentication.signup.test

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertTextEquals
import keyless.presentation.common.R
import presentation.authentication.signup.test.Setups.setupSignupFail
import presentation.authentication.signup.test.SignupNodes.confirmPasswordInput
import presentation.authentication.signup.test.SignupNodes.confirmPasswordLabel
import presentation.authentication.signup.test.SignupNodes.firstNameInput
import presentation.authentication.signup.test.SignupNodes.firstNameLabel
import presentation.authentication.signup.test.SignupNodes.lastNameInput
import presentation.authentication.signup.test.SignupNodes.lastNameLabel
import presentation.authentication.signup.test.SignupNodes.passwordInput
import presentation.authentication.signup.test.SignupNodes.passwordLabel
import presentation.authentication.signup.test.SignupNodes.passwordLetterRequirement
import presentation.authentication.signup.test.SignupNodes.passwordMinLengthRequirement
import presentation.authentication.signup.test.SignupNodes.passwordNumberRequirement
import presentation.authentication.signup.test.SignupNodes.passwordRequirements
import presentation.authentication.signup.test.SignupNodes.screenTitle
import presentation.authentication.signup.test.SignupNodes.signupButton
import presentation.authentication.signup.test.SignupNodes.termsText
import presentation.authentication.signup.test.SignupNodes.usernameInput
import presentation.authentication.signup.test.SignupNodes.usernameLabel
import presentation.common.feature.theme.lightColorScheme
import presentation.common.test.assertBackButtonClick
import presentation.common.test.assertBackButtonLayout
import presentation.common.test.assertDialogReturnButton
import presentation.common.test.assertToast
import presentation.common.test.assertWebActivity
import presentation.test.ComposeTestNode
import presentation.test.assertDrawableRes
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screen as verifyPhoneScreen

internal fun TestsSuite.assertSignupScreenLayout() {
    assertScreenTitleLayout()
    assertFirstNameInputLayout()
    assertLastNameInputLayout()
    assertUsernameInputLayout()
    assertPasswordInputLayout()
    assertConfirmPasswordInputLayout()
    assertTermsTextLayout()
    assertSignupButtonLayout()
    assertPasswordRequirementsLayout()
}
internal fun TestsSuite.assertBackButtonEnabled() {
    compose.assertBackButtonLayout(true)
    compose.assertBackButtonClick { if (!shared.isUnitTest) compose.screenTitle.assertDoesNotExist() }
}

internal fun TestsSuite.assertInputFieldValidations() {
    // Empty first name validation
    assertFirstNameInput("")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.please_enter_first_name))
    }
    clearToast()

    // Empty last name validation
    assertFirstNameInput("John")
    assertLastNameInput("")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.please_enter_last_name))
    }
    clearToast()

    // Empty username validation
    assertLastNameInput("Doe")
    assertUsernameInput("")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.please_enter_username))
    }
    clearToast()

    // Empty password validation
    assertUsernameInput("johndoe")
    assertPasswordInput("")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.please_enter_password))
    }
    clearToast()

    // Empty confirm password validation
    assertPasswordInput("Password123")
    assertConfirmPasswordInput("")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.please_enter_confirm_password))
    }
    clearToast()
}

internal fun TestsSuite.assertPasswordValidations() {
    // Password too short validation
    assertFirstNameInput("John")
    assertLastNameInput("Doe")
    assertUsernameInput("johndoe")
    assertPasswordInput("Pass1")
    assertConfirmPasswordInput("Pass1")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password without a letter validation
    assertPasswordInput("1231234123")
    assertConfirmPasswordInput("1231234123")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password without number validation
    assertPasswordInput("Passwordabc")
    assertConfirmPasswordInput("Passwordabc")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password mismatch validation
    assertPasswordInput("Password123")
    assertConfirmPasswordInput("Password456")
    compose.signupButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.password_not_match))
    }
    clearToast()
}

internal fun TestsSuite.assertSuccessfulSignup() {
    // Fill in all fields with valid data
    assertFirstNameInput("John")
    assertLastNameInput("Doe")
    assertUsernameInput("johndoe")
    assertPasswordInput("Password123")
    assertConfirmPasswordInput("Password123")

    // Click signup button
    compose.signupButton.displayedOrScroll().performClick {
        compose.verifyPhoneScreen.assertExists()
    }
}

internal fun TestsSuite.assertFailedSignup() {
    // Fill in all fields with valid data
    assertFirstNameInput("John")
    assertLastNameInput("Doe")
    assertUsernameInput("johndoe")
    assertPasswordInput("Password123")
    assertConfirmPasswordInput("Password123")

    // Click signup button
    val response = setupSignupFail()
    compose.signupButton.displayedOrScroll().performClick { compose.assertDialogReturnButton(response.message) }
}

internal fun TestsSuite.assertScreenTitleLayout() {
    val node = compose.screenTitle
    node.displayedOrScroll().assertTextEquals(activity.getString(R.string.create_guest))
}

internal fun TestsSuite.assertFirstNameInputLayout() {
    val node = compose.firstNameInput
    val label = compose.firstNameLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.firstname)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.firstname))
}

internal fun TestsSuite.assertFirstNameInput(input: String) {
    val node = compose.firstNameInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input)
}

internal fun TestsSuite.assertLastNameInputLayout() {
    val node = compose.lastNameInput
    val label = compose.lastNameLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.last_name)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.last_name))
}

internal fun TestsSuite.assertLastNameInput(input: String) {
    val node = compose.lastNameInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input)
}

internal fun TestsSuite.assertUsernameInputLayout() {
    val node = compose.usernameInput
    val label = compose.usernameLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.user_name)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.user_name))
}

internal fun TestsSuite.assertUsernameInput(input: String) {
    val node = compose.usernameInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input)
}

internal fun TestsSuite.assertPasswordInputLayout() {
    val node = compose.passwordInput
    val label = compose.passwordLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.password)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.password))
}

internal fun TestsSuite.assertPasswordInput(input: String) {
    val node = compose.passwordInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input, true)
}

internal fun TestsSuite.assertConfirmPasswordInputLayout() {
    val node = compose.confirmPasswordInput
    val label = compose.confirmPasswordLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.confirm_password)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.confirm_password))
}

internal fun TestsSuite.assertConfirmPasswordInput(input: String) {
    val node = compose.confirmPasswordInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input, true)
}

internal fun TestsSuite.assertTermsTextLayout() {
    compose.termsText.displayedOrScroll()
        .assertTextEquals(activity.getString(R.string.by_signing_up_you_agree_to_the_terms_and_conditions))
}

internal fun TestsSuite.assertTermsNavigation() {
    compose.termsText.displayedOrScroll().performClick  { shared.assertLaunchWebActivity() }
    compose.assertWebActivity()
}

internal fun TestsSuite.assertSignupButtonLayout() {
    val node = compose.signupButton
    val text = node.getChild { it.assertTextEquals(activity.getString(R.string.sign_up)) }

    node.displayedOrScroll().assertIsEnabled().assertBackgroundColor(lightColorScheme.secondary)
        .assertContentColor(lightColorScheme.onSecondary)
    text.displayedOrScroll().assertIsDisplayed()
}

internal fun TestsSuite.assertPasswordRequirementsLayout() {
    compose.passwordRequirements.displayedOrScroll()
    compose.passwordMinLengthRequirement.assertIsDisplayed()
    compose.passwordLetterRequirement.assertIsDisplayed()
    compose.passwordNumberRequirement.assertIsDisplayed()
}

internal fun TestsSuite.assertPasswordRequirementsValidation() {
    // Test empty password - all requirements should be unchecked
    assertPasswordInput("")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = false)

    // Test minimum length requirement
    assertPasswordInput("12345678")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)

    // Test letter requirement
    assertPasswordInput("a12345")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)

    // Test all requirements met
    assertPasswordInput("Password123")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)
}

private fun TestsSuite.assertPasswordRequirementIcon(node: ComposeTestNode, isChecked: Boolean) {
    node.displayedOrScroll()
    val expectedDrawableRes = if (isChecked) R.drawable.checkbox_on_background else R.drawable.checkbox_off_background
    val child = node.getChild { it.assertDrawableRes(expectedDrawableRes) }

    child.displayedOrScroll()
}

internal fun TestsSuite.clearToast() {
    advanceBackground(100000)
    compose.assertToast(null)
}
