package presentation.authentication.signup.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.login.loginInjection
import presentation.authentication.signup.signupInjection
import presentation.authentication.signup.test.Setups.setupSignupFail
import presentation.authentication.signup.test.Setups.setupSignupSuccess
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class SignupSharedTests : KoinTest {
    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    open val isUnitTest = false

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.SIGNUP } },
                loginInjection, signupInjection, verifyPhoneInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        scenario.close()
    }

    @Test
    fun assertSignupScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.assertSignupScreenLayout()
    }

    @Test
    fun assertBackButtonEnabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.assertBackButtonEnabled()
    }

    @Test
    fun assertInputFieldValidations() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.assertInputFieldValidations()
    }

    @Test
    fun assertPasswordValidations() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.setupSignupSuccess()
        suite.assertPasswordValidations()
    }

    @Test
    fun assertPasswordRequirementsValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.assertPasswordRequirementsValidation()
    }

    @Test
    fun assertSuccessfulSignup() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.setupSignupSuccess()
        suite.assertSuccessfulSignup()
    }

    @Test
    fun assertFailedSignup() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.setupSignupFail()
        suite.assertFailedSignup()
    }

    @Test
    fun assertTermsNavigation() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@SignupSharedTests, this)
        suite.assertTermsNavigation()
    }

    open fun assertLaunchWebActivity(): Unit = Unit
}
