package presentation.authentication.signup.domain.models

import data.keyless.authentication.models.SignUpRequest
import presentation.authentication.signup.feature.StateHolder
import presentation.common.domain.models.Device

internal fun UserAction.Signup.toRequest(): SignUpRequest {
    return SignUpRequest(
        firstName = firstName,
        lastName = lastName,
        username = username,
        password = password,
        confirmPassword = confirmPassword,
        deviceModel = Device.getDeviceNameApi(),
        deviceType = "android"
    )
}

internal fun StateHolder.toEvent(): UserAction.Signup {
    return UserAction.Signup(
        firstName = ui.firstName.get(),
        lastName = ui.lastName.get(),
        username = ui.username.get(),
        password = ui.password.text.get(),
        confirmPassword = ui.confirmPassword.text.get()
    )
}