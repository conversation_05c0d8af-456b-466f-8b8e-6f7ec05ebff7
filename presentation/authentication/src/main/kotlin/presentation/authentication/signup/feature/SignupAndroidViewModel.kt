package presentation.authentication.signup.feature

import androidx.compose.runtime.State
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import presentation.authentication.signup.domain.models.Event
import presentation.authentication.signup.domain.models.ScreenData
import presentation.authentication.signup.domain.models.SideEffect
import presentation.authentication.signup.domain.ViewModel as DomainViewModel

internal class SignupAndroidViewModel(
    private val domain: DomainViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON>rror<PERSON>and<PERSON>,
    private val onClear: () -> Unit
) : ViewModel() {

    private var stateHolder: StateHolder? = null

    val sideEffects: Flow<SideEffect> = domain.sideEffectsStream
    val screenData: Flow<ScreenData> = domain.screenDataStream

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    fun stateHolder(data: State<ScreenData>): StateHolder {
        if (stateHolder == null) stateHolder = StateHolder(data = data)
        if (data != stateHolder?.data) stateHolder = stateHolder!!.copy(data = data)

        return stateHolder!!
    }


    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}
