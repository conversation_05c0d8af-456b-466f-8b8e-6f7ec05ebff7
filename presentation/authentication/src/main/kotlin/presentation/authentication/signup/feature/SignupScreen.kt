package presentation.authentication.signup.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import keyless.presentation.common.R
import presentation.authentication.signup.domain.models.Event
import presentation.authentication.signup.domain.models.ScreenData
import presentation.authentication.signup.domain.models.ScreenEvent
import presentation.authentication.signup.domain.models.toEvent
import presentation.authentication.signup.test.TestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppPasswordField
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.StringState
import presentation.common.feature.theme.AppTheme

@Composable
internal fun SignupScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onCancelStatus: (Status) -> Unit,
    onBackClick: () -> Unit
) {
    AppLogoPage(
        isBackEnabled = true,
        status = state.data.value.status,
        onCancelStatus = onCancelStatus,
        onBackPress = onBackClick
    ) { ScreenLayout(state, onEvent) }
}

@Composable
private fun ScreenLayout(
    state: StateHolder,
    onEvent: (Event) -> Unit
) {
    AppScrollColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large)
    ) {
        item { AppPageTitleText(
            modifier = Modifier.testTag(TestTags.screenTitle),
            text = stringResource(R.string.create_guest)
        ) }
        item { SignupForm(modifier = Modifier.padding(top = DesignSystem.Padding.large), state) }
        item {
            AppLabelText(
                text = stringResource(R.string.by_signing_up_you_agree_to_the_terms_and_conditions),
                modifier = Modifier.hiddenClickable { onEvent(ScreenEvent.NavToTerms) }.testTag(TestTags.termsText),
                design = DesignSystem.Text.Label.copy(textDecoration = TextDecoration.Underline)
            )
        }
        item { ActionButton(state, onEvent) }
    }
}

@Composable
private fun SignupForm(modifier: Modifier = Modifier, state: StateHolder) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large)
    ) {
        FormInputField(state.ui.firstName, stringResource(R.string.firstname), onValueChange = { it })
        FormInputField(state.ui.lastName, stringResource(R.string.last_name), onValueChange = { it })
        FormInputField(state.ui.username, stringResource(R.string.user_name), onValueChange = { it.trim() })
        FormInputField(
            state.ui.password.text,
            stringResource(R.string.password),
            state.ui.password.hide,
            onValueChange = { it.trim() }
        )
        PasswordRequirements(state)
        FormInputField(
            state.ui.confirmPassword.text,
            stringResource(R.string.confirm_password),
            state.ui.confirmPassword.hide,
            onValueChange = { it.trim() }
        )
    }
}

@Composable
private fun ActionButton(state: StateHolder, onEvent: (Event) -> Unit) {
    AppButton(
        modifier = Modifier.testTag(TestTags.signupButton),
        text = stringResource(R.string.sign_up),
        isEnabled = true,
        onClick = { onEvent(state.toEvent()) }
    )
}

@Composable
private fun FormInputField(
    state: StringState,
    hint: String,
    hide: BooleanState? = null,
    onValueChange: (String) -> String
) {
    AppColumn(arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)) {
        AppBodyText(
            modifier = Modifier.testTag(when(hint) {
                stringResource(R.string.firstname) -> TestTags.firstNameLabel
                stringResource(R.string.last_name) -> TestTags.lastNameLabel
                stringResource(R.string.user_name) -> TestTags.usernameLabel
                stringResource(R.string.password) -> TestTags.passwordLabel
                stringResource(R.string.confirm_password) -> TestTags.confirmPasswordLabel
                else -> ""
            }),
            text = hint
        )

        if (hide != null) {
            AppPasswordField(
                modifier = Modifier.testTag(when(hint) {
                    stringResource(R.string.password) -> TestTags.passwordInput
                    stringResource(R.string.confirm_password) -> TestTags.confirmPasswordInput
                    else -> ""
                }),
                value = state.value,
                onValueChange = { state.update(it.trim()) },
                hint = hint,
                hide = hide,
                design = DesignSystem.TextField.NormalTextField.copy(
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
            )
        } else {
            AppTextField(
                modifier = Modifier.testTag(when(hint) {
                    stringResource(R.string.firstname) -> TestTags.firstNameInput
                    stringResource(R.string.last_name) -> TestTags.lastNameInput
                    stringResource(R.string.user_name) -> TestTags.usernameInput
                    else -> ""
                }),
                value = state.value,
                onValueChange = { state.update(onValueChange(it)) },
                hint = hint,
                singleLine = true,
                design = DesignSystem.TextField.NormalTextField.copy(
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
            )
        }
    }
}

@Composable
private fun PasswordRequirements(state: StateHolder) {
    AppColumn(
        modifier = Modifier.testTag(TestTags.passwordRequirements),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)
    ) {
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordMinLengthRequirement),
            text = stringResource(R.string.at_least_8_characters),
            isValid = state.ui.passwordMinLength.value
        )
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordLetterRequirement),
            text = stringResource(R.string.containing_a_letter),
            isValid = state.ui.passwordContainsLetter.value
        )
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordNumberRequirement),
            text = stringResource(R.string.a_number),
            isValid = state.ui.passwordContainsNumber.value
        )
    }
}

@Composable
private fun PasswordRequirementRow(modifier: Modifier = Modifier, text: String, isValid: Boolean) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterVertically
    ) {
        AppImage(res = if (isValid) R.drawable.checkbox_on_background else R.drawable.checkbox_off_background)
        AppLabelText(text = text)
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember { StateHolder(data = mutableStateOf(ScreenData.empty)) }
    AppTheme {
        SignupScreen(
            state = state,
            onEvent = {},
            onBackClick = {},
            onCancelStatus = {}
        )
    }
}