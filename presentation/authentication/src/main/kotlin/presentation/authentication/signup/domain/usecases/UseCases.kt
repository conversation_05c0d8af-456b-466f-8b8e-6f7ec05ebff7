package presentation.authentication.signup.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.keyless.authentication.AuthenticationRepository
import presentation.authentication.signup.domain.models.SideEffect
import presentation.common.domain.repositories.SideEffectsRepository

internal class UseCases(
    private val authentication: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    val signup = SignupUseCase(authentication, logger, status, sideEffects)
}
