package presentation.authentication.changepassword.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import core.common.status.StatusRepository
import keyless.data.keyless.ConfigValues
import keyless.presentation.common.R
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.changepassword.domain.models.ScreenData
import presentation.authentication.changepassword.domain.models.SideEffect
import presentation.common.feature.components.WebActivity
import presentation.common.feature.components.appToast
import presentation.common.feature.components.successDialogWithOkButton
import presentation.common.feature.theme.AppTheme

class ChangePasswordFragment : Fragment() {

    private val viewModel: ChangePasswordAndroidViewModel by viewModel()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToForgotPassword -> {
            val intent = Intent(requireContext(), WebActivity::class.java)
                .putExtra("url", "${ConfigValues.forgotPasswordUrl}/")
                .putExtra("title", getString(R.string.forgot_password))
            requireActivity().startActivity(intent)
            requireActivity().finish()
        }
        is SideEffect.PasswordChanged -> {
            requireActivity().finish()
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val screenData = viewModel.screenData.collectAsState(initial = ScreenData.empty)
        val state = viewModel.stateHolder(screenData)

        ChangePasswordScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }
}
