package presentation.authentication.changepassword.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import keyless.presentation.common.R
import presentation.authentication.changepassword.domain.models.Event
import presentation.authentication.changepassword.domain.models.ScreenData
import presentation.authentication.changepassword.domain.models.ScreenEvent
import presentation.authentication.changepassword.domain.models.UserAction
import presentation.authentication.changepassword.test.TestTags
import presentation.authentication.login.feature.CurrentPasswordView
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppPasswordField
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.theme.AppTheme

@Composable
internal fun ChangePasswordScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onCancelStatus: (Status) -> Unit
) {
    AppLogoPage(
        modifier = Modifier.testTag(TestTags.screen),
        isBackEnabled = false,
        status = state.data.value.status,
        onCancelStatus = onCancelStatus,
        onBackPress = {}
    ) {
        ScreenLayout(state, onEvent)
    }
}

@Composable
private fun ScreenLayout(
    state: StateHolder,
    onEvent: (Event) -> Unit
) {
    AppScrollColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large)
    ) {
        item { AppPageTitleText(
            modifier = Modifier.testTag(TestTags.screenTitle),
            text = stringResource(R.string.change_password)
        ) }

        item { PasswordForm(state, onEvent) }

        item { ActionButton(state, onEvent) }
    }
}

@Composable
private fun PasswordForm(state: StateHolder, onEvent: (Event) -> Unit) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large)
    ) {
        CurrentPasswordView(
            text = state.ui.currentPassword.text,
            hide = state.ui.currentPassword.hide,
            hint = stringResource(R.string.current_password),
            labelTag = TestTags.currentPasswordLabel,
            inputTag = TestTags.currentPasswordInput,
            forgotPasswordTag = TestTags.forgotPasswordButton,
            onForgotPassword = { onEvent(ScreenEvent.ForgotPassword) }
        )
        FormInputField(state.ui.newPassword, stringResource(R.string.new_password))

        PasswordRequirements(state)

        FormInputField(state.ui.confirmNewPassword, stringResource(R.string.confirm_new_password))
    }
}

@Composable
private fun ActionButton(state: StateHolder, onEvent: (Event) -> Unit) {
    AppButton(
        modifier = Modifier.testTag(TestTags.submitButton),
        text = stringResource(R.string.submit),
        isEnabled = true,
        onClick = {
            val event = UserAction.ChangePassword(
                oldPassword = state.ui.currentPassword.text.get(),
                newPassword = state.ui.newPassword.text.get(),
                confirmPassword = state.ui.confirmNewPassword.text.get()
            )
            onEvent(event)
        }
    )
}

@Composable
private fun FormInputField(
    state: PasswordState,
    hint: String
) {
    AppColumn(arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)) {
        AppBodyText(
            modifier = Modifier.testTag(when(hint) {
                stringResource(R.string.new_password) -> TestTags.newPasswordLabel
                stringResource(R.string.confirm_new_password) -> TestTags.confirmPasswordLabel
                else -> ""
            }),
            text = hint
        )

        AppPasswordField(
            modifier = Modifier.testTag(when(hint) {
                stringResource(R.string.new_password) -> TestTags.newPasswordInput
                stringResource(R.string.confirm_new_password) -> TestTags.confirmPasswordInput
                else -> ""
            }),
            value = state.text.value,
            onValueChange = { state.text.update(it.trim()) },
            hint = hint,
            hide = state.hide
        )
    }
}

@Composable
private fun PasswordRequirements(state: StateHolder) {
    AppColumn(
        modifier = Modifier.testTag(TestTags.passwordRequirements),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)
    ) {
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordMinLengthRequirement),
            text = stringResource(R.string.at_least_8_characters),
            isValid = state.ui.passwordMinLength.value
        )
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordLetterRequirement),
            text = stringResource(R.string.containing_a_letter),
            isValid = state.ui.passwordContainsLetter.value
        )
        PasswordRequirementRow(
            modifier = Modifier.testTag(TestTags.passwordNumberRequirement),
            text = stringResource(R.string.a_number),
            isValid = state.ui.passwordContainsNumber.value
        )
    }
}

@Composable
private fun PasswordRequirementRow(modifier: Modifier = Modifier, text: String, isValid: Boolean) {
    AppRow(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterVertically
    ) {
        AppImage(res = if (isValid) R.drawable.checkbox_on_background else R.drawable.checkbox_off_background)
        AppLabelText(text = text)
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember { StateHolder(data = androidx.compose.runtime.mutableStateOf(ScreenData.empty)) }
    AppTheme { ChangePasswordScreen(state, {}, {}) }
}
