package presentation.authentication.changepassword.feature

import androidx.compose.runtime.State
import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import presentation.authentication.changepassword.domain.ViewModel
import presentation.authentication.changepassword.domain.models.Event
import presentation.authentication.changepassword.domain.models.ScreenData

internal class ChangePasswordAndroidViewModel(
    private val domain: ViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON>rror<PERSON><PERSON><PERSON>,
    private val onClear: () -> Unit
) : androidx.lifecycle.ViewModel() {

    private var stateHolder: StateHolder? = null

    val screenData = domain.screenDataStream
    val sideEffects = domain.sideEffectsStream

    fun stateHolder(data: State<ScreenData>): StateHolder {
        if (stateHolder == null) stateHolder = StateHolder(data = data)
        if (data != stateHolder?.data) stateHolder = stateHolder!!.copy(data = data)

        return stateHolder!!
    }

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}
