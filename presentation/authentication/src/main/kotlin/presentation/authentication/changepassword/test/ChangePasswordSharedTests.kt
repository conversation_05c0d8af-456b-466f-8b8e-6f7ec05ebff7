package presentation.authentication.changepassword.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.changepassword.changePasswordInjection
import presentation.authentication.changepassword.test.Setups.setupChangePasswordFail
import presentation.authentication.changepassword.test.Setups.setupChangePasswordSuccess
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class ChangePasswordSharedTests : KoinTest {
    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    open val isUnitTest = false

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.UPDATE_PASSWORD } },
                changePasswordInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        scenario.close()
    }

    @Test
    fun assertChangePasswordScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertChangePasswordScreenLayout()
    }

    @Test
    fun assertBackButtonDisabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertBackButtonDisabled()
    }

    @Test
    fun assertInputFieldValidations() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertInputFieldValidations()
    }

    @Test
    fun assertPasswordValidations() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertPasswordValidations()
    }

    @Test
    fun assertPasswordRequirementsValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertPasswordRequirementsValidation()
    }

    @Test
    fun assertSuccessfulChangePassword() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.setupChangePasswordSuccess()
        suite.assertSuccessfulChangePassword()
    }

    @Test
    fun assertFailedChangePassword() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.setupChangePasswordFail()
        suite.assertFailedChangePassword()
    }

    @Test
    fun assertForgotPasswordNavigation() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@ChangePasswordSharedTests, this)
        suite.assertForgotPasswordNavigation()
    }

    internal open fun assertLaunchWebActivity() = Unit
}
