package presentation.authentication.changepassword.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.ChangePasswordResponse
import keyless.presentation.common.R
import presentation.authentication.changepassword.domain.models.SideEffect
import presentation.authentication.changepassword.domain.models.UserAction
import presentation.authentication.changepassword.domain.models.toRequest
import presentation.common.domain.repositories.SideEffectsRepository

internal class ChangePasswordUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.ChangePassword) = logger.async {
        if (!validate(event)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { changePassword(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun validate(event: UserAction.ChangePassword): Boolean = logger.async {
        if (event.oldPassword.isBlank()) {
            status.info(Message(R.string.please_enter_current_password))
            return@async false
        }

        if (event.newPassword.isBlank()) {
            status.info(Message(R.string.please_enter_new_password))
            return@async false
        }

        if (event.confirmPassword.isBlank()) {
            status.info(Message(R.string.please_enter_confirm_password))
            return@async false
        }

        if (event.newPassword == event.oldPassword) {
            status.info(Message(R.string.the_current_password_and_the_new_password_cannot_be_the_same))
            return@async false
        }

        if (!isValidPassword(event.newPassword)) {
            status.info(Message(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
            return@async false
        }

        if (event.newPassword != event.confirmPassword) {
            status.info(Message(R.string.password_not_match))
            return@async false
        }

        return@async true
    }

    private suspend fun changePassword(event: UserAction.ChangePassword) = logger.async {
        val token = Preferences.authenticationToken.get()
        val response = repository.changePassword(event.toRequest(), token)

        if (!response.success) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun handleFailResponse(response: ChangePasswordResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: ChangePasswordResponse) = logger.async {
        status.success(Message.fromString(response.message))
        sideEffects.emit(SideEffect.PasswordChanged)
    }

    private fun isValidPassword(password: String): Boolean {
        val letterRegex = Regex("[a-zA-Z]")
        val numericRegex = Regex("\\d")

        if (password.length < 8) return false
        if (!password.contains(letterRegex)) return false
        if (!password.contains(numericRegex)) return false

        return true
    }
}
