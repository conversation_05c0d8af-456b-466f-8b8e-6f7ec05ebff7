package presentation.authentication.changepassword

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.changepassword.domain.ViewModel
import presentation.authentication.changepassword.domain.models.ScreenData
import presentation.authentication.changepassword.domain.models.SideEffect
import presentation.authentication.changepassword.domain.usecases.UseCases
import presentation.authentication.changepassword.feature.ChangePasswordAndroidViewModel
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object ChangePasswordInjectionScope

val changePasswordInjection = module {
    scope<ChangePasswordInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped { UseCases(authentication = get(), logger = get(), status = get(), sideEffects = get()) }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<ChangePasswordInjectionScope>(ChangePasswordInjectionScope.getScopeId())
        ChangePasswordAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
