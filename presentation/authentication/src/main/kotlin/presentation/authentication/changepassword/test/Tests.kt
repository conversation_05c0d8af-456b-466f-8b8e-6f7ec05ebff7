package presentation.authentication.changepassword.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object TestTags {
    val tag = this::class.java.name
    val screen = tag + ".screen"
    val screenTitle = tag + ".screenTitle"
    val currentPasswordInput = tag + ".currentPasswordInput"
    val currentPasswordLabel = tag + ".currentPasswordLabel"
    val newPasswordInput = tag + ".newPasswordInput"
    val newPasswordLabel = tag + ".newPasswordLabel"
    val confirmPasswordInput = tag + ".confirmPasswordInput"
    val confirmPasswordLabel = tag + ".confirmPasswordLabel"
    val submitButton = tag + ".submitButton"
    val forgotPasswordButton = tag + ".forgotPasswordButton"
    val passwordRequirements = tag + ".passwordRequirements"
    val passwordMinLengthRequirement = tag + ".passwordMinLengthRequirement"
    val passwordLetterRequirement = tag + ".passwordLetterRequirement"
    val passwordNumberRequirement = tag + ".passwordNumberRequirement"
}

internal object ChangePasswordNodes {
    val ComposeTestRule.screen get() = tagged(TestTags.screen)
    val ComposeTestRule.screenTitle get() = tagged(TestTags.screenTitle)
    val ComposeTestRule.currentPasswordInput get() = tagged(TestTags.currentPasswordInput)
    val ComposeTestRule.currentPasswordLabel get() = tagged(TestTags.currentPasswordLabel)
    val ComposeTestRule.newPasswordInput get() = tagged(TestTags.newPasswordInput)
    val ComposeTestRule.newPasswordLabel get() = tagged(TestTags.newPasswordLabel)
    val ComposeTestRule.confirmPasswordInput get() = tagged(TestTags.confirmPasswordInput)
    val ComposeTestRule.confirmPasswordLabel get() = tagged(TestTags.confirmPasswordLabel)
    val ComposeTestRule.submitButton get() = tagged(TestTags.submitButton)
    val ComposeTestRule.forgotPasswordButton get() = tagged(TestTags.forgotPasswordButton)
    val ComposeTestRule.passwordRequirements get() = tagged(TestTags.passwordRequirements)
    val ComposeTestRule.passwordMinLengthRequirement get() = tagged(TestTags.passwordMinLengthRequirement)
    val ComposeTestRule.passwordLetterRequirement get() = tagged(TestTags.passwordLetterRequirement)
    val ComposeTestRule.passwordNumberRequirement get() = tagged(TestTags.passwordNumberRequirement)
}

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: ChangePasswordSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}
