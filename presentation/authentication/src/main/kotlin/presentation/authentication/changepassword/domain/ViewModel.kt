package presentation.authentication.changepassword.domain

import presentation.authentication.changepassword.domain.models.Event
import presentation.authentication.changepassword.domain.models.ScreenData
import presentation.authentication.changepassword.domain.models.ScreenEvent
import presentation.authentication.changepassword.domain.models.SideEffect
import presentation.authentication.changepassword.domain.models.UserAction
import presentation.authentication.changepassword.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is UserAction -> onUserAction(event)
        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onUserAction(event: UserAction) = when (event) {
        is UserAction.ChangePassword -> useCases.changePassword.execute(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.ForgotPassword -> sideEffects.emit(SideEffect.NavToForgotPassword)
    }
}
