package presentation.authentication.changepassword.domain.models

import data.common.preferences.Preferences
import data.keyless.authentication.models.ChangePasswordRequest

internal fun UserAction.ChangePassword.toRequest() = ChangePasswordRequest(
    oldPassword = oldPassword,
    password = newPassword,
    confirmPassword = confirmPassword,
    userId = Preferences.userId.get(),
    isAdmin = Preferences.isAdminLogin()
)
