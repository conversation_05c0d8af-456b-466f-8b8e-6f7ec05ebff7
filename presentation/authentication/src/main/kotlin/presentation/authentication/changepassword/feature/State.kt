package presentation.authentication.changepassword.feature

import androidx.compose.runtime.State
import kotlinx.coroutines.flow.map
import presentation.authentication.changepassword.domain.models.ScreenData
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>
)

internal class UIStateHolder(
    val currentPassword: PasswordState = PasswordState(),
    val newPassword: PasswordState = PasswordState(),
    val confirmNewPassword: PasswordState = PasswordState()
) {
    val passwordMinLength= DerivedState(newPassword.text.get().length >= 8, newPassword.text.stream.map { it.length >= 8 })
    val passwordContainsLetter = DerivedState(
        initial = newPassword.text.get().any { it.isLetter() },
        flow = newPassword.text.stream.map { it.any { it.isLetter() } }
    )
    val passwordContainsNumber = DerivedState(
        initial = newPassword.text.get().any { it.isDigit() },
        flow = newPassword.text.stream.map { it.any { it.isDigit() } }
    )

}

internal data class PasswordState(
    val text: StringState = StringState(""),
    val hide: BooleanState = BooleanState(true)
)