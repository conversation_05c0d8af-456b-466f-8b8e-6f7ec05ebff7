package presentation.authentication.changepassword.test

import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.ChangePasswordResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get

internal object Setups: KoinTest {

    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupChangePasswordSuccess(): ChangePasswordResponse {
        val url = "${ConfigValues.baseUrl}/user/change_password"
        val response = MockResponses.postChangePassword
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
        return response.value()
    }

    fun TestsSuite.setupChangePasswordFail(): ChangePasswordResponse {
        val url = "${ConfigValues.baseUrl}/user/change_password"
        val response = MockResponses.postChangePasswordFail
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
        return response.value()
    }
}
