package presentation.authentication.changepassword.test

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertTextEquals
import keyless.presentation.common.R
import presentation.authentication.changepassword.test.ChangePasswordNodes.confirmPasswordInput
import presentation.authentication.changepassword.test.ChangePasswordNodes.confirmPasswordLabel
import presentation.authentication.changepassword.test.ChangePasswordNodes.currentPasswordInput
import presentation.authentication.changepassword.test.ChangePasswordNodes.currentPasswordLabel
import presentation.authentication.changepassword.test.ChangePasswordNodes.forgotPasswordButton
import presentation.authentication.changepassword.test.ChangePasswordNodes.newPasswordInput
import presentation.authentication.changepassword.test.ChangePasswordNodes.newPasswordLabel
import presentation.authentication.changepassword.test.ChangePasswordNodes.passwordLetterRequirement
import presentation.authentication.changepassword.test.ChangePasswordNodes.passwordMinLengthRequirement
import presentation.authentication.changepassword.test.ChangePasswordNodes.passwordNumberRequirement
import presentation.authentication.changepassword.test.ChangePasswordNodes.passwordRequirements
import presentation.authentication.changepassword.test.ChangePasswordNodes.screen
import presentation.authentication.changepassword.test.ChangePasswordNodes.screenTitle
import presentation.authentication.changepassword.test.ChangePasswordNodes.submitButton
import presentation.authentication.changepassword.test.Setups.setupChangePasswordFail
import presentation.authentication.changepassword.test.Setups.setupChangePasswordSuccess
import presentation.common.feature.theme.lightColorScheme
import presentation.common.test.assertBackButtonLayout
import presentation.common.test.assertDialogReturnButton
import presentation.common.test.assertToast
import presentation.common.test.assertWebActivity
import presentation.test.ComposeTestNode
import presentation.test.assertDrawableRes

internal fun TestsSuite.assertChangePasswordScreenLayout() {
    assertScreenTitleLayout()
    assertCurrentPasswordInputLayout()
    assertNewPasswordInputLayout()
    assertConfirmPasswordInputLayout()
    assertSubmitButtonLayout()
    assertPasswordRequirementsLayout()
    assertForgotPasswordButtonLayout()
}

internal fun TestsSuite.assertBackButtonDisabled() {
    compose.assertBackButtonLayout(false)
}

internal fun TestsSuite.assertInputFieldValidations() {
    // Empty current password validation
    assertCurrentPasswordInput("")
    compose.submitButton.displayedOrScroll()
        .performClick { compose.assertToast(activity.getString(R.string.please_enter_current_password)) }
    clearToast()

    // Empty new password validation
    assertCurrentPasswordInput("OldPassword123")
    assertNewPasswordInput("")
    compose.submitButton.displayedOrScroll()
        .performClick { compose.assertToast(activity.getString(R.string.please_enter_new_password)) }
    clearToast()

    // Empty confirm password validation
    assertNewPasswordInput("NewPassword123")
    assertConfirmPasswordInput("")
    compose.submitButton.displayedOrScroll()
        .performClick { compose.assertToast(activity.getString(R.string.please_enter_confirm_password)) }
    clearToast()
}

internal fun TestsSuite.assertPasswordValidations() {
    // Same password validation
    assertCurrentPasswordInput("Password123")
    assertNewPasswordInput("Password123")
    assertConfirmPasswordInput("Password123")
    compose.submitButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_current_password_and_the_new_password_cannot_be_the_same))
    }
    clearToast()

    // Password too short validation
    assertCurrentPasswordInput("OldPassword123")
    assertNewPasswordInput("Pass1")
    assertConfirmPasswordInput("Pass1")
    compose.submitButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password without letter validation
    assertNewPasswordInput("12345678")
    assertConfirmPasswordInput("12345678")
    compose.submitButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password without number validation
    assertNewPasswordInput("Password")
    assertConfirmPasswordInput("Password")
    compose.submitButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.the_password_must_be_a_minimum_of_8_characters_with_1_lowercase_and_1_numeric_character))
    }
    clearToast()

    // Password mismatch validation
    assertNewPasswordInput("Password123")
    assertConfirmPasswordInput("Password456")
    compose.submitButton.displayedOrScroll().performClick {
        compose.assertToast(activity.getString(R.string.password_not_match))
    }
    clearToast()
}

internal fun TestsSuite.assertPasswordRequirementsValidation() {
    // Test empty password - all requirements should be unchecked
    assertNewPasswordInput("")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = false)

    // Test minimum length requirement
    assertNewPasswordInput("12345678")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)

    // Test letter requirement
    assertNewPasswordInput("a12345")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = false)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)

    // Test all requirements met
    assertNewPasswordInput("Password123")
    assertPasswordRequirementIcon(compose.passwordMinLengthRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordLetterRequirement, isChecked = true)
    assertPasswordRequirementIcon(compose.passwordNumberRequirement, isChecked = true)
}

internal fun TestsSuite.assertSuccessfulChangePassword() {
    // Fill in all fields with valid data
    assertCurrentPasswordInput("OldPassword123")
    assertNewPasswordInput("NewPassword123")
    assertConfirmPasswordInput("NewPassword123")

    // Click submit button
    val response = setupChangePasswordSuccess()
    compose.submitButton.displayedOrScroll().performClick {
        if (!shared.isUnitTest) compose.screen.assertDoesNotExist()
        else compose.assertToast(response.message)
    }
}

internal fun TestsSuite.assertFailedChangePassword() {
    // Fill in all fields with valid data
    assertCurrentPasswordInput("WrongPassword123")
    assertNewPasswordInput("NewPassword123")
    assertConfirmPasswordInput("NewPassword123")

    // Click submit button
    val response = setupChangePasswordFail()
    compose.submitButton.displayedOrScroll().performClick { compose.assertDialogReturnButton(response.message) }
    clearToast()
}

internal fun TestsSuite.assertForgotPasswordNavigation() {
    compose.forgotPasswordButton.displayedOrScroll().performClick { shared.assertLaunchWebActivity() }
    compose.assertWebActivity()
}

internal fun TestsSuite.assertScreenTitleLayout() {
    val node = compose.screenTitle
    node.displayedOrScroll().assertTextEquals(activity.getString(R.string.change_password))
}

internal fun TestsSuite.assertCurrentPasswordInputLayout() {
    val node = compose.currentPasswordInput
    val label = compose.currentPasswordLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.current_password)) }
    label.displayedOrScroll().assertTextEquals(activity.getString(R.string.current_password))
}

internal fun TestsSuite.assertCurrentPasswordInput(input: String) {
    val node = compose.currentPasswordInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input, true)
}

internal fun TestsSuite.assertNewPasswordInputLayout() {
    val node = compose.newPasswordInput
    val label = compose.newPasswordLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.new_password)) }
    label.displayedOrScroll().assertTextEquals(activity.getString(R.string.new_password))
}

internal fun TestsSuite.assertNewPasswordInput(input: String) {
    val node = compose.newPasswordInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input, true)
}

internal fun TestsSuite.assertConfirmPasswordInputLayout() {
    val node = compose.confirmPasswordInput
    val label = compose.confirmPasswordLabel

    node.displayedOrScroll().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.confirm_new_password)) }
    label.displayedOrScroll().assertTextEquals(activity.getString(R.string.confirm_new_password))
}

internal fun TestsSuite.assertConfirmPasswordInput(input: String) {
    val node = compose.confirmPasswordInput
    node.displayedOrScroll().assertIsEnabled().performTextClearance().performTextInput(input, true)
}

internal fun TestsSuite.assertForgotPasswordButtonLayout() {
    compose.forgotPasswordButton.displayedOrScroll().assertTextEquals(activity.getString(R.string.forgot_password_q))
}

internal fun TestsSuite.assertSubmitButtonLayout() {
    val node = compose.submitButton
    val text = node.getChild { it.assertTextEquals(activity.getString(R.string.submit)) }

    node.displayedOrScroll().assertIsEnabled().assertBackgroundColor(lightColorScheme.secondary)
        .assertContentColor(lightColorScheme.onSecondary)
    text.assertIsDisplayed()
}

internal fun TestsSuite.assertPasswordRequirementsLayout() {
    compose.passwordRequirements.displayedOrScroll()
    compose.passwordMinLengthRequirement.displayedOrScroll()
    compose.passwordLetterRequirement.displayedOrScroll()
    compose.passwordNumberRequirement.displayedOrScroll()
}

private fun TestsSuite.assertPasswordRequirementIcon(node: ComposeTestNode, isChecked: Boolean) {
    node.displayedOrScroll()
    val expectedDrawableRes = if (isChecked) R.drawable.checkbox_on_background else R.drawable.checkbox_off_background
    val child = node.getChild { it.assertDrawableRes(expectedDrawableRes) }

    child.displayedOrScroll()
}

internal fun TestsSuite.clearToast() {
    advanceBackground(100000)
    compose.assertToast(null)
}
