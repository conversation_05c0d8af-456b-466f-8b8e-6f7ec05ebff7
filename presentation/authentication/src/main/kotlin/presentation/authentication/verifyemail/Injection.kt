package presentation.authentication.verifyemail

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.verifyemail.domain.ViewModel
import presentation.authentication.verifyemail.domain.models.ScreenData
import presentation.authentication.verifyemail.domain.models.SideEffect
import presentation.authentication.verifyemail.domain.usecases.UseCases
import presentation.authentication.verifyemail.feature.VerifyEmailAndroidViewModel
import presentation.authentication.verifyphone.VerifyPhoneInjectionScope
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object VerifyEmailInjectionScope

val verifyEmailInjection = module {
    scope<VerifyEmailInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped {
            val scope = getKoin().getOrCreateScope<VerifyPhoneInjectionScope>(VerifyPhoneInjectionScope.getScopeId())
            val screenData = scope
                .get<ScreenDataRepository<presentation.authentication.verifyphone.domain.models.ScreenData>>()

            UseCases(
                authentication = get(),
                phoneScreenData = screenData,
                logger = get(),
                status = get(),
                dispatchers = get(),
                sideEffects = get()
            )
        }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<VerifyEmailInjectionScope>(VerifyEmailInjectionScope.getScopeId())
        VerifyEmailAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
