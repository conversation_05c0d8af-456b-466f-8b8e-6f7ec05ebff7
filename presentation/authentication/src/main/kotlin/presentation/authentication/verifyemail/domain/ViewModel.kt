package presentation.authentication.verifyemail.domain

import presentation.authentication.verifyemail.domain.models.Event
import presentation.authentication.verifyemail.domain.models.ScreenData
import presentation.authentication.verifyemail.domain.models.SideEffect
import presentation.authentication.verifyemail.domain.models.UserAction
import presentation.authentication.verifyemail.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is UserAction -> onUserAction(event)
    }

    private suspend fun onUserAction(event: UserAction) = when (event) {
        is UserAction.SendEmailOtp -> useCases.sendEmailOtp.execute(event)
    }
}
