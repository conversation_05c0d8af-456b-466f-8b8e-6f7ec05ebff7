package presentation.authentication.verifyemail.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.common.coroutines.AbstractCoroutineDispatcher
import data.keyless.authentication.AuthenticationRepository
import presentation.authentication.verifyemail.domain.models.SideEffect
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class UseCases(
    private val authentication: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val dispatchers: AbstractC<PERSON>utineDispatcher,
    private val phoneScreenData: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    val sendEmailOtp = SendEmailOtpUseCase(authentication, logger, status, sideEffects, phoneScreenData, dispatchers)
}
