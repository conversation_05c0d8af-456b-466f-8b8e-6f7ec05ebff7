package presentation.authentication.verifyemail.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object TestTags {
    val tag = this::class.java.name
    val screen = tag + ".screen"
    val screenTitle = tag + ".screenTitle"
    val screenDescription = tag + ".screenDescription"
    val emailInput = tag + ".emailInput"
    val emailLabel = tag + ".emailLabel"
    val verifyButton = tag + ".verifyButton"
    val emailImage = tag + ".emailImage"
}

internal object VerifyEmailNodes {
    val ComposeTestRule.screen get() = tagged(TestTags.screen)
    val ComposeTestRule.screenTitle get() = tagged(TestTags.screenTitle)
    val ComposeTestRule.screenDescription get() = tagged(TestTags.screenDescription)
    val ComposeTestRule.emailInput get() = tagged(TestTags.emailInput)
    val ComposeTestRule.emailLabel get() = tagged(TestTags.emailLabel)
    val ComposeTestRule.verifyButton get() = tagged(TestTags.verifyButton)
    val ComposeTestRule.emailImage get() = tagged(TestTags.emailImage)
}

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: VerifyEmailSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}