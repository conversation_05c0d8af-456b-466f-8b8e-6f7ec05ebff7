package presentation.authentication.verifyemail.feature

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import core.common.status.StatusRepository
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.verifyemail.domain.models.ScreenData
import presentation.authentication.verifyemail.domain.models.SideEffect
import presentation.authentication.verifyotp.feature.VerifyOtpFragment
import presentation.common.feature.components.appToast
import presentation.common.feature.components.safeNavigate
import presentation.common.feature.components.successDialogWithOkButton
import presentation.common.feature.theme.AppTheme

class VerifyEmailFragment : Fragment() {

    private val viewModel: VerifyEmailAndroidViewModel by viewModel()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }

        requireActivity().onBackPressedDispatcher.addCallback(this) { }
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToVerifyOtp -> {
            val bundle = Bundle().apply {
                putString(VerifyOtpFragment.emailKey, viewModel.ui.email.get())
            }
            findNavController().safeNavigate(
                keyless.presentation.authentication.R.id.verifyEmailFragment,
                keyless.presentation.authentication.R.id.action_verifyEmail_to_verifyOtp,
                bundle
            )
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val screenData = viewModel.screenData.collectAsState(ScreenData.empty)
        val state = remember(screenData) { viewModel.stateHolder(screenData) }

        VerifyEmailScreen(
            state,
            onEvent = viewModel::onEvent,
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }
}
