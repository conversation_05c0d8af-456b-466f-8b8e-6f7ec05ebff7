package presentation.authentication.verifyemail.test

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.assertTextEquals
import keyless.presentation.common.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import presentation.authentication.verifyemail.test.Setups.setupSendEmailOtpFail
import presentation.authentication.verifyemail.test.Setups.setupSendEmailOtpSuccess
import presentation.authentication.verifyemail.test.VerifyEmailNodes.emailImage
import presentation.authentication.verifyemail.test.VerifyEmailNodes.emailInput
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screenDescription
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screenTitle
import presentation.authentication.verifyemail.test.VerifyEmailNodes.verifyButton
import presentation.authentication.verifyotp.test.VerifyOtpNodes.screen
import presentation.common.feature.theme.lightColorScheme
import presentation.common.test.assertBackButtonLayout
import presentation.common.test.assertToast
import presentation.test.wait

internal fun TestsSuite.assertVerifyEmailScreenLayout() {
    assertScreenTitleLayout()
    assertScreenDescriptionLayout()
    assertEmailImageLayout()
    assertEmailInputLayout()
    assertVerifyButtonLayout()
}

internal fun TestsSuite.assertScreenBackDisabled() {
    compose.assertBackButtonLayout(false)

    (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed()
    assertScreenTitleLayout()
}

internal fun TestsSuite.assertEmailInputValidation() {
    // Empty email validation
    assertEmailInput("")
    compose.verifyButton.performClick {
        compose.assertToast(activity.getString(R.string.please_enter_valid_email))
    }
    clearToast()

    // Invalid email format validation
    assertEmailInput("invalid-email")
    compose.verifyButton.performClick {
        compose.assertToast(activity.getString(R.string.please_enter_valid_email))
    }
    clearToast()

    // Valid email format validation
    assertEmailInput("<EMAIL>")
    setupSendEmailOtpSuccess()

    compose.verifyButton.performClick {
        compose.wait { compose.screen.assertExists() }
    }
    testScope.launch {
        val context = if (shared.isUnitTest) currentCoroutineContext() else Dispatchers.Main
        withContext(context) { (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed() }
    }
}

internal fun TestsSuite.assertFailedEmailValidation() {
    assertEmailInput("<EMAIL>")
    assertVerifyButtonLayout()

    val response = setupSendEmailOtpFail()
    compose.verifyButton.performClick {
        compose.wait { compose.assertToast(response.message) }
    }
    clearToast()
}

internal fun TestsSuite.assertScreenTitleLayout() {
    val node = compose.screenTitle
    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.verify_your_email))
}

internal fun TestsSuite.assertScreenDescriptionLayout() {
    val node = compose.screenDescription
    node.assertIsDisplayed()
        .assertTextEquals(activity.getString(R.string.enter_the_email_which_you_want_to_associate_with_your_accountand_we_ll_send_an_email_with_verification_link))
}

internal fun TestsSuite.assertEmailImageLayout() {
    val node = compose.emailImage
    node.assertIsDisplayed()
}

internal fun TestsSuite.assertEmailInputLayout() {
    val node = compose.emailInput
    node.assertIsDisplayed().assertIsEnabled()
}

internal fun TestsSuite.assertEmailInput(input: String) {
    val node = compose.emailInput
    node.assertIsDisplayed().assertIsEnabled().performTextClearance().performTextInput(input).assertTextEquals(input)
}

internal fun TestsSuite.assertVerifyButtonLayout() {
    val node = compose.verifyButton
    val text = node.getChild { it.assertTextEquals(activity.getString(R.string.verify_email)) }

    node.assertIsDisplayed().assertIsEnabled().assertBackgroundColor(lightColorScheme.secondary)
        .assertContentColor(lightColorScheme.onSecondary)
    text.assertIsDisplayed()
}

internal fun TestsSuite.clearToast() {
    advanceBackground(100000)
    compose.assertToast(null)
}
