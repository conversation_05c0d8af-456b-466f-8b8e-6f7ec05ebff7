package presentation.authentication.verifyemail.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import keyless.presentation.common.R
import presentation.authentication.verifyemail.domain.models.Event
import presentation.authentication.verifyemail.domain.models.ScreenData
import presentation.authentication.verifyemail.domain.models.UserAction
import presentation.authentication.verifyemail.test.TestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.verticalScrollAdaptToIme
import presentation.common.feature.theme.AppTheme

@Composable
internal fun VerifyEmailScreen(
    state: StateHolder,
    onCancelStatus: (Status) -> Unit,
    onEvent: (Event) -> Unit
) {
    AppLogoPage(
        modifier = Modifier.testTag(TestTags.screen),
        isBackEnabled = false,
        status = state.data.value.status,
        onCancelStatus = onCancelStatus,
        onBackPress = {}
    ) {
        ScreenLayout(
            state,
            onEvent = onEvent
        )
    }
}

@Composable
private fun ScreenLayout(state: StateHolder, onEvent: (Event) -> Unit) {
    AppColumn(
        modifier = Modifier
            .fillMaxHeight()
            .verticalScrollAdaptToIme(),
        arrangement = Arrangement.SpaceBetween
    ) {
        EmailSection(state = state)

        SendActionSection(state, onEvent)
    }
}

@Composable
private fun EmailSection(
    state: StateHolder
) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        AppPageTitleText(
            modifier = Modifier.testTag(TestTags.screenTitle),
            text = stringResource(R.string.verify_your_email)
        )

        AppBodyText(
            modifier = Modifier.testTag(TestTags.screenDescription),
            text = stringResource(R.string.enter_the_email_which_you_want_to_associate_with_your_accountand_we_ll_send_an_email_with_verification_link)
        )

        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            AppImage(
                modifier = Modifier.testTag(TestTags.emailImage),
                res = R.drawable.iv_verify_email
            )
        }

        EmailTextField(state)
    }
}

@Composable
private fun SendActionSection(state: StateHolder, onEvent: (Event) -> Unit) {
    AppButton(
        modifier = Modifier.testTag(TestTags.verifyButton),
        text = stringResource(R.string.verify_email),
        isEnabled = true,
        onClick = {
            onEvent(UserAction.SendEmailOtp(email = state.ui.email.get()))
        }
    )
}

@Composable
private fun EmailTextField(state: StateHolder) {
    AppTextField(
        modifier = Modifier.testTag(TestTags.emailInput),
        state = state.ui.email,
        hint = stringResource(R.string.enter_email),
        singleLine = true,
        design = DesignSystem.TextField.NormalTextField.copy(
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email)
        )
    )

}

@Preview
@Composable
private fun Preview() {
    val state = remember { StateHolder(data = mutableStateOf(ScreenData.empty)) }

    AppTheme { VerifyEmailScreen(state, onEvent = {}, onCancelStatus = {}) }
}