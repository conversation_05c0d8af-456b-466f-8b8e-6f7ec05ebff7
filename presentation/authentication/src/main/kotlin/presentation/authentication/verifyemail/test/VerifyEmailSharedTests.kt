package presentation.authentication.verifyemail.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.login.loginInjection
import presentation.authentication.verifyemail.test.Setups.setupGuestLoginEmail
import presentation.authentication.verifyemail.test.Setups.setupSendEmailOtpFail
import presentation.authentication.verifyemail.test.Setups.setupSendEmailOtpSuccess
import presentation.authentication.verifyemail.verifyEmailInjection
import presentation.authentication.verifyotp.verifyOtpInjection
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class VerifyEmailSharedTests : KoinTest {
    internal abstract val isUnitTest: Boolean

    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.LOGIN } },
                loginInjection, verifyPhoneInjection, verifyEmailInjection, verifyOtpInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        scenario.close()
        Setups.clear()
    }

    @Test
    fun assertVerifyEmailScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyEmailSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertVerifyEmailScreenLayout()
    }

    @Test
    fun assertScreenBackDisabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyEmailSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertScreenBackDisabled()
    }

    @Test
    fun assertEmailInputValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyEmailSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.setupSendEmailOtpSuccess()
        suite.assertEmailInputValidation()
    }

    @Test
    fun assertFailedEmailValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyEmailSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.setupSendEmailOtpFail()
        suite.assertFailedEmailValidation()
    }
}
