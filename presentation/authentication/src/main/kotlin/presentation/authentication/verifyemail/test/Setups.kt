package presentation.authentication.verifyemail.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.SendOtpResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.login.test.LoginNodes.emailInput
import presentation.authentication.login.test.LoginNodes.loginButton
import presentation.authentication.login.test.LoginNodes.passwordInput
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screenTitle

internal object Setups : KoinTest {

    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupGuestLoginEmail() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 1, userType = "Guest")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)

        compose.emailInput.performTextInput("username")
        compose.passwordInput.performTextInput("password", true)
        compose.loginButton.performClick { compose.screenTitle.assertExists() }
    }

    fun TestsSuite.setupSendEmailOtpSuccess() {
        val url = "${ConfigValues.baseUrl}/user/sendotp_email"
        val response = MockResponses.postUserSendOtpEmail
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
    }

    fun TestsSuite.setupSendEmailOtpFail(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/sendotp_email"
        val response = MockResponses.postUserSendOtpEmailFailWaitAMinute
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
        return response.value<SendOtpResponse>()
    }
}
