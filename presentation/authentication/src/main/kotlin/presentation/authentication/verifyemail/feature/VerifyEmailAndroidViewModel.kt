package presentation.authentication.verifyemail.feature

import androidx.compose.runtime.State
import androidx.lifecycle.viewModelScope
import core.common.coroutines.AbstractCoroutineDispatcher
import domain.common.ErrorHandler
import kotlinx.coroutines.launch
import presentation.authentication.verifyemail.domain.ViewModel
import presentation.authentication.verifyemail.domain.models.Event
import presentation.authentication.verifyemail.domain.models.ScreenData

internal class VerifyEmailAndroidViewModel(
    private val domain: ViewModel,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val errorHandler: <PERSON>rrorHandler,
    private val onClear: () -> Unit
) : androidx.lifecycle.ViewModel() {

    private var stateHolder: StateHolder? = null
    val ui = UIStateHolder()

    val screenData = domain.screenDataStream
    val sideEffects = domain.sideEffectsStream

    fun stateHolder(data: State<ScreenData>): StateHolder {
        if (stateHolder == null) stateHolder = StateHolder(ui = ui, data = data)
        if (data != stateHolder?.data) stateHolder = stateHolder!!.copy(data = data)

        return stateHolder!!
    }

    fun onEvent(event: Event) {
        viewModelScope.launch(dispatchers.default) { errorHandler.async { domain.onEvent(event) } }
    }

    override fun onCleared() {
        onClear()
        super.onCleared()
    }
}
