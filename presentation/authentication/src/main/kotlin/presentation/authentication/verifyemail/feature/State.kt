package presentation.authentication.verifyemail.feature

import androidx.compose.runtime.State
import domain.common.isValidEmail
import kotlinx.coroutines.flow.map
import presentation.authentication.verifyemail.domain.models.ScreenData
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>
)

internal class UIStateHolder(
    val email: StringState = StringState("")
) {
    val actionButtonEnabled = DerivedState(
        email.get().isValidEmail(),
        email.stream.map { it.isValidEmail() }
    )
}