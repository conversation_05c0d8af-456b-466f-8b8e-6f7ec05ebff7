package presentation.authentication.verifyemail.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.common.coroutines.AbstractCoroutineDispatcher
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.SendOtpResponse
import domain.common.isNotValidEmail
import keyless.presentation.common.R
import presentation.authentication.verifyemail.domain.models.SideEffect
import presentation.authentication.verifyemail.domain.models.UserAction
import presentation.authentication.verifyemail.domain.models.toRequest
import presentation.authentication.verifyphone.domain.models.ScreenData
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class SendEmailOtpUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val dispatchers: AbstractCoroutineDispatcher
) {

    suspend fun execute(event: UserAction.SendEmailOtp) = logger.async {
        if (!validate(event.email)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: UserAction.SendEmailOtp) = logger.async {
        val response = repository.sendEmailOtp(event.toRequest())
        screenData.current.launchTimer(dispatchers.backgroundWork)
        if (!response.isSuccess) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun validate(email: String): Boolean = logger.async {
        if (email.isBlank() || email.isNotValidEmail()) {
            status.info(Message(R.string.please_enter_valid_email))
            return@async false
        }

        return@async true
    }

    private suspend fun handleFailResponse(response: SendOtpResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: SendOtpResponse): Unit = logger.async {
        sideEffects.emit(SideEffect.NavToVerifyOtp)
    }
}
