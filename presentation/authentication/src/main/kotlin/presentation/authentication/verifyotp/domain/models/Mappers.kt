package presentation.authentication.verifyotp.domain.models

import data.common.preferences.Preferences
import data.common.utils.phoneNumberWithPlus
import data.keyless.authentication.models.ResendOtpRequest
import data.keyless.authentication.models.SendEmailOtpRequest
import data.keyless.authentication.models.SendOtpMobileRequest
import data.keyless.authentication.models.VerifyEmailOtpRequest
import data.keyless.authentication.models.VerifyOtpRequest

internal fun UserAction.VerifyOtp.toRequest(): VerifyOtpRequest {
    return VerifyOtpRequest(
        otp = otp.toInt(),
        userId = Preferences.userId.get(),
        method = "mobile",
        deviceType = "android",
        uid = Preferences.uuid.get(),
        deviceToken = Preferences.deviceFCMToken.get(),
        countryCode = countryCode.phoneNumberWithPlus(),
        mobileNumber = mobileNumber
    )
}

internal fun UserAction.VerifyEmailOtp.toRequest(): VerifyEmailOtpRequest {
    return VerifyEmailOtpRequest(
        otp = otp.toInt(),
        userId = Preferences.userId.get(),
        uid = Preferences.uuid.get(),
        deviceToken = Preferences.deviceFCMToken.get(),
        email = email
    )
}

internal fun ResendOTPEvent.ResendSmsOtp.toRequest(): ResendOtpRequest {
    return ResendOtpRequest(
        method = "mobile",
        userId = Preferences.userId.get(),
        uid = Preferences.uuid.get(),
        deviceToken = Preferences.deviceFCMToken.get(),
        countryCode = countryCode.phoneNumberWithPlus(),
        mobileNumber = mobileNumber,
        email = null
    )
}

internal fun ResendOTPEvent.ResendWhatsappOtp.toRequest() = SendOtpMobileRequest(
    countryCode = countryCode.phoneNumberWithPlus(),
    mobileNumber = mobileNumber,
    userType = Preferences.userRole.get(),
    userId = Preferences.userId.get()
)

internal fun ResendOTPEvent.ResendCallOtp.toRequest() = SendOtpMobileRequest(
    countryCode = countryCode.phoneNumberWithPlus(),
    mobileNumber = mobileNumber,
    userType = Preferences.userRole.get(),
    userId = Preferences.userId.get()
)

internal fun ResendOTPEvent.ResendEmailOtp.toRequest() = SendEmailOtpRequest(
    email = email,
    userId = Preferences.userId.get()
)