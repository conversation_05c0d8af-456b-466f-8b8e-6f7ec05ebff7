package presentation.authentication.verifyotp.feature

import androidx.compose.runtime.State
import kotlinx.coroutines.flow.map
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.DerivedState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>,
    val state: UIState
)

internal class UIStateHolder(
    val otp: StringState = StringState(""),
    val showResendSheet: BooleanState = BooleanState(false)
) {
    val actionButtonEnabled = DerivedState(
        otp.get().length == 4,
        otp.stream.map { it.length == 4 }
    )
}

internal sealed interface UIState {
    data class PhoneVerification(val phoneNumber: String, val countryCode: String) : UIState
    data class EmailVerification(val email: String) : UIState
}