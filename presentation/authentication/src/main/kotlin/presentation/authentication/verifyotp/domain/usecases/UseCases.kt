package presentation.authentication.verifyotp.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.common.coroutines.AbstractCoroutineDispatcher
import data.keyless.authentication.AuthenticationRepository
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class UseCases(
    private val authentication: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val phoneScreenData: ScreenDataRepository<presentation.authentication.verifyphone.domain.models.ScreenData>,
    private val dispatchers: AbstractCoroutineDispatcher
) {

    val verifyOtp = VerifyOtpUseCase(authentication, logger, status, sideEffects)
    val resendOtp = ResendOtpUseCase(
        authentication,
        logger,
        status,
        sideEffects = sideEffects,
        dispatchers = dispatchers,
        phoneScreenData = phoneScreenData
    )
}
