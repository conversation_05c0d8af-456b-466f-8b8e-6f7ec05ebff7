package presentation.authentication.verifyotp.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.SendOtpResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.login.test.LoginNodes.emailInput
import presentation.authentication.login.test.LoginNodes.loginButton
import presentation.authentication.login.test.LoginNodes.passwordInput
import presentation.authentication.verifyotp.test.VerifyOtpNodes.screenTitle
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.mobileNumber
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.smsButton
import presentation.authentication.verifyemail.test.VerifyEmailNodes.emailInput as verifyEmailInput
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screenTitle as verifyEmailScreenTitle
import presentation.authentication.verifyemail.test.VerifyEmailNodes.verifyButton as verifyEmailButton
import presentation.authentication.verifyphone.test.VerifyPhoneNodes.screenTitle as verifyPhoneScreenTitle

internal object Setups: KoinTest {

    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupGuestLoginEmail() {
        val loginUrl = "${ConfigValues.baseUrl}/user/login"
        val verifyEmailUrl = "${ConfigValues.baseUrl}/user/sendotp_email"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 1, userType = "Guest")
        val verifyEmailMock = MockResponses.postUserSendOtpEmail
        val loginResponse = MockHttpResponse(url = loginUrl, method = "POST", body = json.encodeToString(modified))
        val verifyEmailResponse = MockHttpResponse(url = verifyEmailUrl, method = "POST", body = verifyEmailMock.raw)
        client.setup(loginResponse)
        client.setup(verifyEmailResponse)

        compose.emailInput.performTextInput("username")
        compose.passwordInput.performTextInput("password", true)
        compose.loginButton.performClick { compose.verifyEmailScreenTitle.assertExists() }
        compose.verifyEmailInput.performTextInput("<EMAIL>")
        compose.verifyEmailButton.performClick { compose.screenTitle.assertExists() }
    }

    fun TestsSuite.setupGuestLoginPhone() {
        val loginUrl = "${ConfigValues.baseUrl}/user/login"
        val verifyPhoneUrl = "${ConfigValues.baseUrl}/user/sendotp_mobile"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 0, userType = "Guest")
        val verifyPhoneMock = MockResponses.postUserSendOtpMobile
        val response = MockHttpResponse(url = loginUrl, method = "POST", body = json.encodeToString(modified))
        val verifyPhoneResponse = MockHttpResponse(url = verifyPhoneUrl, method = "POST", body = verifyPhoneMock.raw)
        client.setup(verifyPhoneResponse)
        client.setup(response)

        compose.emailInput.performTextInput("username")
        compose.passwordInput.performTextInput("password", true)
        compose.loginButton.performClick { compose.verifyPhoneScreenTitle.assertExists() }
        compose.mobileNumber.performTextInput("1234567890")
        compose.smsButton.performClick { compose.screenTitle.assertExists() }
    }

    fun TestsSuite.setupVerifyEmailOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verify_emailotp"
        val response = MockResponses.postVerifyEmailOtp.value<SendOtpResponse>().copy(profileStatus = 2)
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupVerifyEmailOtpFail(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verify_emailotp"
        val response = MockResponses.postVerifyEmailOtpFail
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
        return response.value()
    }

    fun TestsSuite.setupVerifyPhoneOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verifyLoginOTP"
        val response = MockResponses.postVerifyOtp.value<SendOtpResponse>().copy(profileStatus = 1)
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupVerifyPhoneOtpFail(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verifyLoginOTP"
        val response = MockResponses.postVerifyOtpFail
        client.setup(MockHttpResponse(url = url, method = "POST", body = response.raw))
        return response.value()
    }

    fun TestsSuite.setupResendSmsOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/resend_otp"
        val response = MockResponses.postResendOtp.value<SendOtpResponse>()
        client.setup(MockHttpResponse(url = url, method = "POST", body = MockResponses.postResendOtp.raw))
        return response
    }

    fun TestsSuite.setupResendWhatsappOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/sendotp-whatsapp"
        val response = MockResponses.postResendOtp.value<SendOtpResponse>()
        client.setup(MockHttpResponse(url = url, method = "POST", body = MockResponses.postResendOtp.raw))
        return response
    }

    fun TestsSuite.setupResendCallOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/send-voice-otp"
        val response = MockResponses.postResendOtp.value<SendOtpResponse>()
        client.setup(MockHttpResponse(url = url, method = "POST", body = MockResponses.postResendOtp.raw))
        return response
    }
}
