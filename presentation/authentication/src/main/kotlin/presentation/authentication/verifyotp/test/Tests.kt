package presentation.authentication.verifyotp.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object VerifyOtpTestTags {
    val tag = this::class.java.name
    val screen = tag + ".screen"
    val screenTitle = tag + ".screenTitle"
    val screenDescription = tag + ".screenDescription"
    val otpInput = tag + ".otpInput"
    val verifyButton = tag + ".verifyButton"
    val resendCodeText = tag + ".resendCodeText"
    val resendCodeButton = tag + ".resendCodeButton"
    val timerText = tag + ".timerText"
    val resendOtpBottomSheet = tag + ".resendOtpBottomSheet"
    val changeEmailButton = tag + ".changeEmailButton"
    val didNotReceiveEmailText = tag + ".didNotReceiveEmailText"
}

internal object VerifyOtpNodes {
    val ComposeTestRule.screen get() = tagged(VerifyOtpTestTags.screen)
    val ComposeTestRule.screenTitle get() = tagged(VerifyOtpTestTags.screenTitle)
    val ComposeTestRule.screenDescription get() = tagged(VerifyOtpTestTags.screenDescription)
    val ComposeTestRule.otpInput get() = tagged(VerifyOtpTestTags.otpInput)
    val ComposeTestRule.verifyButton get() = tagged(VerifyOtpTestTags.verifyButton)
    val ComposeTestRule.resendCodeText get() = tagged(VerifyOtpTestTags.resendCodeText)
    val ComposeTestRule.resendCodeButton get() = tagged(VerifyOtpTestTags.resendCodeButton)
    val ComposeTestRule.timerText get() = tagged(VerifyOtpTestTags.timerText)
    val ComposeTestRule.resendOtpBottomSheet get() = tagged(VerifyOtpTestTags.resendOtpBottomSheet)
    val ComposeTestRule.changeEmailButton get() = tagged(VerifyOtpTestTags.changeEmailButton)
    val ComposeTestRule.didNotReceiveEmailText get() = tagged(VerifyOtpTestTags.didNotReceiveEmailText)
}

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: VerifyOtpSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}