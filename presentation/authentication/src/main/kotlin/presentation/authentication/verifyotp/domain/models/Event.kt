package presentation.authentication.verifyotp.domain.models

internal sealed interface Event

internal sealed interface UserAction : Event {
    data class VerifyOtp(val otp: String, val countryCode: String, val mobileNumber: String) : UserAction

    data class VerifyEmailOtp(val otp: String, val email: String, ) : UserAction

    object ChangeEmail : UserAction
}

internal sealed interface ResendOTPEvent : Event {
    data class ResendSmsOtp(val countryCode: String, val mobileNumber: String) : ResendOTPEvent

    data class ResendWhatsappOtp(val countryCode: String, val mobileNumber: String) : ResendOTPEvent

    data class ResendCallOtp(val countryCode: String, val mobileNumber: String) : ResendOTPEvent

    data class ResendEmailOtp(val email: String) : ResendOTPEvent
}