package presentation.authentication.verifyotp.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.login.loginInjection
import presentation.authentication.verifyemail.verifyEmailInjection
import presentation.authentication.verifyotp.test.Setups.setupGuestLoginEmail
import presentation.authentication.verifyotp.test.Setups.setupGuestLoginPhone
import presentation.authentication.verifyotp.test.Setups.setupResendCallOtpSuccess
import presentation.authentication.verifyotp.test.Setups.setupResendSmsOtpSuccess
import presentation.authentication.verifyotp.test.Setups.setupResendWhatsappOtpSuccess
import presentation.authentication.verifyotp.test.Setups.setupVerifyEmailOtpFail
import presentation.authentication.verifyotp.test.Setups.setupVerifyEmailOtpSuccess
import presentation.authentication.verifyotp.test.Setups.setupVerifyPhoneOtpFail
import presentation.authentication.verifyotp.test.Setups.setupVerifyPhoneOtpSuccess
import presentation.authentication.verifyotp.verifyOtpInjection
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class VerifyOtpSharedTests : KoinTest {
    abstract val isUnitTest: Boolean
    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.LOGIN } },
                loginInjection, verifyEmailInjection, verifyOtpInjection, verifyPhoneInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        Setups.clear()
        scenario.close()
    }

    @Test
    fun assertVerifyEmailOtpScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertVerifyEmailOtpScreenLayout()
    }

    @Test
    fun assertVerifyPhoneOtpScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.assertVerifyPhoneOtpScreenLayout()
    }

    @Test
    fun assertOtpInputValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.setupVerifyPhoneOtpSuccess()
        suite.assertOtpInputValidation()
    }

    @Test
    fun assertEmailOtpInputValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.setupVerifyEmailOtpSuccess()
        suite.assertEmailOtpInputValidation()
    }

    @Test
    fun assertFailedEmailOtpValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.setupVerifyEmailOtpFail()
        suite.assertFailedEmailOtpValidation()
    }

    @Test
    fun assertFailedPhoneOtpValidation() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginPhone()
        suite.setupVerifyPhoneOtpFail()
        suite.assertFailedPhoneOtpValidation()
    }

    @Test
    fun assertResendCodeFunctionality() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginPhone()

        suite.setupResendSmsOtpSuccess()
        suite.setupResendWhatsappOtpSuccess()
        suite.setupResendCallOtpSuccess()

        suite.assertResendPhoneCodeFunctionality()
    }

    @Test
    fun assertResendEmailCodeFunctionality() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertResendEmailCodeFunctionality()
    }

    @Test
    fun assertChangeEmailFunctionality() = commonRunTest(timeout = 30.seconds) {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertChangeEmailFunctionality()
    }

    @Test
    fun assertBackButtonEnabled() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@VerifyOtpSharedTests, this)
        suite.setupGuestLoginEmail()
        suite.assertBackButtonEnabled()
    }
}
