package presentation.authentication.verifyotp.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import core.common.status.StatusRepository
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.login.feature.LoginFragment.Companion.DASHBOARD_ACTIVITY_CLASS
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.common.feature.components.appToast
import presentation.common.feature.components.safeNavigate
import presentation.common.feature.components.successDialogWithOkButton
import presentation.common.feature.theme.AppTheme

class VerifyOtpFragment : Fragment() {

    private val viewModel: VerifyOtpAndroidViewModel by viewModel()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.sideEffects.collect(::onSideEffect)
            }
        }
    }

    private fun onSideEffect(sideEffect: SideEffect) {
        when (sideEffect) {
            is SideEffect.NavToDashboard -> {
                if (DASHBOARD_ACTIVITY_CLASS == null) return
                val int = Intent(requireContext(), DASHBOARD_ACTIVITY_CLASS).putExtra("runSplash", false)
                requireActivity().startActivity(int)
                requireActivity().finishAffinity()
            }

            is SideEffect.NavToVerifyEmail -> {
                findNavController().safeNavigate(
                    keyless.presentation.authentication.R.id.verifyOtpFragment,
                    keyless.presentation.authentication.R.id.action_verifyOtp_to_verifyEmail
                )
            }

            is SideEffect.BackNav -> {
                requireActivity().onBackPressedDispatcher.onBackPressed()
            }
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val screenData = viewModel.screenData.collectAsState(initial = ScreenData.empty)
        val state = remember(screenData) { viewModel.initState(screenData, getState()) }

        VerifyOtpScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onBackClick = { findNavController().popBackStack() },
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }

    private fun getState(): UIState {
        val phoneNumber = arguments?.getString(phoneNumberKey)
        val countryCode = arguments?.getString(countryCodeKey)
        if (phoneNumber != null && countryCode != null) {
            return UIState.PhoneVerification(phoneNumber = phoneNumber, countryCode = countryCode)
        }
        val email = arguments?.getString(emailKey)
        if (email != null) {
            return UIState.EmailVerification(email = email)
        }

        throw IllegalStateException("No arguments provided to otp screen")
    }

    companion object {
        val phoneNumberKey = this::class.java.name + ".phoneNumberKey"
        val countryCodeKey = this::class.java.name + ".countryCodeKey"
        val emailKey = this::class.java.name + ".emailKey"
    }
}