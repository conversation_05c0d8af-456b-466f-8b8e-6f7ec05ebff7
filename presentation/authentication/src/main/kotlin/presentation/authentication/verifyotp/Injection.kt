package presentation.authentication.verifyotp

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.verifyotp.domain.ViewModel
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.authentication.verifyotp.domain.usecases.UseCases
import presentation.authentication.verifyotp.feature.VerifyOtpAndroidViewModel
import presentation.authentication.verifyphone.VerifyPhoneInjectionScope
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object VerifyOtpInjectionScope

val verifyOtpInjection = module {
    scope<VerifyOtpInjectionScope> {
        scoped {
            val scope = getKoin().getOrCreateScope<VerifyPhoneInjectionScope>(VerifyPhoneInjectionScope.getScopeId())
            val phoneData = scope
                .get<ScreenDataRepository<presentation.authentication.verifyphone.domain.models.ScreenData>>()

            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream, phoneData.stream) { data, status, phoneData ->
                    data.copy(status = status, counter = phoneData.timerStream)
                }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped {
            val scope = getKoin().getOrCreateScope<VerifyPhoneInjectionScope>(VerifyPhoneInjectionScope.getScopeId())
            val phoneData = scope
                .get<ScreenDataRepository<presentation.authentication.verifyphone.domain.models.ScreenData>>()
            UseCases(
                authentication = get(),
                logger = get(),
                status = get(),
                sideEffects = get(),
                screenData = get(),
                dispatchers = get(),
                phoneScreenData = phoneData
            )
        }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<VerifyOtpInjectionScope>(VerifyOtpInjectionScope.getScopeId())
        VerifyOtpAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}
