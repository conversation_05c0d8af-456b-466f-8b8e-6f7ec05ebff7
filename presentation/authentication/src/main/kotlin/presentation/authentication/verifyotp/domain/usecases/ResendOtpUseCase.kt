package presentation.authentication.verifyotp.domain.usecases

import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.SendOtpResponse
import presentation.authentication.verifyotp.domain.models.ResendOTPEvent
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.authentication.verifyotp.domain.models.toRequest
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ResendOtpUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val dispatchers: AbstractCoroutineDispatcher,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val phoneScreenData: ScreenDataRepository<presentation.authentication.verifyphone.domain.models.ScreenData>
) {

    suspend fun execute(event: ResendOTPEvent) = logger.async {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: ResendOTPEvent) = logger.async {
        val response = remoteJob(event)
        phoneScreenData.current.launchTimer(dispatchers.backgroundWork)
        if (!response.isSuccess) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun remoteJob(event: ResendOTPEvent) = logger.async {
        when (event) {
            is ResendOTPEvent.ResendSmsOtp -> repository.resendOtp(event.toRequest())
            is ResendOTPEvent.ResendWhatsappOtp -> repository.sendOtpWhatsApp(event.toRequest())
            is ResendOTPEvent.ResendCallOtp -> repository.sendOtpPhoneCall(event.toRequest())
            is ResendOTPEvent.ResendEmailOtp -> repository.sendEmailOtp(event.toRequest())
        }
    }

    private suspend fun handleFailResponse(response: SendOtpResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: SendOtpResponse): Unit = logger.async {
        status.info(Message.fromString(response.message))
    }
}
