package presentation.authentication.verifyotp.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import core.common.status.Status
import data.common.utils.toOnlySecondsTimerDisplay
import keyless.presentation.common.R
import presentation.authentication.verifyotp.domain.models.Event
import presentation.authentication.verifyotp.domain.models.ResendOTPEvent
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.authentication.verifyotp.domain.models.UserAction
import presentation.authentication.verifyotp.test.VerifyOtpTestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppBottomSheet
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppOtpTextField
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppTextButton
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.verticalScrollAdaptToIme
import presentation.common.feature.theme.AppTheme


@Composable
internal fun VerifyOtpScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onBackClick: () -> Unit,
    onCancelStatus: (Status) -> Unit
) {
    AppLogoPage(
        modifier = Modifier.testTag(VerifyOtpTestTags.screen),
        isBackEnabled = true,
        status = state.data.value.status,
        onCancelStatus = onCancelStatus,
        onBackPress = onBackClick
    ) {
        ScreenLayout(state = state, onEvent = onEvent)
    }
}

@Composable
private fun ScreenLayout(state: StateHolder, onEvent: (Event) -> Unit) {
    AppColumn(
        modifier = Modifier
            .fillMaxHeight()
            .verticalScrollAdaptToIme(),
        arrangement = Arrangement.SpaceBetween
    ) {
        MobileNumberSection(state = state, onEvent = onEvent)

        ActionButton(state, onEvent)
    }
}

@Composable
private fun MobileNumberSection(
    state: StateHolder,
    onEvent: (Event) -> Unit
) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        when (state.state) {
            is UIState.EmailVerification -> EmailNumberSection(state.state)
            is UIState.PhoneVerification -> MobileNumberSection(state.state)
        }

        OtpTextField(state)

        ResendCodeSection(state, onEvent)
    }
}

@Composable
private fun ColumnScope.MobileNumberSection(state: UIState.PhoneVerification) {
    AppPageTitleText(
        modifier = Modifier.testTag(VerifyOtpTestTags.screenTitle),
        text = stringResource(R.string.check_your_phone)
    )

    AppBodyText(
        modifier = Modifier.testTag(VerifyOtpTestTags.screenDescription),
        text = stringResource(R.string.to_confirm_your_account_enter_the_4digit_code) +
                " ${state.countryCode} ${state.phoneNumber}"
    )
}

@Composable
private fun ColumnScope.EmailNumberSection(state: UIState.EmailVerification) {
    AppPageTitleText(
        modifier = Modifier.testTag(VerifyOtpTestTags.screenTitle),
        text = stringResource(R.string.check_your_mail)
    )

    AppBodyText(
        modifier = Modifier.testTag(VerifyOtpTestTags.screenDescription),
        text = stringResource(R.string.to_confirm_your_account_enter_the_4digit_code) +
                " ${state.email}"
    )
}


@Composable
private fun ResendCodeSection(state: StateHolder, onEvent: (Event) -> Unit) {
    val initial = remember { state.data.value.counter.value }
    val timer = state.data.value.counter.collectAsStateWithLifecycle(initial).value

    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.Center,
        alignment = Alignment.CenterHorizontally
    ) {
        if (timer > 0) {
            AppBodyText(
                modifier = Modifier.testTag(VerifyOtpTestTags.timerText),
                text = stringResource(R.string.resend_code_after_00) + timer.toOnlySecondsTimerDisplay() +
                        " " + stringResource(R.string.seconds)
            )
        } else {
            AppBodyText(
                modifier = Modifier.testTag(VerifyOtpTestTags.resendCodeText),
                text = stringResource(R.string.didn_t_get_the_code)
            )

            AppTextButton(
                modifier = Modifier.testTag(VerifyOtpTestTags.resendCodeButton),
                text = stringResource(R.string.resend_code),
                onClick = {
                    if (state.state is UIState.PhoneVerification) {
                        state.ui.showResendSheet.update(true)
                    } else if (state.state is UIState.EmailVerification) {
                        onEvent(ResendOTPEvent.ResendEmailOtp(state.state.email))
                    }
                }
            )
        }
    }

    if (state.ui.showResendSheet.value && state.state is UIState.PhoneVerification) ResendPhoneSheet(
        state = state.state,
        onDismiss = { state.ui.showResendSheet.update(false) },
        onEvent = onEvent
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ResendPhoneSheet(state: UIState.PhoneVerification, onDismiss: () -> Unit, onEvent: (Event) -> Unit) {
    AppBottomSheet(
        modifier = Modifier.testTag(VerifyOtpTestTags.resendOtpBottomSheet),
        onDismiss = onDismiss,
        title = stringResource(R.string.resend_code)
    ) {
        AppColumn(
            modifier = Modifier.fillMaxWidth(),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
            alignment = Alignment.CenterHorizontally
        ) {
            AppTextButton(text = stringResource(R.string.receive_by_phone_sms)) {
                onEvent(ResendOTPEvent.ResendSmsOtp(countryCode = state.countryCode, mobileNumber = state.phoneNumber))
                onDismiss()
            }

            AppTextButton(text = stringResource(R.string.receive_by_whatsapp)) {
                onEvent(
                    ResendOTPEvent.ResendWhatsappOtp(countryCode = state.countryCode, mobileNumber = state.phoneNumber)
                )
                onDismiss()
            }

            AppTextButton(text = stringResource(R.string.receive_by_phone_call)) {
                onEvent(ResendOTPEvent.ResendCallOtp(countryCode = state.countryCode, mobileNumber = state.phoneNumber))
                onDismiss()
            }
        }
    }
}

@Composable
private fun ActionButton(state: StateHolder, onEvent: (Event) -> Unit) {
    when (state.state) {
        is UIState.PhoneVerification -> {
            AppButton(
                modifier = Modifier.testTag(VerifyOtpTestTags.verifyButton),
                text = stringResource(R.string.verify_otp),
                isEnabled = true,
                onClick = {
                    onEvent(
                        UserAction.VerifyOtp(
                            otp = state.ui.otp.get(),
                            countryCode = state.state.countryCode,
                            mobileNumber = state.state.phoneNumber
                        )
                    )
                }
            )
        }

        is UIState.EmailVerification -> {
            EmailVerificationAction(state, state.state.email, onEvent)
        }
    }
}

@Composable
private fun EmailVerificationAction(state: StateHolder, email: String, onEvent: (Event) -> Unit) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium, Alignment.CenterVertically)
    ) {

        AppButton(
            modifier = Modifier.testTag(VerifyOtpTestTags.verifyButton),
            text = stringResource(R.string.verify_otp),
            isEnabled = true,
            onClick = { onEvent(UserAction.VerifyEmailOtp(otp = state.ui.otp.get(), email = email)) }
        )

        if (state.state is UIState.EmailVerification) {
            AppBodyText(
                modifier = Modifier
                    .fillMaxWidth()
                    .testTag(VerifyOtpTestTags.didNotReceiveEmailText),
                text = stringResource(R.string.did_not_receive_the_email_check_your_nspam_filter_junk_mail) +
                        " ${stringResource(R.string.or)}",
                design = DesignSystem.Text.Body.copy(alignment = TextAlign.Center)
            )

            AppTextButton(
                modifier = Modifier.testTag(VerifyOtpTestTags.changeEmailButton),
                text = stringResource(R.string.use_another_email_address),
                onClick = { onEvent(UserAction.ChangeEmail) }
            )
        }
    }
}

@Composable
private fun OtpTextField(state: StateHolder) {
    AppRow(modifier = Modifier.fillMaxWidth(), arrangement = Arrangement.Center) {
        AppOtpTextField(
            modifier = Modifier.testTag(VerifyOtpTestTags.otpInput),
            state = state.ui.otp,
            count = 4,
            design = DesignSystem.TextField.NormalTextField.copy(
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
        )
    }
}

@Preview
@Composable
private fun Preview() {
    val phone = remember { UIState.EmailVerification(email = "") }
    val state = remember { StateHolder(data = mutableStateOf(ScreenData.empty), state = phone) }

    AppTheme { VerifyOtpScreen(state, onEvent = {}, onBackClick = {}, onCancelStatus = {}) }
}