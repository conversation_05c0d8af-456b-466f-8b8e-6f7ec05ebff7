package presentation.authentication.verifyotp.domain

import presentation.authentication.verifyotp.domain.models.Event
import presentation.authentication.verifyotp.domain.models.ResendOTPEvent
import presentation.authentication.verifyotp.domain.models.ScreenData
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.authentication.verifyotp.domain.models.UserAction
import presentation.authentication.verifyotp.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is UserAction -> onUserAction(event)
        is ResendOTPEvent -> useCases.resendOtp.execute(event)
    }

    private suspend fun onUserAction(event: UserAction) = when (event) {
        is UserAction.VerifyOtp -> useCases.verifyOtp.execute(event)
        is UserAction.VerifyEmailOtp -> useCases.verifyOtp.execute(event)
        is UserAction.ChangeEmail -> sideEffects.emit(SideEffect.BackNav)
    }
}
