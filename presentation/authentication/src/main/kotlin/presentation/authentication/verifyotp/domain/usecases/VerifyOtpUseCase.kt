package presentation.authentication.verifyotp.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.ProfileStatus
import data.keyless.authentication.models.SendOtpResponse
import keyless.presentation.common.R
import presentation.authentication.verifyotp.domain.models.SideEffect
import presentation.authentication.verifyotp.domain.models.UserAction
import presentation.authentication.verifyotp.domain.models.toRequest
import presentation.common.domain.repositories.SideEffectsRepository

internal class VerifyOtpUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.VerifyOtp) = logger.async {
        if (!validate(event.otp)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { verifyOtp(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    suspend fun execute(event: UserAction.VerifyEmailOtp) = logger.async {
        if (!validate(event.otp)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { verifyEmailOtp(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun validate(otp: String): Boolean = logger.async {
        if (otp.isBlank() || otp.length != 4) {
            status.info(Message(R.string.please_enter_valid_otp))
            return@async false
        }

        return@async true
    }

    private suspend fun verifyOtp(event: UserAction.VerifyOtp) = logger.async {
        val response = repository.verifyOtp(event.toRequest())

        if (!response.isSuccess) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun verifyEmailOtp(event: UserAction.VerifyEmailOtp) = logger.async {
        val response = repository.verifyEmailOtp(event.toRequest())

        if (!response.isSuccess) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun handleFailResponse(response: SendOtpResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: SendOtpResponse): Unit = logger.async {
        when (response.profileCompletionStatus) {
            ProfileStatus.VERIFY_PHONE -> Unit
            ProfileStatus.VERIFY_EMAIL -> sideEffects.emit(SideEffect.NavToVerifyEmail)
            ProfileStatus.COMPLETE -> loginSuccess(response)
        }
    }

    private suspend fun loginSuccess(response: SendOtpResponse) = logger.async {
        Preferences.authenticationToken.set("Bearer " + response.token)
        Preferences.isLoggedIn.set(true)
        Preferences.role.set(response.role)
        Preferences.userRole.set(response.userType)
        if (response.firstName.isBlank()) {
            Preferences.name.set(response.name)
        } else {
            Preferences.name.set(response.firstName + " " + response.firstName)
        }

        sideEffects.emit(SideEffect.NavToDashboard)
    }

}
