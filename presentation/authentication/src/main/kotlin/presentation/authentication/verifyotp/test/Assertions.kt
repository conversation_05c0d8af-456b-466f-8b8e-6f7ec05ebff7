package presentation.authentication.verifyotp.test

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performTextInput
import keyless.presentation.common.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import presentation.authentication.verifyemail.test.VerifyEmailNodes
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screen
import presentation.authentication.verifyotp.test.Setups.setupVerifyEmailOtpFail
import presentation.authentication.verifyotp.test.Setups.setupVerifyPhoneOtpFail
import presentation.authentication.verifyotp.test.VerifyOtpNodes.changeEmailButton
import presentation.authentication.verifyotp.test.VerifyOtpNodes.didNotReceiveEmailText
import presentation.authentication.verifyotp.test.VerifyOtpNodes.otpInput
import presentation.authentication.verifyotp.test.VerifyOtpNodes.resendCodeButton
import presentation.authentication.verifyotp.test.VerifyOtpNodes.resendCodeText
import presentation.authentication.verifyotp.test.VerifyOtpNodes.resendOtpBottomSheet
import presentation.authentication.verifyotp.test.VerifyOtpNodes.screenDescription
import presentation.authentication.verifyotp.test.VerifyOtpNodes.screenTitle
import presentation.authentication.verifyotp.test.VerifyOtpNodes.timerText
import presentation.authentication.verifyotp.test.VerifyOtpNodes.verifyButton
import presentation.common.feature.theme.lightColorScheme
import presentation.common.test.CommonTestTags
import presentation.common.test.assertBackButtonClick
import presentation.common.test.assertBackButtonLayout
import presentation.common.test.assertDialogReturnButton
import presentation.common.test.assertToast
import presentation.test.getChild
import presentation.test.tagged
import presentation.test.testDispatcher
import presentation.test.wait
import presentation.authentication.verifyemail.test.VerifyEmailNodes.screen as verifyEmailScreen

internal fun TestsSuite.assertVerifyEmailOtpScreenLayout() {
    assertScreenTitleLayout(R.string.check_your_mail)
    assertScreenDescriptionLayout("<EMAIL>")
    assertOtpInputLayout()
    assertVerifyButtonLayout()
    assertDidNotReceiveEmailTextLayout()
    assertChangeEmailButtonLayout()
}

internal fun TestsSuite.assertVerifyPhoneOtpScreenLayout() {
    assertScreenTitleLayout(R.string.check_your_phone)
    assertScreenDescriptionLayout("+971 1234567890")
    assertOtpInputLayout()
    assertVerifyButtonLayout()
    assertResendCodeLayout()
}

internal fun TestsSuite.assertOtpInputValidation() {
    assertOtpInput("")
    compose.verifyButton.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_otp)) }
    clearToast()

    assertOtpInput("123")
    compose.verifyButton.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_otp)) }
    clearToast()

    assertOtpInput("1234")
    compose.verifyButton.performClick { compose.screen.assertExists() }
}

internal fun TestsSuite.assertEmailOtpInputValidation() {
    assertOtpInput("")
    compose.verifyButton.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_otp)) }
    clearToast()

    assertOtpInput("123")
    compose.verifyButton.performClick { compose.assertToast(activity.getString(R.string.please_enter_valid_otp)) }
    clearToast()

    compose.screenTitle.assertExists()
    assertOtpInput("1234")
    compose.verifyButton.performClick { }
}

internal fun TestsSuite.assertFailedEmailOtpValidation() {
    assertOtpInput("1234")

    val response = setupVerifyEmailOtpFail()
    compose.verifyButton.performClick { compose.assertDialogReturnButton(response.message).performClick() }
    compose.tagged(CommonTestTags.appDialog(response.message)).assertDoesNotExist()
}

internal fun TestsSuite.assertFailedPhoneOtpValidation() {
    assertOtpInput("1234")

    val response = setupVerifyPhoneOtpFail()
    compose.verifyButton.performClick { compose.assertDialogReturnButton(response.message).performClick() }
    compose.tagged(CommonTestTags.appDialog(response.message)).assertDoesNotExist()
}

internal fun TestsSuite.assertResendPhoneCodeFunctionality() {
    assertResendPhoneOtpOption(activity.getString(R.string.receive_by_phone_sms))
    assertResendPhoneOtpOption(activity.getString(R.string.receive_by_whatsapp))
    assertResendPhoneOtpOption(activity.getString(R.string.receive_by_phone_call))
}

internal fun TestsSuite.assertResendEmailCodeFunctionality() {
    compose.timerText.assertIsDisplayed()
    advanceBackground(55_000)
    compose.timerText.assertIsDisplayed()
    advanceBackground(61000)
    compose.timerText.assertDoesNotExist()

    compose.resendCodeText.assertIsDisplayed()
    compose.resendCodeButton.assertIsDisplayed().assertIsEnabled()

    // Click resend button to show options
    compose.resendCodeButton.performClick { }
    compose.timerText.assertIsDisplayed()
    advanceBackground(55_000)
    compose.timerText.assertIsDisplayed()
    advanceBackground(61000)
    compose.timerText.assertDoesNotExist()
}

private fun TestsSuite.assertResendPhoneOtpOption(text: String) {
    compose.timerText.assertIsDisplayed()
    advanceBackground(55_000)
    compose.timerText.assertIsDisplayed()
    advanceBackground(61000)
    compose.timerText.assertDoesNotExist()

    compose.resendCodeText.assertIsDisplayed()
    compose.resendCodeButton.assertIsDisplayed().assertIsEnabled()

    // Click resend button to show options
    compose.resendCodeButton.performClick { }
    assertResendOtpBottomSheetLayout()

    val bottomSheet = compose.resendOtpBottomSheet
    val button = bottomSheet.getChild { it.getChild { it.assertTextEquals(text) } }
    button.assertIsDisplayed().assertIsEnabled().performClick { bottomSheet.assertDoesNotExist() }

    compose.timerText.assertIsDisplayed()
}


internal fun TestsSuite.assertChangeEmailFunctionality() {
    compose.changeEmailButton.assertIsDisplayed().assertIsEnabled()

    compose.changeEmailButton.performClick { compose.verifyEmailScreen.assertExists() }
}

internal fun TestsSuite.assertScreenTitleLayout(titleResId: Int) {
    val node = compose.screenTitle
    node.assertIsDisplayed().assertTextEquals(activity.getString(titleResId))
}

internal fun TestsSuite.assertScreenDescriptionLayout(description: String) {
    val node = compose.screenDescription
    node.assertIsDisplayed().assertTextEquals(
        activity.getString(R.string.to_confirm_your_account_enter_the_4digit_code) + " " + description
    )
}

internal fun TestsSuite.assertOtpInputLayout() {
    val node = compose.otpInput
    node.assertIsDisplayed().assertIsEnabled()
}

internal fun TestsSuite.assertOtpInput(input: String) {
    val node = compose.otpInput
    val inputField = node.getChild { it.performTextInput("") }
    inputField.assertIsDisplayed().assertIsEnabled().performTextClearance().performTextInput(input)
}

internal fun TestsSuite.assertVerifyButtonLayout() {
    val node = compose.verifyButton
    val text = node.getChild { it.assertTextEquals(activity.getString(R.string.verify_otp)) }

    node.assertIsDisplayed().assertIsEnabled().assertBackgroundColor(lightColorScheme.secondary)
        .assertContentColor(lightColorScheme.onSecondary)
    text.assertIsDisplayed()
}

internal fun TestsSuite.assertResendCodeLayout() {
    // Initially, timer should be displayed
    compose.timerText.assertIsDisplayed()
}

internal fun TestsSuite.assertDidNotReceiveEmailTextLayout() {
    val node = compose.didNotReceiveEmailText
    node.assertIsDisplayed()
        .assertTextEquals(
            activity.getString(R.string.did_not_receive_the_email_check_your_nspam_filter_junk_mail) + " " +
                    activity.getString(R.string.or)
        )
}

internal fun TestsSuite.assertChangeEmailButtonLayout() {
    val node = compose.changeEmailButton
    val text = node.getChild { it.assertTextEquals(activity.getString(R.string.use_another_email_address)) }

    node.assertIsDisplayed().assertIsEnabled()
    text.assertIsDisplayed()
}

internal fun TestsSuite.clearToast() {
    advanceBackground(100000)
    compose.assertToast(null)
}

internal fun TestsSuite.assertBackButtonEnabled() {
    compose.wait {
        advanceBackground(10_000)
        val context = if (shared.isUnitTest) testDispatcher else Dispatchers.Main
        testScope.launch(context) { (activity as ComponentActivity).onBackPressedDispatcher.onBackPressed() }
        compose.verifyEmailScreen.assertExists()
    }

    with(VerifyEmailNodes) {
        compose.verifyButton.performClick { with(VerifyOtpNodes) { compose.screenTitle.assertExists() } }
    }

    compose.assertBackButtonLayout(true)
    compose.assertBackButtonClick { compose.verifyEmailScreen.assertExists() }
}

internal fun TestsSuite.assertResendOtpBottomSheetLayout() {
    val node = compose.resendOtpBottomSheet
    node.assertIsDisplayed()

    val smsButton = node.getChild { it.assertTextEquals(activity.getString(R.string.receive_by_phone_sms)) }
    val whatsappButton = node.getChild { it.assertTextEquals(activity.getString(R.string.receive_by_whatsapp)) }
    val callButton = node.getChild { it.assertTextEquals(activity.getString(R.string.receive_by_phone_call)) }

    smsButton.assertIsDisplayed().assertIsEnabled()
    whatsappButton.assertIsDisplayed().assertIsEnabled()
    callButton.assertIsDisplayed().assertIsEnabled()
}