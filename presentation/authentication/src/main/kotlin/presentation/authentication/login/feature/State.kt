package presentation.authentication.login.feature

import androidx.compose.runtime.State
import presentation.authentication.login.domain.models.ScreenData
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.StringState

internal data class StateHolder(
    val ui: UIStateHolder = UIStateHolder(),
    val data: State<ScreenData>
)

class UIStateHolder(
    val email: StringState = StringState(""),
    val password: PasswordStateHolder = PasswordStateHolder()
)

class PasswordStateHolder(
    val text: StringState = StringState(""),
    val hide: BooleanState = BooleanState(true)
)

sealed interface UIState {

    data object LoginEmailPassword : UIState
}