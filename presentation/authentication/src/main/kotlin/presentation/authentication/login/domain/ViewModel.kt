package presentation.authentication.login.domain

import presentation.authentication.login.domain.models.Event
import presentation.authentication.login.domain.models.ScreenData
import presentation.authentication.login.domain.models.ScreenEvent
import presentation.authentication.login.domain.models.SideEffect
import presentation.authentication.login.domain.models.UserAction
import presentation.authentication.login.domain.usecases.UseCases
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal class ViewModel(
    private val data: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val useCases: UseCases
) {

    val screenDataStream = data.stream
    val sideEffectsStream = sideEffects.stream

    suspend fun onEvent(event: Event) = when (event) {
        is UserAction -> onUserAction(event)

        is ScreenEvent -> onScreenEvent(event)
    }

    private suspend fun onUserAction(event: UserAction) = when (event) {
        is UserAction.Login -> useCases.login.execute(event)
    }

    private suspend fun onScreenEvent(event: ScreenEvent) = when (event) {
        is ScreenEvent.NavToForgotPassword -> sideEffects.emit(SideEffect.NavToForgotPassword)
    }
}