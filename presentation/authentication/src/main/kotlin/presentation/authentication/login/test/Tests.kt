package presentation.authentication.login.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import presentation.test.tagged

internal object TestTags {
    val tag = this::class.java.name
    val screenTitle = tag + ".screenTitle"
    val emailInput = tag + ".emailInput"
    val emailLabel = tag + ".emailLabel"
    val passwordInput = tag + ".passwordInput"
    val passwordLabel = tag + ".passwordLabel"
    val loginButton = tag + ".loginButton"
    val forgotPassword = tag + ".forgotPassword"
}

internal object LoginNodes {
    val ComposeTestRule.screenTitle get() = tagged(TestTags.screenTitle)
    val ComposeTestRule.emailInput get() = tagged(TestTags.emailInput, true)
    val ComposeTestRule.emailLabel get() = tagged(TestTags.emailLabel)
    val ComposeTestRule.passwordInput get() = tagged(TestTags.passwordInput, true)
    val ComposeTestRule.passwordLabel get() = tagged(TestTags.passwordLabel)
    val ComposeTestRule.loginButton get() = tagged(TestTags.loginButton)
    val ComposeTestRule.forgotPassword get() = tagged(TestTags.forgotPassword)
}

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val shared: LoginSharedTests,
    val testScope: TestScope
) {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}