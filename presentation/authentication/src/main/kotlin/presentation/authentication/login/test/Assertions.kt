package presentation.authentication.login.test

import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertTextEquals
import keyless.presentation.common.R
import presentation.authentication.login.test.LoginNodes.emailInput
import presentation.authentication.login.test.LoginNodes.emailLabel
import presentation.authentication.login.test.LoginNodes.forgotPassword
import presentation.authentication.login.test.LoginNodes.loginButton
import presentation.authentication.login.test.LoginNodes.passwordInput
import presentation.authentication.login.test.LoginNodes.passwordLabel
import presentation.authentication.login.test.LoginNodes.screenTitle
import presentation.common.test.assertToast
import presentation.common.test.assertWebActivity
import presentation.test.tagged

internal fun TestsSuite.assertLoginScreenLayout() {
    assertScreenTitleLayout()
    assertEmailInputLayout()
    assertPasswordInputLayout()
    assertLoginButtonLayout()
    assertForgotPasswordLayout()
}

internal fun TestsSuite.assertLoginScreenFormValidations() {
    assertEmailInputInteraction(input = "")
    assertLoginButtonClick { compose.assertToast(activity.getString(R.string.please_enter_email)) }
    assertEmailInputInteraction(input = "username")
    advanceBackground(50000)
    compose.assertToast(null)

    assertPasswordInputInteraction(input = "")
    assertLoginButtonClick { compose.assertToast(activity.getString(R.string.please_enter_valid_password)) }
    assertPasswordInputInteraction(input = "password")
    advanceBackground(50000)
    compose.assertToast(null)
}

internal fun TestsSuite.assertForgotPassword() {
    assertForgotPasswordClick { shared.assertLaunchWebActivity() }
    compose.assertWebActivity()
}

internal fun TestsSuite.assertGuestNavToVerifyPhone() {
    assertEmailInputInteraction(input = "username")
    assertPasswordInputInteraction(input = "password")

    assertLoginButtonClick {
        compose.tagged(presentation.authentication.verifyphone.test.TestTags.screen).assertExists()
    }
}

internal fun TestsSuite.assertGuestNavToVerifyEmail() {
    assertEmailInputInteraction(input = "username")
    assertPasswordInputInteraction(input = "password")

    assertLoginButtonClick {
        compose.tagged(presentation.authentication.verifyemail.test.TestTags.screen).assertExists()
    }
}

internal fun TestsSuite.assertGuestNavToDashboard() {
    assertEmailInputInteraction(input = "username")
    assertPasswordInputInteraction(input = "password")

    assertLoginButtonClick { /* TODO */ }
}

internal fun TestsSuite.assertOwnerNavToDashboard() {
    assertEmailInputInteraction(input = "username")
    assertPasswordInputInteraction(input = "password")

    assertLoginButtonClick { /* TODO */ }
}

internal fun TestsSuite.assertScreenTitleLayout() {
    val node = compose.screenTitle

    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.sign_in_to_your_account))
}

internal fun TestsSuite.assertEmailInputLayout() {
    val node = compose.emailInput
    val label = compose.emailLabel

    node.assertIsDisplayed().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.username_email)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.username_email))
}

internal fun TestsSuite.assertEmailInputInteraction(input: String) {
    val node = compose.emailInput

    node.assertIsDisplayed().assertIsEnabled().performTextInput(input).assertTextEquals(input)
}

internal fun TestsSuite.assertPasswordInputLayout() {
    val node = compose.passwordInput
    val label = compose.passwordLabel

    node.assertIsDisplayed().assertIsEnabled()
    node.anyChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.password)) }
    label.assertIsDisplayed().assertTextEquals(activity.getString(R.string.password))
}

internal fun TestsSuite.assertPasswordInputInteraction(input: String) {
    val node = compose.passwordInput

    node.assertIsDisplayed().assertIsEnabled().performTextInput(input, true)
}

internal fun TestsSuite.assertLoginButtonLayout() {
    val node = compose.loginButton
    val text = node.getChild { it.assertIsDisplayed().assertTextEquals(activity.getString(R.string.login)) }

    node.assertIsDisplayed().assertIsEnabled()
    text.assertExists()
}

internal fun TestsSuite.assertLoginButtonClick(confirmation: () -> Unit) {
    val node = compose.loginButton

    node.assertIsDisplayed().assertIsEnabled().performClick(confirmation)
}

internal fun TestsSuite.assertForgotPasswordLayout() {
    val node = compose.forgotPassword

    node.assertIsDisplayed().assertTextEquals(activity.getString(R.string.forgot_password_q))
}

internal fun TestsSuite.assertForgotPasswordClick(confirmation: () -> Unit) {
    val node = compose.forgotPassword

    node.assertIsDisplayed().performClick(confirmation)
}