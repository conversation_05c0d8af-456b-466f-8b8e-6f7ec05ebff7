package presentation.authentication.login.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.LoginResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get

object Setups : KoinTest {
    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun setupGuestLoginPhone() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 0, userType = "Guest")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)
    }

    fun setupGuestLoginEmail() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 1, userType = "Guest")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)
    }

    fun setupGuestLoginDashboard() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 2, userType = "Guest")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)
    }

    fun setupOwnerLoginDashboard() {
        val url = "${ConfigValues.baseUrl}/user/login"
        val modified = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 2, userType = "owner")
        val response = MockHttpResponse(url = url, method = "POST", body = json.encodeToString(modified))
        client.setup(response)
    }
}