package presentation.authentication.login.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import keyless.data.keyless.ConfigValues
import keyless.presentation.common.R
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.login.domain.models.ScreenData
import presentation.authentication.login.domain.models.SideEffect
import presentation.common.feature.components.WebActivity
import presentation.common.feature.components.safeNavigate
import presentation.common.feature.theme.AppTheme

class LoginFragment : Fragment() {

    private val viewModel by viewModel<LoginAndroidViewModel>()
    private val logger: Logger by inject()
    private val status: StatusRepository by inject()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }
    }

    @Composable
    private fun Screen() {
        val scope = rememberCoroutineScope()
        val data = viewModel.screenData.collectAsStateWithLifecycle(ScreenData.empty)
        val state = remember { viewModel.stateHolder(data) }

        LoginScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onBackClick = { requireActivity().finish() },
            onCancelStatus = { scope.launch { status.removeStatus(it) } }
        )
    }

    private fun onSideEffect(sideEffect: SideEffect) {
        logger.log("Collected side effect: ${sideEffect.name}")
        when (sideEffect) {
            is SideEffect.NavToDashboard -> {
                if (DASHBOARD_ACTIVITY_CLASS == null) return
                val int = Intent(requireContext(), DASHBOARD_ACTIVITY_CLASS).putExtra("runSplash", false)
                requireActivity().startActivity(int)
                requireActivity().finishAffinity()
            }

            is SideEffect.NavToForgotPassword -> {
                val intent = Intent(requireContext(), WebActivity::class.java)
                    .putExtra("url", "${ConfigValues.forgotPasswordUrl}/")
                    .putExtra("title", getString(R.string.forgot_password))
                requireActivity().startActivity(intent)
            }

            is SideEffect.NavToVerifyPhone -> {
                findNavController().safeNavigate(
                    keyless.presentation.authentication.R.id.loginFragment,
                    keyless.presentation.authentication.R.id.action_login_to_verifyPhone
                )
            }

            is SideEffect.NavToVerifyEmail -> {
                findNavController().safeNavigate(
                    keyless.presentation.authentication.R.id.loginFragment,
                    keyless.presentation.authentication.R.id.action_login_to_verifyEmail
                )
            }
        }
    }

    companion object {
        var DASHBOARD_ACTIVITY_CLASS: Class<*>? = null
    }
}