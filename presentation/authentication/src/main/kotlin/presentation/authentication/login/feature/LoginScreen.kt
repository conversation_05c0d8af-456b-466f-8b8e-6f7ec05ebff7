package presentation.authentication.login.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import keyless.presentation.common.R
import presentation.authentication.login.domain.models.Event
import presentation.authentication.login.domain.models.ScreenData
import presentation.authentication.login.domain.models.ScreenEvent
import presentation.authentication.login.domain.models.UserAction
import presentation.authentication.login.test.TestTags
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppButton
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppLogoPage
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppPasswordField
import presentation.common.feature.components.AppTextField
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.StringState
import presentation.common.feature.theme.AppTheme

@Composable
internal fun LoginScreen(
    state: StateHolder,
    onEvent: (Event) -> Unit,
    onCancelStatus: (Status) -> Unit,
    onBackClick: () -> Unit
) {
    AppLogoPage(isBackEnabled = true, status = state.data.value.status, onBackPress = onBackClick, onCancelStatus = onCancelStatus) { ScreenLayout(state, onEvent) }
}

@Composable
private fun ScreenLayout(state: StateHolder, onEvent: (Event) -> Unit) {
    AppColumn(
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
    ) {
        AppPageTitleText(
            modifier = Modifier.testTag(TestTags.screenTitle),
            text = stringResource(R.string.sign_in_to_your_account)
        )

        LoginForm(modifier = Modifier.padding(top = DesignSystem.Padding.large), state = state, onEvent = onEvent)

        AppButton(
            modifier = Modifier
                .padding(top = DesignSystem.Padding.large)
                .testTag(TestTags.loginButton),
            text = stringResource(R.string.login),
            isEnabled = true,
            onClick = {
                onEvent(UserAction.Login(username = state.ui.email.get(), password = state.ui.password.text.get()))
            }
        )
    }
}

@Composable
private fun LoginForm(
    modifier: Modifier = Modifier,
    state: StateHolder,
    onEvent: (ScreenEvent) -> Unit
) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.large)
    ) {
        FormInputField(
            state = state.ui.email,
            hint = stringResource(R.string.username_email),
            hide = null,
            labelTag = TestTags.emailLabel,
            inputTag = TestTags.emailInput
        )

        CurrentPasswordView(
            text = state.ui.password.text,
            hide = state.ui.password.hide,
            hint = stringResource(R.string.password),
            onForgotPassword = { onEvent(ScreenEvent.NavToForgotPassword) }
        )
    }
}

@Composable
internal fun CurrentPasswordView(
    text: StringState,
    hide: BooleanState,
    hint: String,
    labelTag: String = TestTags.passwordLabel,
    inputTag: String = TestTags.passwordInput,
    forgotPasswordTag: String = TestTags.forgotPassword,
    onForgotPassword: () -> Unit
) {
    AppColumn(alignment = Alignment.End) {
        FormInputField(
            state = text,
            hint = hint,
            hide = hide,
            labelTag = labelTag,
            inputTag = inputTag
        )
        AppLabelText(
            modifier = Modifier.hiddenClickable { onForgotPassword() }.testTag(forgotPasswordTag),
            text = stringResource(R.string.forgot_password_q)
        )
    }
}


@Composable
private fun FormInputField(
    state: StringState,
    hint: String,
    hide: BooleanState? = null,
    labelTag: String,
    inputTag: String
) {
    AppColumn(arrangement = Arrangement.spacedBy(DesignSystem.Padding.small)) {
        AppBodyText(modifier = Modifier.testTag(labelTag), text = hint)

        if (hide != null) {
            AppPasswordField(
                modifier = Modifier.testTag(inputTag),
                value = state.value,
                onValueChange = { state.update(it.trim()) },
                hint = hint,
                hide = hide,
                design = DesignSystem.TextField.NormalTextField.copy(
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
            )
        } else {
            AppTextField(
                modifier = Modifier.testTag(inputTag),
                value = state.value,
                onValueChange = { state.update(it.trim()) },
                hint = hint,
                singleLine = true,
                design = DesignSystem.TextField.NormalTextField.copy(
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next)
                )
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    val state = remember { StateHolder(data = mutableStateOf(ScreenData.empty)) }
    AppTheme { LoginScreen(onBackClick = {}, state = state, onEvent = {}, onCancelStatus = {}) }
}