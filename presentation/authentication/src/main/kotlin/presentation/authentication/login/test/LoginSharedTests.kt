package presentation.authentication.login.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.login.loginInjection
import presentation.authentication.verifyemail.verifyEmailInjection
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity

internal abstract class LoginSharedTests : KoinTest {
    internal lateinit var suite: TestsSuite
    internal lateinit var scenario: ActivityScenario<Activity>

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                module { single<AuthenticationFlow> { AuthenticationFlow.LOGIN } },
                loginInjection, verifyPhoneInjection, verifyEmailInjection
            )
        )
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
    }

    @After
    fun tearDown() {
        Setups.clear()
        scenario.close()
    }

    @Test
    fun assertLoginScreenLayout() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertLoginScreenLayout()
    }

    @Test
    fun assertLoginScreenFormValidations() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertLoginScreenFormValidations()
    }

    @Test
    fun assertForgotPassword() = commonRunTest {
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertForgotPassword()
    }

    @Test
    fun assertGuestNavToVerifyPhone() = commonRunTest {
        Setups.setupGuestLoginPhone()
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertGuestNavToVerifyPhone()
    }

    @Test
    fun assertGuestNavToVerifyEmail() = commonRunTest {
        Setups.setupGuestLoginEmail()
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertGuestNavToVerifyEmail()
    }

    @Test
    fun assertGuestNavToDashboard() = commonRunTest {
        Setups.setupGuestLoginDashboard()
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertGuestNavToDashboard()
    }

    @Test
    fun assertOwnerNavToDashboard() = commonRunTest {
        Setups.setupOwnerLoginDashboard()
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this@LoginSharedTests, this)
        suite.assertOwnerNavToDashboard()
    }

    open fun assertLaunchWebActivity(): Unit = Unit
}