package presentation.authentication.login.domain.models

import data.common.preferences.Preferences
import data.keyless.authentication.models.LoginRequest
import presentation.common.domain.models.Device

internal fun UserAction.Login.toRequest() = LoginRequest(
    user = username,
    password = password,
    uid = Preferences.uuid.get(),
    deviceToken = Preferences.deviceFCMToken.get(),
    deviceModel = Device.getDeviceNameApi(),
    method = "email"
)