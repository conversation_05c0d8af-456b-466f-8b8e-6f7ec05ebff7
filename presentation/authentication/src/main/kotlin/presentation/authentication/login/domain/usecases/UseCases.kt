package presentation.authentication.login.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.keyless.authentication.AuthenticationRepository
import presentation.authentication.login.domain.models.SideEffect
import presentation.common.domain.repositories.SideEffectsRepository

internal class UseCases(
    private val authentication: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    val login = LoginUseCase(authentication, logger, status, sideEffects)
}