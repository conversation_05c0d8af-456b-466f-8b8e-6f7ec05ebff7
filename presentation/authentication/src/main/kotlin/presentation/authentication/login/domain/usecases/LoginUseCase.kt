package presentation.authentication.login.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import data.common.preferences.Preferences
import data.common.preferences.UserType
import data.keyless.authentication.AuthenticationRepository
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.ProfileStatus
import keyless.presentation.common.R
import presentation.authentication.login.domain.models.SideEffect
import presentation.authentication.login.domain.models.UserAction
import presentation.authentication.login.domain.models.toRequest
import presentation.common.domain.repositories.SideEffectsRepository

internal class LoginUseCase(
    private val repository: AuthenticationRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val sideEffects: SideEffectsRepository<SideEffect>
) {

    suspend fun execute(event: UserAction.Login) = logger.async {
        if (!validate(event)) return@async

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { login(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun validate(event: UserAction.Login): Boolean = logger.async {
        if (event.username.isBlank()) {
            status.info(Message(R.string.please_enter_email))
            return@async false
        }

        if (event.password.isBlank()) {
            status.info(Message(resId = R.string.please_enter_valid_password))
            return@async false
        }

        return@async true
    }

    private suspend fun login(event: UserAction.Login) = logger.async {
        val response = repository.login(event.toRequest())

        if (!response.success) handleFailResponse(response) else handleSuccessResponse(response)
    }

    private suspend fun handleFailResponse(response: LoginResponse) = logger.async {
        status.fail(KError.Info(Message.fromString(response.message)))
    }

    private suspend fun handleSuccessResponse(response: LoginResponse): Unit = logger.async {
        savePreferences(response)

        when {
            Preferences.userRole.get() != UserType.Guest.toString() || response.status == ProfileStatus.COMPLETE -> {
                Preferences.isLoggedIn.set(true)
                sideEffects.emit(SideEffect.NavToDashboard)
            }

            response.status == ProfileStatus.VERIFY_PHONE -> {
                sideEffects.emit(SideEffect.NavToVerifyPhone)
            }

            response.status == ProfileStatus.VERIFY_EMAIL -> {
                sideEffects.emit(SideEffect.NavToVerifyEmail)
            }
        }
    }

    private fun savePreferences(response: LoginResponse) = logger.log {
        Preferences.authenticationToken.set("Bearer " + response.token)
        Preferences.userId.set(response.userId)
        Preferences.userRole.set(response.userType)

        Preferences.userFullName.set((response.firstName + " " + response.lastName).trim())
        Preferences.role.set(response.role)
        Preferences.userEmail.set(response.email)
        Preferences.firstName.set(response.firstName)
    }
}