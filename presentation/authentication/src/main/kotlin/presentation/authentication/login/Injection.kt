package presentation.authentication.login

import core.common.status.StatusRepository
import kotlinx.coroutines.flow.combine
import org.koin.core.component.getScopeId
import org.koin.core.module.dsl.viewModel
import org.koin.dsl.module
import presentation.authentication.login.domain.ViewModel
import presentation.authentication.login.domain.models.ScreenData
import presentation.authentication.login.domain.models.SideEffect
import presentation.authentication.login.domain.usecases.UseCases
import presentation.authentication.login.feature.LoginAndroidViewModel
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository

internal data object LoginInjectionScope

val loginInjection = module {
    scope<LoginInjectionScope> {
        scoped {
            ScreenDataRepository(initial = ScreenData.empty) {
                combine(it, get<StatusRepository>().stream) { data, status -> data.copy(status = status) }
            }
        }
        scoped { SideEffectsRepository<SideEffect>() }
        scoped { UseCases(authentication = get(), logger = get(), status = get(), sideEffects = get()) }
        scoped { ViewModel(useCases = get(), data = get(), sideEffects = get()) }
    }

    viewModel {
        val scope = getKoin().getOrCreateScope<LoginInjectionScope>(LoginInjectionScope.getScopeId())
        LoginAndroidViewModel(
            domain = scope.get(),
            dispatchers = get(),
            errorHandler = get(),
            onClear = { scope.close() }
        )
    }
}