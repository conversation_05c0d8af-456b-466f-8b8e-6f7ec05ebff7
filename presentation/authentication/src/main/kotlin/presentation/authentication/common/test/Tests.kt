package presentation.authentication.common.test

import android.app.Activity
import androidx.compose.ui.test.junit4.ComposeTestRule
import core.common.coroutines.AbstractCoroutineDispatcher
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.TestScope
import org.koin.test.KoinTest

internal class TestsSuite(
    val compose: ComposeTestRule,
    val activity: Activity,
    val coroutines: AbstractCoroutineDispatcher,
    val testScope: TestScope
) : KoinTest {
    fun advanceBackground(millis: Long) {
        (coroutines.backgroundWork as TestDispatcher).scheduler.advanceTimeBy(millis)
    }
}
