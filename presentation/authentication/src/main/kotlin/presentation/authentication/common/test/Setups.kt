package presentation.authentication.common.test

import core.common.serialization.json
import core.http.client.HttpClient
import core.http.client.test.MockKtorHttpClient
import core.http.client.test.models.MockHttpResponse
import data.keyless.authentication.models.LoginResponse
import data.keyless.authentication.models.SendOtpResponse
import data.keyless.authentication.models.SignupResponse
import data.test.MockResponses
import keyless.presentation.authentication.ConfigValues
import org.koin.test.KoinTest
import org.koin.test.get

internal object Setups : KoinTest {
    val client get() = get<HttpClient>() as MockKtorHttpClient

    fun clear() {
        client.clear()
    }

    fun TestsSuite.setupLoginSuccess(): LoginResponse {
        val url = "${ConfigValues.baseUrl}/user/login"
        val response = MockResponses.postUserLogin.value<LoginResponse>().copy(profileStatus = 0, userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupSignupSuccess(): SignupResponse {
        val url = "${ConfigValues.baseUrl}/user/username"
        val response = MockResponses.postSignup.value<SignupResponse>().copy(userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupVerifyPhoneOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verifyLoginOTP"
        val response = MockResponses.postVerifyOtp.value<SendOtpResponse>().copy(profileStatus = 1, userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupVerifyEmailOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/verify_emailotp"
        val response = MockResponses.postVerifyEmailOtp.value<SendOtpResponse>().copy(profileStatus = 2, userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupSendPhoneOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/sendotp_mobile"
        val response = MockResponses.postUserSendOtpMobile.value<SendOtpResponse>().copy(userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }

    fun TestsSuite.setupSendEmailOtpSuccess(): SendOtpResponse {
        val url = "${ConfigValues.baseUrl}/user/sendotp_email"
        val response = MockResponses.postUserSendOtpEmail.value<SendOtpResponse>().copy(userType = "Guest")
        client.setup(MockHttpResponse(url = url, method = "POST", body = json.encodeToString(response)))
        return response
    }
}
