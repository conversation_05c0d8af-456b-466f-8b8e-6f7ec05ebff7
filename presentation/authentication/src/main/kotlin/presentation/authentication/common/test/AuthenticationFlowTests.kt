package presentation.authentication.common.test

import android.app.Activity
import androidx.compose.ui.test.junit4.createEmptyComposeRule
import androidx.test.core.app.ActivityScenario
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.authentication.changepassword.changePasswordInjection
import presentation.authentication.common.test.Setups.setupLoginSuccess
import presentation.authentication.common.test.Setups.setupSignupSuccess
import presentation.authentication.common.test.Setups.setupVerifyEmailOtpSuccess
import presentation.authentication.common.test.Setups.setupVerifyPhoneOtpSuccess
import presentation.authentication.login.loginInjection
import presentation.authentication.signup.signupInjection
import presentation.authentication.verifyemail.verifyEmailInjection
import presentation.authentication.verifyotp.verifyOtpInjection
import presentation.authentication.verifyphone.verifyPhoneInjection
import presentation.test.commonRunTest
import presentation.test.withActivity
import kotlin.time.Duration.Companion.seconds

abstract class AuthenticationFlowTests : KoinTest {
    private lateinit var suite: TestsSuite
    private lateinit var scenario: ActivityScenario<Activity>

    @get:Rule
    val compose = createEmptyComposeRule()

    @Before
    fun setup() {
        loadKoinModules(
            listOf(
                loginInjection,
                signupInjection,
                verifyPhoneInjection,
                verifyEmailInjection,
                verifyOtpInjection,
                changePasswordInjection
            )
        )
    }

    @After
    fun tearDown() {
        Setups.clear()
        if (::scenario.isInitialized) scenario.close()
    }

    @Test
    fun testGuestLoginToVerifyPhoneToVerifyEmailFlow() = commonRunTest(timeout = 60.seconds) {
        loadKoinModules(module { single<AuthenticationFlow> { AuthenticationFlow.LOGIN } })
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this)

        // Setup mock responses
        suite.setupLoginSuccess()
        suite.setupVerifyPhoneOtpSuccess()
        suite.setupVerifyEmailOtpSuccess()

        // Test login flow
        suite.assertLoginFlow()

        // Test verify phone flow
        suite.assertVerifyPhoneFlow()

        // Test verify phone OTP flow
        suite.assertVerifyPhoneOtpFlow()

        // Test verify email flow
        suite.assertVerifyEmailFlow()

        // Test verify email OTP flow
        suite.assertVerifyEmailOtpFlow()
    }

    @Test
    fun testGuestSignupToVerifyPhoneToVerifyEmailFlow() = commonRunTest(timeout = 60.seconds) {
        loadKoinModules(module { single<AuthenticationFlow> { AuthenticationFlow.SIGNUP } })
        scenario = ActivityScenario.launch(AuthenticationActivity::class.java as Class<Activity>)
        suite = TestsSuite(compose, scenario.withActivity { this }, get(), this)

        // Setup mock responses
        suite.setupSignupSuccess()
        suite.setupVerifyPhoneOtpSuccess()
        suite.setupVerifyEmailOtpSuccess()

        // Test signup flow
        suite.assertSignupFlow()

        // Test verify phone flow
        suite.assertVerifyPhoneFlow()

        // Test verify phone OTP flow
        suite.assertVerifyPhoneOtpFlow()


        // Test verify email flow
        suite.assertVerifyEmailFlow()

        // Test verify email OTP flow
        suite.assertVerifyEmailOtpFlow()
    }
}
