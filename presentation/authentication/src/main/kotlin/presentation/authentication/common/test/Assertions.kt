package presentation.authentication.common.test

import androidx.compose.ui.test.performTextInput
import presentation.authentication.common.test.Setups.setupSendEmailOtpSuccess
import presentation.authentication.common.test.Setups.setupSendPhoneOtpSuccess
import presentation.authentication.login.test.LoginNodes
import presentation.authentication.signup.test.SignupNodes
import presentation.authentication.verifyemail.test.VerifyEmailNodes
import presentation.authentication.verifyotp.test.VerifyOtpNodes
import presentation.authentication.verifyphone.test.VerifyPhoneNodes

internal fun TestsSuite.assertLoginFlow() = with (LoginNodes) {
    // Wait for login screen to appear
    compose.screenTitle.assertIsDisplayed()

    // Enter email and password
    compose.emailInput.performTextInput("<EMAIL>")
    compose.passwordInput.performTextInput("Password123", true)

    // Click login button
    compose.loginButton.performClick { with(VerifyPhoneNodes) { compose.screen.assertIsDisplayed() } }
}

internal fun TestsSuite.assertSignupFlow() = with(SignupNodes) {
    // Wait for signup screen to appear
    compose.screenTitle.assertIsDisplayed()

    // Enter signup details
    compose.firstNameInput.performTextInput("<PERSON>")
    compose.lastNameInput.performTextInput("Doe")
    compose.usernameInput.performTextInput("johndoe")
    compose.passwordInput.performTextInput("Password123", true)
    compose.confirmPasswordInput.performTextInput("Password123", true)

    // Click signup button
    compose.signupButton.displayedOrScroll().performClick { with(VerifyPhoneNodes) { compose.screen.assertIsDisplayed() } }
}

internal fun TestsSuite.assertVerifyPhoneFlow() = with(VerifyPhoneNodes) {
    // Wait for verify phone screen to appear
    compose.screen.assertIsDisplayed()

    // Setup mock response for sending OTP
    setupSendPhoneOtpSuccess()

    // Enter phone details
    compose.mobileNumber.performTextInput("501234567")

    // Click next button
    compose.smsButton.displayedOrScroll().performClick { with(VerifyOtpNodes) { compose.screen.assertIsDisplayed() } }
}

internal fun TestsSuite.assertVerifyPhoneOtpFlow() = with(VerifyOtpNodes) {
    advanceBackground(61_000)
    // Wait for verify OTP screen to appear
    compose.screen.assertIsDisplayed()

    // Enter OTP
    compose.otpInput.anyChild { it.performTextInput("1234") }

    // Click verify button
    compose.verifyButton.displayedOrScroll().performClick { with(VerifyEmailNodes) { compose.screen.assertIsDisplayed() } }
}

internal fun TestsSuite.assertVerifyEmailFlow() = with(VerifyEmailNodes) {
    // Wait for verify email screen to appear
    compose.screen.assertIsDisplayed()

    // Setup mock response for sending email OTP
    setupSendEmailOtpSuccess()

    // Enter email
    compose.emailInput.performTextInput("<EMAIL>")

    // Click send OTP button
    compose.verifyButton.displayedOrScroll().performClick { with(VerifyOtpNodes) { compose.screen.assertIsDisplayed() } }
}

internal fun TestsSuite.assertVerifyEmailOtpFlow() = with(VerifyOtpNodes) {
    advanceBackground(61_000)
    // Wait for verify OTP screen to appear
    compose.screen.assertIsDisplayed()

    // Enter OTP
    compose.otpInput.anyChild { it.performTextInput("1234") }

    // Click verify button
    compose.verifyButton.displayedOrScroll().performClick {

    }

    // Clear toast
//    advanceBackground(60_000)
//    compose.assertToast(null)
}
