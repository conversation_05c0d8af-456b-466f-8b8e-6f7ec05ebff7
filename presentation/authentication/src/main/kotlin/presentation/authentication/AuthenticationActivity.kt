package presentation.authentication

import android.os.Build
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.insets.ColorProtection
import androidx.core.view.insets.ProtectionLayout
import androidx.navigation.fragment.NavHostFragment
import keyless.presentation.authentication.R
import org.koin.android.ext.android.get

class AuthenticationActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initView()
        setupNavGraph()
    }

    private fun initView() {
        enableEdgeToEdge()
        setContentView(R.layout.activity_authentication)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars() or WindowInsetsCompat.Type.ime())
            v.setPadding(
                /* left = */ insets.left,
                /* top = */ insets.top,
                /* right = */ insets.right,
                /* bottom = */ insets.bottom
            )

            WindowInsetsCompat.CONSUMED
        }
        WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = false

        val protection = ColorProtection(WindowInsetsCompat.Side.TOP, getColor(keyless.presentation.common.R.color.colorAccent))
        findViewById<ProtectionLayout>(R.id.protection).setProtections(listOf(protection))
    }

    private fun setupNavGraph() {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.auth_nav_host_fragment) as NavHostFragment
        val inflater = navHostFragment.navController.navInflater
        val graph = inflater.inflate(R.navigation.auth_nav_graph)
        graph.setStartDestination(getStartDestination())

        val navController = navHostFragment.navController
        navController.setGraph(graph, intent.extras)
    }

    private fun getStartDestination(): Int {
        val flow = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getSerializableExtra(AUTHENTICATION_FLOW_KEY, AuthenticationFlow::class.java)
        } else {
            intent.getSerializableExtra(AUTHENTICATION_FLOW_KEY) as? AuthenticationFlow
        }
            ?: runCatching { get<AuthenticationFlow>() }.getOrElse { error("Authentication flow is not provided") } // Injected in tests

        return when (flow) {
            AuthenticationFlow.LOGIN -> R.id.loginFragment
            AuthenticationFlow.SIGNUP -> R.id.signupFragment
            AuthenticationFlow.UPDATE_PASSWORD -> R.id.changePasswordFragment
        }
    }

    companion object {
        val AUTHENTICATION_FLOW_KEY = this::class.java.name + ".authentication_flow"
    }
}

enum class AuthenticationFlow {
    LOGIN, SIGNUP, UPDATE_PASSWORD
}