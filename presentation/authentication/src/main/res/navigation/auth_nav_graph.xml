<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/auth_nav_graph">

    <fragment
            android:id="@+id/loginFragment"
            android:name="presentation.authentication.login.feature.LoginFragment"
            android:label="LoginFragment" >
        <action
                android:id="@+id/action_login_to_verifyPhone"
                app:destination="@id/verifyPhoneFragment" />
        <action
                android:id="@+id/action_login_to_verifyEmail"
                app:destination="@id/verifyEmailFragment" />
        <action
                android:id="@+id/action_login_to_signup"
                app:destination="@id/signupFragment" />
        <action
                android:id="@+id/action_login_to_changePassword"
                app:destination="@id/changePasswordFragment" />
    </fragment>
    <fragment
            android:id="@+id/verifyPhoneFragment"
            android:name="presentation.authentication.verifyphone.feature.VerifyPhoneFragment"
            android:label="VerifyPhoneFragment" >
        <action
                android:id="@+id/action_verifyPhone_to_verifyOtp"
                app:destination="@id/verifyOtpFragment" />
    </fragment>
    <fragment
            android:id="@+id/verifyOtpFragment"
            android:name="presentation.authentication.verifyotp.feature.VerifyOtpFragment"
            android:label="VerifyOtpFragment" >
        <action
                android:id="@+id/action_verifyOtp_to_verifyEmail"
                app:destination="@id/verifyEmailFragment" />
    </fragment>
    <fragment
            android:id="@+id/verifyEmailFragment"
            android:name="presentation.authentication.verifyemail.feature.VerifyEmailFragment"
            android:label="VerifyEmailFragment" >
        <action
                android:id="@+id/action_verifyEmail_to_verifyOtp"
                app:destination="@id/verifyOtpFragment" />
    </fragment>
    <fragment
            android:id="@+id/signupFragment"
            android:name="presentation.authentication.signup.feature.SignupFragment"
            android:label="SignupFragment" >
        <action
                android:id="@+id/action_signup_to_verifyPhone"
                app:destination="@id/verifyPhoneFragment" />
    </fragment>
    <fragment
            android:id="@+id/changePasswordFragment"
            android:name="presentation.authentication.changepassword.feature.ChangePasswordFragment"
            android:label="ChangePasswordFragment" />
</navigation>