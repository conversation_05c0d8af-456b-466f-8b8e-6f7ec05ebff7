plugins {
    id("keyless.android.feature")
    id("keyless.android.compose")
    id("config.values")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-monitoring-common"))
    implementation(project(":data-common"))
    implementation(project(":data-keyless"))
    implementation(project(":domain-common"))
    implementation(project(":presentation-common"))

    implementation(keyless.ccp)
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}


android {
    defaultConfig {
        testInstrumentationRunner = "presentation.test.TestRunner"
    }

    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            animationsDisabled = true
            all { it.jvmArgs("-noverify") }
        }
        animationsDisabled = true
    }
}

dependencies {
    implementation(project(":data-test"))
    implementation(project(":presentation-test"))

    implementation(keyless.kotlinx.serialization.json)

    testImplementation("org.robolectric:robolectric:4.14.1")
    debugImplementation("androidx.compose.ui:ui-test-manifest:1.6.4")
}