plugins {
    id("keyless.android.feature")
    id("keyless.android.compose")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":data-common"))
    implementation(project(":core-http-client-common"))
    implementation(project(":core-monitoring-common"))

    implementation("com.github.vedraj360:DesignerToast:0.1.3")

    implementation(keyless.retrofit)

    implementation(keyless.androidx.compose.ui.test.junit4)
}