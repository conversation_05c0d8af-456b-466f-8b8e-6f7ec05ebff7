package presentation.common.test

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.assertHasClickAction
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsEnabled
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.junit4.ComposeTestRule
import androidx.compose.ui.test.onChildAt
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.test.performClick

fun ComposeTestRule.assertBackButtonLayout(isBackEnabled: Boolean) {
    val back = onNode(hasTestTag(Icons.AutoMirrored.Default.KeyboardArrowLeft.name), true)

    if (isBackEnabled) {
        back.assertIsDisplayed().assertIsEnabled()
    } else {
        back.assertDoesNotExist()
    }
}

fun ComposeTestRule.assertBackButtonClick(timeoutMillis: Long = 5_000, confirmation: () -> Unit) {
    val back = onNode(hasTestTag(Icons.AutoMirrored.Default.KeyboardArrowLeft.name), true)

    wait(timeoutMillis) {
        back.assertIsDisplayed().performClick()
        confirmation()
    }
}

fun ComposeTestRule.assertToast(message: String?) {
    if (message != null) {
        onNodeWithText(message, useUnmergedTree = true).assertExists()
    } else {
        val node = onNode(hasTestTag(CommonTestTags.statusContainer), true)
        node.assertDoesNotExist()
    }
}

fun ComposeTestRule.assertDialogReturnButton(message: String): SemanticsNodeInteraction {
    val node = onNode(hasTestTag(CommonTestTags.appDialog(message)), useUnmergedTree = true)
    val text = node.getChild { it.assertTextEquals(message) }
    val button = node.getChild { it.assertHasClickAction() }

    node.assertIsDisplayed()
    text.assertIsDisplayed()
    button.assertIsDisplayed().assertIsEnabled()
    return button
}


fun ComposeTestRule.assertWebActivity() {
    val node = onNode(hasTestTag(CommonTestTags.webActivity), useUnmergedTree = true)

    node.assertExists()

    // TODO node.anyChild { it.assertTextEquals(activity.getString(R.string.forgot_password)) }
}

private fun SemanticsNodeInteraction.getChild(block: (SemanticsNodeInteraction) -> Unit): SemanticsNodeInteraction {
    for (child in flattenChildren()) {
        val result = runCatching { block(child) }
        if (result.isSuccess) return child
    }

    throw AssertionError("Assert failed: none of the children matched the assertion")
}

private fun SemanticsNodeInteraction.flattenChildren(): List<SemanticsNodeInteraction> {
    val list = mutableListOf<SemanticsNodeInteraction>()
    var counter = 0

    while (true) {
        val child = getChildAt(counter) ?: break
        list.add(child)
        child.flattenChildren().forEach { list.add(it) }
        counter++
    }

    return list
}

private fun SemanticsNodeInteraction.getChildAt(index: Int): SemanticsNodeInteraction? {
    return runCatching { onChildAt(index).assertExists() }.getOrNull()
}

private fun ComposeTestRule.wait(timeoutMillis: Long, block: () -> Unit) {
    var internalError: Throwable? = null
    val job = { runCatching { block() }.onFailure { internalError = it }.isSuccess }
    runCatching { waitUntil(timeoutMillis) { job() } }.onFailure { throw it.initCause(internalError) }
}