package presentation.common.domain.repositories

import android.content.Context

import android.util.Log
import core.common.error.isAny
import core.common.error.toFatal
import core.common.platform.Platform
import core.http.client.HttpError
import core.monitoring.common.repository.Logger
import keyless.presentation.common.R
import org.json.JSONObject
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import retrofit2.HttpException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLHandshakeException

object ErrorMessageHandler : KoinComponent {

    private val logger: Logger by inject()

    fun handleException(exception: Exception): String {
        exception.printStackTrace()

        return when {
            shouldHandleOld(exception) -> handleOld(exception)
            exception is HttpError -> handleNew(exception)
            else -> exception.javaClass.name ?: getKoin().get<Context>().getString(R.string.something_went_wrong)
        }
    }

    private fun shouldHandleOld(exception: Throwable): Boolean {
        return exception.isAny<HttpException>() ||
                exception.isAny<SocketTimeoutException>() ||
                exception.isAny<UnknownHostException>() ||
                exception.isAny<ConnectException>() ||
                exception.isAny<SSLHandshakeException>()
    }

    private fun handleNew(error: HttpError): String {
        if (shouldHandleOld(error)) {
            return handleOld(error)
        }
        logger.log(error.displayMessage())
        logger.error(error)
        return error.displayMessage()
    }

    private fun handleOld(exception: Exception): String {
        exception.printStackTrace()
        return when  {
            exception is HttpException -> handleHttpException(exception)
            exception.isAny<SocketTimeoutException>() -> {
                logger.log(exception.message ?: "Socket timeout exception")
                getKoin().get<Context>().getString(R.string.socket_timeout_exception)
            }

            exception.isAny<UnknownHostException>() -> {
                logger.log(exception.message ?: "Unkown host exception")
                getKoin().get<Context>().getString(R.string.you_are_in_offline)
            }

            exception.isAny<ConnectException>() -> {
                logger.log(exception.message ?: "Connect execption")
                logger.error(exception.toFatal(Platform.executeLocation(), mapOf()))
                getKoin().get<Context>().getString(R.string.server_error)
            }
            exception.isAny<SSLHandshakeException>() && exception.message?.contains("chain validation failed", true) == true -> {
                "The device's date and time are incorrect.\n" +
                        "Please update them in your phone's settings."
            }
            else -> {
                logger.log(exception.message ?: "Error happened")
                getKoin().get<Context>().getString(R.string.something_went_wrong)
            }
        }
    }

    private fun handleHttpException(exception: HttpException): String {
        try {
            var errorMessage = ""

            exception.response().let {
                it?.errorBody()?.let { body ->
                    val readText = body.charStream().readText()
                    Log.e("//", "findJsonObject: " + readText)

                    try {
                        val error = JSONObject(readText)
                        val optJSONArray = error.optJSONArray("message")
                        if (optJSONArray != null) {
                            for (i in 0 until optJSONArray.length()) {
                                var errorKey = ""

                                optJSONArray.optJSONObject(i).keys().forEach {
                                    errorKey = it
                                    return@forEach
                                }
                                errorMessage = optJSONArray.optJSONObject(i).optString(errorKey)
                            }
                        } else {
                            val optMsg = error.optString("message")
                            errorMessage = optMsg
                        }

                        return errorMessage
                    } catch (e: java.lang.Exception) {
                        return getKoin().get<Context>().getString(R.string.something_went_wrong)

                    }
                }
            }
            logger.log(errorMessage)
            logger.error(exception.toFatal(Platform.executeLocation(), mapOf()))
            return errorMessage
        } catch (e: Exception) {
            return getKoin().get<Context>().getString(R.string.something_went_wrong)

        }
    }

    private fun HttpError.displayMessage(): String {
        return when  {
            isAny<SocketTimeoutException>() -> getKoin().get<Context>().getString(R.string.socket_timeout_exception)
            isAny<UnknownHostException>() -> getKoin().get<Context>().getString(R.string.you_are_in_offline)
            isAny<ConnectException>() -> getKoin().get<Context>().getString(R.string.server_error)
            isAny<SSLHandshakeException>() && message?.contains("chain validation failed", true) == true -> {
                "The device's date and time are incorrect.\n" +
                        "Please update them in your phone's settings."
            }
            else -> this.message ?: getKoin().get<Context>().getString(R.string.something_went_wrong)
        }
    }
}