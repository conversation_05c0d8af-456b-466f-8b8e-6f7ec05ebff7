package presentation.common.feature.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import keyless.presentation.common.R

@Composable
fun NoLocksIllustration(modifier: Modifier = Modifier) {
    AppColumn(
        modifier = modifier,
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium, Alignment.CenterVertically),
        alignment = Alignment.CenterHorizontally
    ) {
        AppImage(res = R.drawable.iv_no_locks)

        AppBodyText(text = stringResource(R.string.no_locks_assigned))
    }
}