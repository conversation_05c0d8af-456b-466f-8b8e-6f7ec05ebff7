package presentation.common.feature.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp

@Composable
fun AppCarousel(
    modifier: Modifier = Modifier,
    count: Int,
    contentPadding: PaddingValues,
    gridSpacing: Dp,
    state: PagerState = rememberPagerState(pageCount = { count }),
    content: @Composable PagerScope.(page: Int) -> Unit
) {
    HorizontalPager(
        modifier = modifier,
        state = state,
        contentPadding = contentPadding,
        pageSpacing = gridSpacing,
        verticalAlignment = Alignment.Top,
        beyondViewportPageCount = 0,
        pageContent = { page -> content(page) }
    )
}

@Composable
fun AppCarouselIndicator(
    modifier: Modifier = Modifier,
    count: Int,
    currentPage: Int,
    color: Color = MaterialTheme.colorScheme.background,
) {
    AppRow(modifier = modifier, arrangement = Arrangement.spacedBy(DesignSystem.Padding.small * 2)) {
        for (page in 0 until count) IndicatorDot(color, currentPage == page)
    }
}

@Composable
private fun IndicatorDot(
    color: Color,
    selected: Boolean
) {
    Box(
        Modifier
            .height(DesignSystem.Padding.small * 2)
            .width(DesignSystem.Padding.small * 2 * if (selected) 3f else 1f)
            .clip(CircleShape)
            .background(color.copy(alpha = if (selected) 1f else 0.5f))
    )
}