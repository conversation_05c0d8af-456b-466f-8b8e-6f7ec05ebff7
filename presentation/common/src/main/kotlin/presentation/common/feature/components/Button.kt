package presentation.common.feature.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.semantics

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    text: String,
    design: ButtonDesign.Normal = DesignSystem.Button.Normal,
    isEnabled: Boolean = true,
    fillWidth: Boolean = true,
    onClick: () -> Unit
) {
    val semantics: Modifier.() -> Modifier = {
        semantics {
            this.backgroundColor = design.backgroundColor
            this.contentColor = design.contentColor
        }
    }
    Box(
        modifier = (if (fillWidth) modifier.fillMaxWidth() else modifier).semantics(),
        contentAlignment = Alignment.Center
    ) {
        Button(
            modifier = (if (fillWidth) Modifier.fillMaxWidth(0.8f) else Modifier)
                .heightIn(design.minHeight)
                .semantics(),
            onClick = onClick,
            enabled = isEnabled,
            shape = design.shape,
            colors = ButtonDefaults.buttonColors(
                contentColor = design.contentColor,
                containerColor = design.backgroundColor,
                disabledContainerColor = design.backgroundColor.copy(alpha = 0.7f),
                disabledContentColor = design.contentColor
            ),
            contentPadding = PaddingValues(design.padding)
        ) {
            AppBodyText(text = text)
        }
    }
}

@Composable
fun AppTextButton(
    modifier: Modifier = Modifier,
    text: String,
    isEnabled: Boolean = true,
    fillWidth: Boolean = true,
    design: TextButtonDesign.Normal = DesignSystem.TextButton.Normal,
    onClick: () -> Unit
) {
    Box(modifier = if (fillWidth) modifier.fillMaxWidth() else modifier, contentAlignment = Alignment.Center) {
        TextButton(
            modifier = (if (fillWidth) Modifier.fillMaxWidth(0.8f) else Modifier).heightIn(design.minHeight),
            onClick = onClick,
            enabled = isEnabled,
            shape = design.shape,
            colors = ButtonDefaults.textButtonColors(
                contentColor = design.contentColor,
                disabledContentColor = design.contentColor.copy(alpha = 0.7f)
            ),
            contentPadding = PaddingValues(design.padding)
        ) {
            AppBodyText(text = text)
        }
    }
}