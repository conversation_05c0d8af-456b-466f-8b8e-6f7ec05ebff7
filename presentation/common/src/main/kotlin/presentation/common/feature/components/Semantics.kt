package presentation.common.feature.components

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.SemanticsPropertyKey
import androidx.compose.ui.semantics.SemanticsPropertyReceiver


val TextColorSemanticProperty = SemanticsPropertyKey<Color>("textColor")
var SemanticsPropertyReceiver.textColor by TextColorSemanticProperty

val BackgroundColorSemanticProperty = SemanticsPropertyKey<Color>("backgroundColor")
var SemanticsPropertyReceiver.backgroundColor by BackgroundColorSemanticProperty

val ContentColorSemanticProperty = SemanticsPropertyKey<Color>("contentColor")
var SemanticsPropertyReceiver.contentColor by ContentColorSemanticProperty

val IconTintSemanticProperty = SemanticsPropertyKey<Color>("iconTint")
var SemanticsPropertyReceiver.iconTint by IconTintSemanticProperty


val DrawableResSemanticProperty = SemanticsPropertyKey<Int>("drawableRes")
var SemanticsPropertyReceiver.drawableRes by DrawableResSemanticProperty