package presentation.common.feature.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalContentColor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics

@Composable
fun AppIcon(
    modifier: Modifier = Modifier,
    @DrawableRes res: Int,
    tint: Color = LocalContentColor.current,
    description: String? = null
) {
    Icon(modifier = modifier, painter = painterResource(res), tint = tint, contentDescription = description)
}

@Composable
fun AppIcon(
    modifier: Modifier = Modifier,
    vector: ImageVector,
    tint: Color = LocalContentColor.current,
    description: String? = null
) {
    Icon(modifier = modifier, painter = rememberVectorPainter(vector), tint = tint, contentDescription = description)
}

@Composable
fun AppImage(
    modifier: Modifier = Modifier,
    @DrawableRes res: Int,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Fit
) {
    Image(
        modifier = modifier.semantics {
            this.drawableRes = res
        },
        painter = painterResource(res),
        contentDescription = contentDescription,
        contentScale = contentScale
    )
}