package presentation.common.feature.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.unit.dp

@Composable
fun BackButton(
    modifier: Modifier = Modifier,
    isEnabled: Boolean,
    onClick: () -> Unit
) {
    val icon = remember(LocalLayoutDirection.current) {
        Icons.AutoMirrored.Default.KeyboardArrowLeft
    }
    IconButton(
        modifier = modifier,
        enabled = isEnabled,
        onClick = onClick
    ) {
        Icon(
            modifier = Modifier.size(36.dp).testTag(icon.name),
            imageVector = icon,
            contentDescription = "",
            tint = MaterialTheme.colorScheme.onPrimary
        )
    }
}

@Composable
fun AppIconButton(
    modifier: Modifier = Modifier,
    @DrawableRes res: Int,
    description: String? = null,
    isEnabled: Boolean = true,
    onClick: () -> Unit
) {
    IconButton(modifier = modifier, enabled = isEnabled, onClick = onClick) {
        AppIcon(res = res, description = description)
    }
}

@Composable
fun AppIconButton(
    modifier: Modifier = Modifier,
    vector: ImageVector,
    description: String? = null,
    isEnabled: Boolean = true,
    onClick: () -> Unit
) {
    IconButton(modifier = modifier, enabled = isEnabled, onClick = onClick) {
        AppIcon(vector = vector, description = description)
    }
}