package presentation.common.feature.components

import android.app.Activity
import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import core.common.status.Status
import core.common.status.StatusRepository
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.get
import presentation.common.domain.repositories.ErrorMessageHandler

interface StatusListener {
    fun registerStatusListener(lifecycleOwner: LifecycleOwner)
}

class StatusListenerImpl : StatusListener, LifecycleEventObserver, KoinComponent {

    private val statusRepository = get<StatusRepository>()

    override fun registerStatusListener(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycle.addObserver(this)
        statusRepository
            .stream
            .onEach {
                if (it.any { it is Status.Loading }) {
                    ProgressDialog.instance.show(lifecycleOwner.toContext(), false)
                } else {
                    ProgressDialog.instance.hide()
                }
                it.forEach { status ->
                    when (status) {
                        is Status.Fail -> {
                            lifecycleOwner.toContext().appToast(ErrorMessageHandler.handleException(status.error))
                            lifecycleOwner.lifecycleScope.launch { statusRepository.removeStatus(status) }
                        }
                        else -> {}
                    }
                }
            }
            .flowWithLifecycle(lifecycleOwner.lifecycle, Lifecycle.State.STARTED)
            .launchIn(lifecycleOwner.lifecycleScope)
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (event == Lifecycle.Event.ON_DESTROY || event == Lifecycle.Event.ON_STOP) {
            source.lifecycleScope.launch { statusRepository.clear() }
        }
    }

    private fun LifecycleOwner.toContext(): Context {
        return when (this) {
            is Activity -> this
            is Fragment -> requireContext()
            else -> throw Exception("Unsupported LifecycleOwner")
        }
    }
}