package presentation.common.feature.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Popup
import core.common.status.Status
import keyless.presentation.common.R
import presentation.common.feature.theme.AppTheme
import presentation.common.test.CommonTestTags

@Composable
fun AppLogoPage(
    modifier: Modifier = Modifier,
    isBackEnabled: Boolean,
    onBackPress: () -> Unit,
    status: List<Status>,
    onCancelStatus: (Status) -> Unit,
    content: @Composable () -> Unit
) {
    AppBasePage(
        modifier = modifier,
        topBar = {
            Image(
                modifier = Modifier.align(Alignment.Center),
                painter = painterResource(R.drawable.keyless_logo),
                contentDescription = null
            )

            if (isBackEnabled) {
                BackButton(modifier = Modifier.align(Alignment.CenterStart), isBackEnabled, onBackPress)
            }
        },
        content = content,
        status = status,
        onCancelStatus = onCancelStatus
    )
}

@Composable
fun AppTitledPage(
    modifier: Modifier = Modifier,
    title: String,
    isBackEnabled: Boolean,
    onBackPress: () -> Unit,
    status: List<Status>,
    onCancelStatus: (Status) -> Unit,
    content: @Composable () -> Unit
) {
    AppBasePage(
        modifier = modifier,
        topBar = {
            AppPageTitleText(modifier = Modifier.align(Alignment.Center), text = title)

            if (isBackEnabled) {
                BackButton(modifier = Modifier.align(Alignment.CenterStart), isBackEnabled, onBackPress)
            }
        },
        content = content,
        status = status,
        onCancelStatus = onCancelStatus
    )
}

@Composable
fun AppBasePage(
    modifier: Modifier = Modifier,
    status: List<Status> = emptyList(),
    onCancelStatus: (Status) -> Unit = {},
    design: DesignSystem.Screen = DesignSystem.Screen.Default,
    paddingValues: PaddingValues = PaddingValues(
        top = design.verticalPadding,
        start = design.horizontalPadding,
        end = design.horizontalPadding
    ),
    topBar: @Composable BoxScope.() -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
    ) {
        ScreenLayout(Modifier, design, paddingValues = paddingValues, topBar = topBar, content = content)

        if (status.isNotEmpty()) {
            StatusSection(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = design.verticalPadding)
                    .align(Alignment.BottomCenter)
                    .testTag(CommonTestTags.statusContainer),
                status = status,
                onCancel = onCancelStatus
            )
        }
    }
}

@Composable
private fun ScreenLayout(
    modifier: Modifier,
    design: DesignSystem.Screen,
    paddingValues: PaddingValues = PaddingValues(
        top = design.verticalPadding,
        start = design.horizontalPadding,
        end = design.horizontalPadding
    ),
    topBar: @Composable (BoxScope.() -> Unit),
    content: @Composable (() -> Unit)
) {
    Column(modifier = modifier) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = design.verticalPadding),
            color = MaterialTheme.colorScheme.primary,
            contentColor = MaterialTheme.colorScheme.onPrimary
        ) {
            Box { topBar() }
        }

        Surface(
            modifier = Modifier
                .weight(1f)
                .fillMaxSize(),
            color = MaterialTheme.colorScheme.onPrimary,
            contentColor = MaterialTheme.colorScheme.onBackground,
            shape = RoundedCornerShape(topStart = design.cornerRadius, topEnd = design.cornerRadius)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues = paddingValues)
            ) {
                content()
            }
        }
    }
}

@Composable
private fun StatusSection(modifier: Modifier = Modifier, status: List<Status>, onCancel: (Status) -> Unit) {
    Box(modifier = modifier.fillMaxWidth(), contentAlignment = Alignment.BottomCenter) {
        status.forEach { StatusItem(it, onCancel) }
    }
}

@Composable
private fun StatusItem(status: Status, onCancel: (Status) -> Unit) {
    when (status) {
        is Status.Info -> {
            if (status.message.localized().isNotBlank()) AppToast(text = status.message.localized())
        }

        is Status.Loading -> {
            LoadingScreen()
        }

        is Status.Intermediate -> {
            if (status.message.localized().isNotBlank()) AppToast(text = status.message.localized())
        }

        is Status.Success -> {
            if (status.message.localized().isNotBlank()) AppToast(text = status.message.localized())
        }

        is Status.Fail -> {
            if (status.message.localized().isNotBlank()) AppDialog(
                message = status.message.localized(),
                onConfirm = { onCancel(status) }
            )
        }
    }
}

@Composable
private fun LoadingScreen() {
    Popup {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.3f)),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}

@Composable
private fun Sample() {
    AppTitledPage(
        title = "Upload Documents",
        isBackEnabled = true,
        onBackPress = {},
        status = listOf(),
        onCancelStatus = {}
    ) {

    }
}

@Preview(showBackground = true)
@Composable
private fun Preview() {
    AppTheme {
        Sample()
    }
}

