package presentation.common.feature.state

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.updateAndGet


abstract class ItemState<T>(private val initial: T, flow: Flow<T>) {
    protected open val flow: Flow<T> = flow.onEach { state.value = it }
    protected val state = mutableStateOf(initial)

    val stream: Flow<T> = flow
    val value: T @Composable get() = flow.collectAsState(initial).value

    fun get() = state.value
}

open class MutableItemState<T>(
    initial: T,
    override val flow: MutableStateFlow<T> = MutableStateFlow(initial)
) : ItemState<T>(initial, flow) {
    fun update(value: T) {
        state.value = flow.updateAndGet { value }
    }

    fun update(block: (T) -> T) {
        state.value = flow.updateAndGet(block)
    }
}

class StringState(initial: String) : MutableItemState<String>(initial)

class BooleanState(initial: Boolean) : MutableItemState<Boolean>(initial) {
    fun toggle() = update { !it }
}

class IntState(initial: Int) : MutableItemState<Int>(initial) {
    fun increment() = update { it + 1 }
}

class DoubleState(initial: Double) : MutableItemState<Double>(initial) {
    fun increment() = update { it + 1 }
}

class LongState(initial: Long) : MutableItemState<Long>(initial) {
    fun increment() = update { it + 1 }
}

class ListState<T>(initial: List<T>) : MutableItemState<List<T>>(initial) {
    fun add(item: T) = update { it + item }
    fun addAll(items: List<T>) = update { it + items }
    fun replaceAll(items: List<T>) = update { items }
    fun addAt(index: Int, item: T) = update { ArrayList(it).apply { this.add(index, item) } }
    fun remove(item: T) = update { it - item }
    fun removeAt(index: Int) = update { ArrayList(it).apply { this.removeAt(index) } }
}

class DerivedState<T>(initial: T, flow: Flow<T>) : ItemState<T>(initial, flow)
