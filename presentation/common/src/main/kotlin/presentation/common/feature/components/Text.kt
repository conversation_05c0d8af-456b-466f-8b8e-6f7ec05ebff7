package presentation.common.feature.components

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.AnnotatedString
import presentation.common.feature.theme.mainFontFamily

@Composable
fun AppBodyText(modifier: Modifier = Modifier, text: String, design: TextDesign.Body = DesignSystem.Text.Body) {
    AppBaseText(modifier = modifier, text = AnnotatedString(text), design = design)
}

@Composable
fun AppBodyText(modifier: Modifier = Modifier, text: AnnotatedString, design: TextDesign.Body = DesignSystem.Text.Body) {
    AppBaseText(modifier = modifier, text = text, design = design)
}

@Composable
fun AppLabelText(modifier: Modifier = Modifier, text: String, design: TextDesign.Label = DesignSystem.Text.Label) {
    AppBaseText(modifier = modifier, text = AnnotatedString(text), design = design)
}

@Composable
fun AppPageTitleText(modifier: Modifier = Modifier, text: String, design: TextDesign.PageTitle = DesignSystem.Text.PageTitle) {
    AppBaseText(modifier = modifier, text = AnnotatedString(text), design = design)
}


@Composable
private fun AppBaseText(
    modifier: Modifier = Modifier,
    text: AnnotatedString,
    design: TextDesign
) {
    Text(
        modifier = modifier,
        text = text,
        color = design.color,
        fontWeight = design.weight,
        fontSize = design.size,
        fontFamily = mainFontFamily,
        textAlign = design.alignment,
        lineHeight = design.lineHeight,
        textDecoration = design.textDecoration,
        minLines = design.minLines,
        overflow = design.overflow
    )
}