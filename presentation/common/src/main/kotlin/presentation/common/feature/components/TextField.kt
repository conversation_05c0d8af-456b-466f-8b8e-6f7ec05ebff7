package presentation.common.feature.components

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import keyless.presentation.common.R
import presentation.common.feature.state.BooleanState
import presentation.common.feature.state.StringState

@Composable
fun AppOtpTextField(
    modifier: Modifier = Modifier,
    state: StringState,
    count: Int,
    hint: String = "",
    design: TextFieldDesign = DesignSystem.TextField.NormalTextField
) {
    Box(modifier = modifier.width(IntrinsicSize.Min)) {
        AppFlowRow(
            modifier = Modifier.width(IntrinsicSize.Max),
            arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium)
        ) {
            repeat(count) { index -> OtqCell(text = state.value.getOrNull(index)?.toString() ?: "") }
        }

        AppTextField(
            modifier = Modifier
                .fillMaxWidth()
                .alpha(0f),
            value = state.value,
            onValueChange = { if (it.length <= count) state.update(it) },
            hint = hint,
            design = design,
            singleLine = true
        )
    }
}

@Composable
private fun OtqCell(text: String) {
    AppSurface(
        modifier = Modifier
            .heightIn(min = DesignSystem.TextField.NormalTextField.minHeight)
            .width(DesignSystem.TextField.NormalTextField.minHeight),
        shape = DesignSystem.TextField.NormalTextField.shape,
        color = DesignSystem.TextField.NormalTextField.backgroundColor,
        paddings = PaddingValues(0.dp)
    ) {
        AppBodyText(modifier = Modifier.align(Alignment.Center), text = text)
    }
}

@Composable
fun AppSearchField(
    modifier: Modifier = Modifier,
    state: StringState,
    hint: String = stringResource(R.string.search),
    design: TextFieldDesign = DesignSystem.TextField.NormalTextField,
    onClear: () -> Unit
) {
    AppTextField(
        modifier = modifier,
        state = state,
        hint = hint,
        design = design,
        leading = { AppIcon(vector = Icons.Default.Search) },
        trailing = {
            if (state.value.isNotBlank()) {
                AppIconButton(vector = Icons.Default.Close, onClick = onClear)
            }
        }
    )
}

@Composable
fun AppPasswordField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    hint: String,
    hide: BooleanState? = null,
    design: TextFieldDesign = DesignSystem.TextField.NormalTextField
) {
    AppTextField(
        modifier = modifier,
        value = value,
        onValueChange = onValueChange,
        hint = hint,
        design = design,
        singleLine = true,
        visualTransformation = if (hide?.value == false) VisualTransformation.None else PasswordVisualTransformation(),
        trailing = hide?.let { hide ->
            {
                AppIconButton(
                    res = if (hide.value) R.drawable.ic_eye_hide else R.drawable.ic_eye_show,
                    onClick = { hide.toggle() }
                )
            }
        }
    )
}

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    state: StringState,
    hint: String,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    design: TextFieldDesign = DesignSystem.TextField.NormalTextField,
    singleLine: Boolean = false,
    leading: (@Composable () -> Unit)? = null,
    trailing: (@Composable () -> Unit)? = null
) {
    AppTextField(
        modifier = modifier,
        value = state.value,
        onValueChange = state::update,
        hint = hint,
        visualTransformation = visualTransformation,
        design = design,
        singleLine = singleLine,
        leading = leading,
        trailing = trailing
    )
}

@Composable
fun AppTextField(
    modifier: Modifier = Modifier,
    value: String,
    onValueChange: (String) -> Unit,
    hint: String,
    enabled: Boolean = true,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    design: TextFieldDesign = DesignSystem.TextField.NormalTextField,
    singleLine: Boolean = false,
    interactionSource: MutableInteractionSource? = null,
    leading: (@Composable () -> Unit)? = null,
    trailing: (@Composable () -> Unit)? = null
) {
    BasicTextField(
        modifier = modifier,
        value = value,
        enabled = enabled,
        onValueChange = onValueChange,
        textStyle = MaterialTheme.typography.bodyMedium,
        keyboardActions = design.keyboardActions,
        keyboardOptions = design.keyboardOptions,
        visualTransformation = visualTransformation,
        interactionSource = interactionSource,
        singleLine = singleLine
    ) { textField ->
        Box(
            modifier = Modifier
                .heightIn(min = design.minHeight)
                .clip(design.shape)
                .background(design.backgroundColor)
                .padding(horizontal = design.padding),
            contentAlignment = Alignment.CenterStart
        ) {
            AppRow(
                modifier = Modifier,
                arrangement = Arrangement.spacedBy(design.padding, Alignment.CenterHorizontally),
                alignment = Alignment.CenterVertically
            ) {
                leading?.invoke()

                Box(modifier = Modifier.weight(1f)) {
                    if (hint.isNotBlank() && value.isEmpty()) {
                        AppBodyText(
                            text = hint,
                            design = DesignSystem.Text.Body.copy(color = LocalContentColor.current.copy(0.6f))
                        )
                    }
                    textField()
                }

                trailing?.invoke()
            }
        }
    }
}