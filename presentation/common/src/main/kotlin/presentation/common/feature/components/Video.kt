package presentation.common.feature.components

import android.widget.VideoView
import androidx.annotation.RawRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect

@Composable
fun AppVideo(
    modifier: Modifier = Modifier,
    @RawRes res: Int,
    loop: Boolean = true
) {
    val context = LocalContext.current
    val video = remember { VideoView(context) }
    var lastPosition = remember { 0 }

    DisposableEffect(Unit) {
        video.setVideoURI(("android.resource://" + context.packageName + "/" + res).toUri())
        video.start()
        if (loop) video.setOnCompletionListener { video.start() }

        onDispose {
            video.pause()
            video.stopPlayback()
        }
    }

    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
        video.setOnPreparedListener {
            it.seekTo(lastPosition)
            it.setOnSeekCompleteListener { smp -> smp.start() }
        }
    }
    LifecycleEventEffect(Lifecycle.Event.ON_PAUSE) {
        lastPosition = video.currentPosition
        video.pause()
    }

    AndroidView(
        modifier = modifier,
        factory = { context -> video }
    )
}