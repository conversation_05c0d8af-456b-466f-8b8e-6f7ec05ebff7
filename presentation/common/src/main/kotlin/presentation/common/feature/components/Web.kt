package presentation.common.feature.components

import android.content.Context
import android.graphics.Bitmap
import android.os.Bundle
import android.view.View
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import keyless.presentation.common.R
import presentation.common.domain.models.Device
import presentation.common.feature.theme.AppTheme
import presentation.common.test.CommonTestTags

class WebActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val url = intent.getStringExtra("url") ?: ""
        val title = intent.getStringExtra("title") ?: ""

        setContent { AppTheme { Screen(title = title, url = url) } }
    }

    @Composable
    private fun Screen(title: String, url: String) {
        AppTitledPage(
            modifier = Modifier.testTag(CommonTestTags.webActivity),
            title = title,
            isBackEnabled = true,
            status = listOf(),
            onCancelStatus = {},
            onBackPress = { finish() }
        ) {

            if (Device.isNetworkAvailable(LocalContext.current)) {
                AndroidView(factory = { setUpWebView(it, url) }, modifier = Modifier.fillMaxSize())
            } else {
                NoInternetView()
            }
        }
    }

    private fun setUpWebView(context: Context, url: String): WebView {
        val webView = WebView(context)

        webView.settings.javaScriptEnabled = true
        webView.settings.loadWithOverviewMode = true
        webView.settings.useWideViewPort = true
        webView.settings.blockNetworkImage = false
        webView.settings.loadsImagesAutomatically = true
        webView.settings.javaScriptCanOpenWindowsAutomatically = true
        webView.settings.mediaPlaybackRequiresUserGesture = false
        webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        webView.settings.cacheMode = WebSettings.LOAD_DEFAULT
        webView.settings.textZoom = 100

        webView.settings.domStorageEnabled = true
        webView.settings.setSupportMultipleWindows(true)
        webView.settings.loadWithOverviewMode = true
        webView.settings.allowContentAccess = true
        webView.settings.setGeolocationEnabled(true)
        webView.settings.allowUniversalAccessFromFileURLs = true
        webView.settings.allowFileAccess = true

        webView.settings.safeBrowsingEnabled = true // api 26


        webView.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
                webView.loadUrl(request.url.toString())
                return true
            }

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
            }

            override fun onPageFinished(view: WebView, url: String) = Unit
        }
        webView.loadUrl(url)

        return webView
    }
}

@Composable
fun NoInternetView() {
    AppColumn(
        modifier = Modifier.fillMaxSize(),
        arrangement = Arrangement.Center,
        alignment = Alignment.CenterHorizontally
    ) {
        AppImage(
            modifier = Modifier.fillMaxWidth(),
            res = R.drawable.img_no_internet,
            contentScale = ContentScale.FillBounds
        )

        AppBodyText(text = stringResource(R.string.no_internet_connection))
    }
}