package presentation.common.feature.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Surface
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun AppColumn(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical = Arrangement.spacedBy(DesignSystem.Padding.small, Alignment.Top),
    alignment: Alignment.Horizontal = Alignment.Start,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier,
        content = content,
        verticalArrangement = arrangement,
        horizontalAlignment = alignment
    )
}

@Composable
fun AppScrollColumn(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Vertical = Arrangement.spacedBy(DesignSystem.Padding.small, Alignment.Top),
    alignment: Alignment.Horizontal = Alignment.Start,
    state: LazyListState = rememberLazyListState(),
    content: LazyListScope.() -> Unit
) {
    LazyColumn(
        state = state,
        modifier = modifier,
        content = content,
        verticalArrangement = arrangement,
        horizontalAlignment = alignment
    )
}

@Composable
fun AppRow(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Horizontal = Arrangement.spacedBy(DesignSystem.Padding.small),
    alignment: Alignment.Vertical = Alignment.CenterVertically,
    content: @Composable RowScope.() -> Unit
) {
    Row(
        modifier = modifier,
        horizontalArrangement = arrangement,
        verticalAlignment = alignment,
        content = content
    )
}

@Composable
fun AppScrollRow(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Horizontal = Arrangement.spacedBy(DesignSystem.Padding.small),
    alignment: Alignment.Vertical = Alignment.CenterVertically,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    state: LazyListState = rememberLazyListState(),
    content: LazyListScope.() -> Unit
) {
    LazyRow(
        state = state,
        modifier = modifier,
        content = content,
        horizontalArrangement = arrangement,
        verticalAlignment = alignment,
        contentPadding = contentPadding
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AppFlowRow(
    modifier: Modifier = Modifier,
    arrangement: Arrangement.Horizontal = Arrangement.spacedBy(DesignSystem.Padding.small),
    alignment: Arrangement.Vertical = Arrangement.spacedBy(DesignSystem.Padding.small),
    maxItemsInEachRow: Int = Int.MAX_VALUE,
    content: @Composable RowScope.() -> Unit
) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = arrangement,
        verticalArrangement = alignment,
        maxItemsInEachRow = maxItemsInEachRow,
        content = content
    )
}

@Composable
fun AppSurface(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = MaterialTheme.colorScheme.onSurface,
    shape: Shape = MaterialTheme.shapes.medium,
    tonalElevation: Dp = 0.dp,
    shadowElevation: Dp = 0.dp,
    paddings: PaddingValues = PaddingValues(0.dp),
    content: @Composable BoxScope.() -> Unit
) {
    Surface(
        modifier = modifier,
        color = color,
        contentColor = contentColor,
        shape = shape,
        tonalElevation = tonalElevation,
        shadowElevation = shadowElevation
    ) {
        Box(
            modifier = Modifier
                .padding(paddings)
        ) {
            content()
        }
    }
}

@Composable
fun AppClickableSurface(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = MaterialTheme.colorScheme.onSurface,
    shape: Shape = MaterialTheme.shapes.medium,
    tonalElevation: Dp = 0.dp,
    shadowElevation: Dp = 0.dp,
    paddings: PaddingValues = PaddingValues(DesignSystem.Padding.medium),
    enabled: Boolean,
    onClick: () -> Unit,
    content: @Composable BoxScope.() -> Unit
) {
    Surface(
        modifier = modifier,
        color = color,
        contentColor = contentColor,
        shape = shape,
        tonalElevation = tonalElevation,
        shadowElevation = shadowElevation,
        enabled = enabled,
        onClick = onClick
    ) {
        Box(
            modifier = Modifier
                .padding(paddings)
        ) {
            content()
        }
    }
}

@Composable
fun AppHorizontalDivider() {
    HorizontalDivider(color = Color(0x20000000))
}

@Composable
fun AppVerticalDivider() {
    VerticalDivider(color = Color(0x20000000))
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppBottomSheet(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit,
    title: String = "",
    content: @Composable BoxScope.() -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        dragHandle = { AppBottomSheetDragHandle(title) }
    ) {
        Box(
            modifier = Modifier.padding(
                vertical = DesignSystem.Screen.Default.verticalPadding,
                horizontal = DesignSystem.Screen.Default.horizontalPadding
            ),
            content = content
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AppBottomSheetDragHandle(title: String) {
    AppColumn(
        alignment = Alignment.CenterHorizontally
    ) {
        BottomSheetDefaults.DragHandle()

        if (title.isNotBlank()) {
            AppPageTitleText(text = title)
        }
    }
}