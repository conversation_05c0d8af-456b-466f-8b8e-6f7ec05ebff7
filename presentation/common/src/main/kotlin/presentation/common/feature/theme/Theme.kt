package presentation.common.feature.theme

import android.os.Build
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.LocalOverscrollConfiguration
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext

private object ExtendedTheme {
    var isDarkTheme: Boolean = false
}

@Suppress("UnusedReceiverParameter")
val MaterialTheme.isDarkTheme: Boolean
    @Composable
    get() = ExtendedTheme.isDarkTheme

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AppTheme(
//    darkTheme: Boolean = isSystemInDarkTheme(),
    darkTheme: Boolean = false,
    useDynamicColors: Boolean = false,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current
    val colors = when {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && useDynamicColors && darkTheme -> {
            dynamicDarkColorScheme(LocalContext.current)
        }

        Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && useDynamicColors && !darkTheme -> {
            dynamicLightColorScheme(LocalContext.current)
        }

        darkTheme -> remember { darkColorScheme() }
        else -> lightColorScheme

    }

    ExtendedTheme.isDarkTheme = darkTheme

    CompositionLocalProvider(LocalOverscrollConfiguration provides null) {
        MaterialTheme(
            colorScheme = colors,
            typography = extendedTypography(),
            content = {
                content()
            }
        )
    }
}
