package presentation.common.feature.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

val lightColorScheme = lightColorScheme(
    primary = Color(0xFF001C55),
    onPrimary = Color(0xFFFFFFFF),
    secondary = Color(0xFFCDBC7A),
    onSecondary = Color(0xFF000000),
    tertiary = Color(0xFFD9DDE6),
    onTertiary = Color(0xFF000000),
    surfaceVariant = Color(0xFF001C55).copy(alpha = 0.1f)
)

val color_whatsapp_light = Color(0xFF25d366)
val color_whatsapp_dark = Color(0xFF25d366)
val color_on_whatsapp_light = Color(0xFF000000)
val color_on_whatsapp_dark = Color(0xFF000000)

val ColorScheme.whatsapp
    @Composable get() = if (!isSystemInDarkTheme()) color_whatsapp_light else color_whatsapp_dark
val ColorScheme.onWhatsapp
    @Composable get() = if (!isSystemInDarkTheme()) color_on_whatsapp_light else color_on_whatsapp_dark
