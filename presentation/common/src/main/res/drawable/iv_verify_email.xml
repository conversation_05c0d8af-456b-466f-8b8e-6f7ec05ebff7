<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="147dp"
    android:height="118dp"
    android:viewportWidth="147"
    android:viewportHeight="118">
  <group>
    <clip-path
        android:pathData="M0,0h147v118h-147z"/>
    <path
        android:pathData="M0.87,41.11C0.9,41.06 0.92,41.02 0.95,40.97C0.96,40.94 0.98,40.92 1,40.88C1.02,40.85 1.04,40.81 1.05,40.78C1.71,39.61 2.65,38.66 3.72,38.2L4.02,38.07L40.8,22.15L77.57,6.23L77.87,6.1C78.94,5.63 79.95,5.74 80.74,6.29C80.76,6.3 80.78,6.32 80.81,6.33C80.83,6.35 80.85,6.36 80.87,6.38L85.13,9.5L82.38,10.31L86.03,59.33C86.12,60.59 85.76,61.93 85.09,63.1C85.08,63.14 85.06,63.17 85.04,63.2C85.02,63.23 85,63.26 84.99,63.29C84.33,64.47 83.39,65.42 82.32,65.88L8.98,97.63L9.28,100.71L5.24,97.74C5.24,97.74 5.2,97.71 5.17,97.7C4.38,97.16 3.85,96.2 3.75,94.94L0.01,44.75C-0.08,43.53 0.25,42.25 0.87,41.11H0.87Z"
        android:strokeAlpha="0.5"
        android:fillColor="#ffffff"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M12.35,101.06L86.5,68.95C88.74,67.98 90.39,64.88 90.18,62.03L86.49,12.56C86.28,9.71 84.29,8.19 82.05,9.16L7.9,41.27C5.66,42.24 4.01,45.34 4.22,48.19L7.91,97.66C8.12,100.51 10.11,102.03 12.35,101.06Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="52.02"
            android:startY="53.07"
            android:endX="-29.89"
            android:endY="89.04"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M8.2,41.14L44.98,25.22L49.31,83.38L12.66,100.93C10.25,101.97 8.14,100.67 7.94,98.02L4.2,47.83C4,45.18 5.79,42.19 8.2,41.15V41.14Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="-28.09"
            android:startY="145.02"
            android:endX="0.17"
            android:endY="75.04"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M81.75,9.3L44.98,25.22L49.28,83.01L86.2,69.07C88.61,68.03 90.41,65.04 90.21,62.39L86.47,12.2C86.27,9.54 84.15,8.24 81.75,9.29V9.3Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="100.53"
            android:startY="89.19"
            android:endX="29.65"
            android:endY="62.15"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M89.35,66.04C88.62,67.39 87.5,68.52 86.2,69.08L12.66,100.93C11.36,101.49 10.14,101.37 9.27,100.72L46.64,47.6L89.35,66.04Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="44.96"
            android:startY="148.24"
            android:endX="11.82"
            android:endY="71.71"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF3A9BD5"/>
          <item android:offset="0.84" android:color="#FF3796D1"/>
          <item android:offset="0.92" android:color="#FF3088C8"/>
          <item android:offset="1" android:color="#FF2573BA"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M5.06,44.18C5.79,42.84 6.9,41.71 8.2,41.14L81.75,9.3C83.05,8.73 84.26,8.85 85.14,9.5L47.77,62.62L5.06,44.18Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22.22"
            android:startY="22.14"
            android:endX="-35.66"
            android:endY="89.35"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M20.86,50.65C20.88,50.6 20.9,50.55 20.92,50.5C20.94,50.47 20.95,50.43 20.97,50.39C20.99,50.36 21.01,50.32 21.03,50.29C21.64,48.97 22.57,47.87 23.68,47.29L24,47.12L62.25,27.02L100.5,6.91L100.82,6.74C101.93,6.16 103.03,6.2 103.92,6.72C103.95,6.73 103.97,6.75 104,6.76C104.02,6.78 104.04,6.79 104.07,6.8L108.91,9.82L106.01,10.91L113.96,63.35C114.16,64.7 113.88,66.18 113.26,67.49C113.25,67.53 113.23,67.56 113.22,67.61C113.2,67.64 113.19,67.68 113.17,67.71C112.55,69.02 111.63,70.12 110.52,70.7L34.22,110.81L34.79,114.11L30.21,111.24C30.21,111.24 30.16,111.21 30.14,111.2C29.24,110.68 28.58,109.7 28.38,108.34L20.24,54.65C20.04,53.34 20.29,51.93 20.86,50.66V50.65Z"
        android:strokeAlpha="0.5"
        android:fillColor="#ffffff"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M38.13,114.22L115.27,73.68C117.61,72.45 119.12,68.99 118.66,65.94L110.64,13.01C110.18,9.96 107.91,8.49 105.58,9.71L28.43,50.26C26.1,51.49 24.58,54.96 25.05,58L33.07,110.93C33.53,113.98 35.79,115.46 38.13,114.23V114.22Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="61.63"
            android:startY="67.45"
            android:endX="-23.52"
            android:endY="112.83"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.75,50.1L67,29.99L76.44,92.21L38.45,114.06C35.94,115.38 33.56,114.15 33.13,111.31L24.99,57.61C24.55,54.78 26.24,51.41 28.75,50.1Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="-17.03"
            android:startY="172.87"
            android:endX="7.71"
            android:endY="95.31"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M105.26,9.88L67,29.99L76.38,91.82L114.96,73.84C117.47,72.52 119.15,69.16 118.72,66.33L110.58,12.63C110.14,9.79 107.77,8.56 105.26,9.88Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="116.06"
            android:startY="102.66"
            android:endX="37.56"
            android:endY="79.37"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M118.09,70.32C117.42,71.82 116.31,73.13 114.95,73.84L38.44,114.06C37.09,114.77 35.77,114.73 34.78,114.11L70.63,53.93L118.09,70.32H118.09Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="61.83"
            android:startY="170.41"
            android:endX="19.95"
            android:endY="90.73"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF3A9BD5"/>
          <item android:offset="0.84" android:color="#FF3796D1"/>
          <item android:offset="0.92" android:color="#FF3088C8"/>
          <item android:offset="1" android:color="#FF2573BA"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M25.61,53.62C26.28,52.13 27.39,50.81 28.75,50.1L105.26,9.88C106.61,9.17 107.93,9.21 108.92,9.84L73.07,70.01L25.61,53.62Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.03"
            android:startY="36.59"
            android:endX="-29.75"
            android:endY="113.56"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M42.01,50.42C42.04,50.37 42.05,50.32 42.08,50.26C42.1,50.23 42.11,50.19 42.12,50.15C42.14,50.11 42.16,50.07 42.18,50.03C42.78,48.6 43.72,47.4 44.89,46.73L45.22,46.54L85.17,23.62L125.12,0.71L125.45,0.52C126.61,-0.14 127.78,-0.15 128.76,0.37C128.79,0.39 128.81,0.4 128.84,0.42C128.86,0.43 128.89,0.45 128.91,0.46L134.19,3.48L131.15,4.76L141.7,60.31C141.97,61.75 141.73,63.33 141.13,64.75C141.11,64.79 141.09,64.83 141.08,64.87C141.06,64.91 141.05,64.95 141.03,64.98C140.42,66.41 139.48,67.62 138.32,68.28L58.63,113.99L59.37,117.48L54.37,114.6C54.37,114.6 54.32,114.57 54.29,114.56C53.32,114.04 52.59,113.02 52.31,111.59L41.51,54.7C41.24,53.32 41.46,51.8 42.02,50.42H42.01Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M62.94,117.48L143.51,71.26C145.95,69.87 147.43,66.12 146.81,62.89L136.16,6.82C135.55,3.59 133.08,2.11 130.64,3.5L50.07,49.71C47.63,51.11 46.15,54.85 46.77,58.08L57.41,114.16C58.03,117.39 60.5,118.87 62.94,117.47V117.48Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="82.52"
            android:startY="68.75"
            android:endX="-6.37"
            android:endY="120.45"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M50.4,49.52L90.35,26.6L102.88,92.52L63.27,117.28C60.65,118.79 58.06,117.57 57.49,114.56L46.69,57.67C46.12,54.67 47.78,51.02 50.4,49.51V49.52Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="2.92"
            android:startY="184.12"
            android:endX="26.23"
            android:endY="100.54"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M130.31,3.69L90.35,26.61L102.8,92.11L143.18,71.45C145.8,69.95 147.46,66.3 146.89,63.29L136.08,6.4C135.51,3.4 132.93,2.18 130.31,3.69Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="141.66"
            android:startY="104.4"
            android:endX="57.11"
            android:endY="82.71"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF59BDEF"/>
          <item android:offset="0.85" android:color="#FF54B8EB"/>
          <item android:offset="0.93" android:color="#FF48AAE1"/>
          <item android:offset="1" android:color="#FF3A9BD5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M146.38,67.57C145.72,69.19 144.59,70.64 143.18,71.45L63.27,117.29C61.85,118.1 60.45,118.11 59.37,117.48L95.17,51.98L146.38,67.58L146.38,67.57Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="86.83"
            android:startY="178.42"
            android:endX="39.07"
            android:endY="95.16"
            android:type="linear">
          <item android:offset="0.77" android:color="#FF3A9BD5"/>
          <item android:offset="0.84" android:color="#FF3796D1"/>
          <item android:offset="0.92" android:color="#FF3088C8"/>
          <item android:offset="1" android:color="#FF2573BA"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M47.19,53.4C47.84,51.78 48.98,50.34 50.39,49.53L130.31,3.69C131.72,2.88 133.12,2.87 134.21,3.49L98.4,69L47.19,53.4V53.4Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="44.44"
            android:startY="37.22"
            android:endX="-13"
            android:endY="121.44"
            android:type="linear">
          <item android:offset="0" android:color="#FF7BDAFF"/>
          <item android:offset="1" android:color="#FF59BDEF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
