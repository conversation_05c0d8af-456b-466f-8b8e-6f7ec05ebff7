<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!--        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>-->
        <item name="android:statusBarColor">@color/colorAccent</item>
        <item name="colorSecondary">@color/colorAccent</item>
        <item name="colorSecondaryVariant">@color/colorAccent</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:windowDisablePreview">true</item>
    </style>
    <style name="progressBarBlue" parent="@style/Theme.AppCompat">
        <item name="colorAccent">@color/colorAccent</item>
    </style>
</resources>