@file:Suppress("PackageDirectoryMismatch")

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.LibraryExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.getByType

class AndroidComposeConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) = with(target) {
        pluginManager.apply(catalog.findPlugin("compose.compiler").get().get().pluginId)
        applyToLibrary()
    }

    private fun Project.applyToLibrary() {
        try {
            configureAndroidCompose(extensions.getByType<LibraryExtension>())
        } catch (ex: Throwable) {
            ex.printStackTrace()
            applyToApplication()
        }
    }

    private fun Project.applyToApplication() {
        try {
            configureAndroidCompose(extensions.getByType<ApplicationExtension>())
        } catch (ex: Throwable) {
            throw ex
        }
    }
}
