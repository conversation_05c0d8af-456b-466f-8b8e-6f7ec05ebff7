@file:Suppress("PackageDirectoryMismatch", "unused")

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.CommonExtension
import com.android.build.api.dsl.LibraryExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.api.plugins.ExtensionAware
import org.gradle.kotlin.dsl.dependencies
import org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions

internal fun Project.configureAndroidApplication(application: ApplicationExtension) = with(application) {
    configureBaseAndroid(application)
    defaultConfig.targetSdk = catalog.findVersion("androidSdkTarget").get().displayName.toInt()

    buildTypes {
        release {
            isMinifyEnabled = false
        }
    }

    buildTypes.all {
        this.proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    }
}

internal fun Project.configureAndroidLibrary(library: LibraryExtension) = with(library) {
    configureBaseAndroid(library)
    defaultConfig {
        consumerProguardFiles("proguard-rules.pro")
    }
    buildTypes {
        release {
            isMinifyEnabled = false
        }
    }

    buildTypes.all {
        this.proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    }
}

internal fun Project.configureAndroidCompose(commonExtension: CommonExtension<*, *, *, *, *, *>) = with(commonExtension) {
    buildFeatures.compose = true

    dependencies {
        val bom = catalog.findLibrary("androidx-compose-bom").get()

        add("implementation", platform(bom))
        add("implementation", catalog.findLibrary("androidx.compose.activity").get())
        add("implementation", catalog.findLibrary("androidx.compose.material3").get())
        add("implementation", catalog.findLibrary("androidx.compose.lifecycle").get())
        add("implementation", catalog.findLibrary("androidx.compose.ui.tooling.preview").get())

        add("debugImplementation", catalog.findLibrary("androidx.compose.ui.test.manifest").get())
        add("debugImplementation", catalog.findLibrary("androidx.compose.ui.tooling").get())
    }
}

private fun Project.configureBaseAndroid(commonExtension: CommonExtension<*, *, *, *, *, *>) = with(commonExtension) {
    compileSdk = catalog.findVersion("androidSdkCompile").get().displayName.toInt()
    defaultConfig.minSdk = catalog.findVersion("androidSdkMin").get().displayName.toInt()
    defaultConfig.testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    defaultConfig.vectorDrawables.useSupportLibrary = true

    compileOptions.sourceCompatibility = JavaVersion
        .toVersion(catalog.findVersion("javaSource").get().displayName)
    compileOptions.targetCompatibility = JavaVersion
        .toVersion(catalog.findVersion("javaTarget").get().displayName)
    kotlinOptions.jvmTarget = JavaVersion
        .toVersion(catalog.findVersion("javaTarget").get().displayName).toString()

    namespace = namespace()

    packaging.resources.excludes += "/META-INF/{AL2.0,LGPL2.1}"

    dependencies {
        add("implementation", catalog.findLibrary("android.material").get())
        add("implementation", catalog.findLibrary("androidx.appcompat").get())
        add("implementation", catalog.findLibrary("androidx.annotation").get())
        add("implementation", catalog.findLibrary("androidx.core.ktx").get())
        add("implementation", catalog.findLibrary("androidx-core-splashscreen").get())
        add("implementation", catalog.findLibrary("androidx.navigation.fragment").get())
        add("implementation", catalog.findLibrary("androidx.navigation.ui").get())
        add("implementation", catalog.findLibrary("kotlinx.coroutines.core").get())
        add("implementation", catalog.findLibrary("kotlinx.coroutines.android").get())
        add("implementation", catalog.findLibrary("kotlinx.datetime").get())

        add("testImplementation", catalog.findLibrary("junit").get())
        add("testImplementation", catalog.findLibrary("kotlin.test").get())
        add("testImplementation", catalog.findLibrary("kotlinx.coroutines.test").get())

        add("androidTestImplementation", catalog.findLibrary("androidx.espresso.core").get())
        add("androidTestImplementation", catalog.findLibrary("androidx.test.junit").get())
        add("androidTestImplementation", catalog.findLibrary("kotlin.test").get())
        add("androidTestImplementation", catalog.findLibrary("kotlinx.coroutines.test").get())
    }
}

internal fun CommonExtension<*, *, *, *, *, *>.kotlinOptions(block: KotlinJvmOptions.() -> Unit) {
    (this as ExtensionAware).extensions.configure("kotlinOptions", block)
}

internal val CommonExtension<*, *, *, *, *, *>.`kotlinOptions`: KotlinJvmOptions
    get() =
    (this as ExtensionAware).extensions.getByName("kotlinOptions") as KotlinJvmOptions