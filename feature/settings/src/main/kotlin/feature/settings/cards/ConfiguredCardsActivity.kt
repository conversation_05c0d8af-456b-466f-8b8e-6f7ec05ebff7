package feature.settings.cards

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.DataModelCardManage
import data.utils.android.CommonValues
import data.utils.android.hideKeyboard
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import feature.common.dialogs.dialogYesNo
import feature.common.navigation.logoutAndNavToDashboard
import feature.settings.profile.ProfileViewModel
import keyless.data.utils.android.R
import keyless.feature.settings.databinding.ActivityConfiguredCardsBinding
import org.koin.androidx.viewmodel.ext.android.viewModel
import rayo.logicsdk.bean.BleLockScanData
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.PermissionActionEnum
import rayo.logicsdk.bean.PermissionTypeEnum
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockPermissionData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import rayo.logicsdk.utils.TimeUtils
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class ConfiguredCardsActivity : AppCompatActivity(), AdapterManageCards.SelectCard {

    private var decryptedAccessKey: String = ""
    private lateinit var selectedModel: DataModelCardManage
    private val viewModel: ProfileViewModel by viewModel()
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private lateinit var adapterManageCards: AdapterManageCards
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String?> = ArrayList<String?>()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    private lateinit var bluetoothDeviceNew: BleLockScanData
    private var mLockBasicInfo: LockBasicInfo? = null
    private var page = 1
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private var isSearch  = false
    private val layoutManager = LinearLayoutManager(this)
    private var listTotalCards: ArrayList<DataModelCardManage> = ArrayList()
    private var REQUEST_CHECK_SETTINGS = 3
    lateinit var dialogLock: Dialog
    private var isPopupShow = 0
    private lateinit var binding: ActivityConfiguredCardsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityConfiguredCardsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        if (CommonValues.isNetworkAvailable(this)) {
            initz()
            setAdapter()
            apiImplementation("show", page, "")
            observerInit()
            pagination()
        } else {
            binding.noInternetLayout.isVisible = true
            binding.svLock.isVisible = false
            binding.layNoData.isVisible = false
        }
        clickListeners()
    }

    override fun onResume() {
        super.onResume()
        apiChecking()
    }

    private fun apiChecking() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        viewModel.getCheckUser(sharePrefs.token, jsonObject).observe(this) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (!it.success) {
                logoutAndNavToDashboard(it.message, this)
            }
        }
    }

    private fun pagination() {
        binding.rvAllCards.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore && !isSearch) {
                    page++
                    apiImplementation("hide", page, "")
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 151) {
            apiImplementation("show", 1, "")
        }

        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            initLock()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    private fun setAdapter() {
        binding.rvAllCards.layoutManager = layoutManager
        adapterManageCards = AdapterManageCards(this)
        binding.rvAllCards.adapter = adapterManageCards
    }

    private fun clickListeners() {
        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        if (isPopupShow == 1){
//                            mBleScanCallback.finishScan()
                            mBleLockSdk?.disconnect()
                        }
                        finish()
                    }
                })
            )
        }

        binding.svLock.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    isSearch = true
                    performSearch()
                    return@OnEditorActionListener true
                }
                false
            }
        )

        binding.svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.stopSearch.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.svLock.setText("")
            page = 1
            isSearch = false
            apiImplementation("show", 1, "")
        }

        binding.addRayonicsCard.setOnClickListener {
            if (Preferences.isAdminLogin()) {
                defaultDialog(
                    this,
                    getString(R.string.disabled_in_admin_mode),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            } else {
                if (isPopupShow == 1) {
                    dialogLock.dismiss()
                }
                if (CommonValues.isNetworkAvailable(this)) {
                    startActivityForResult(Intent(this, SelectRayonicsLock::class.java), 50)
                } else {
                    toast(getString(keyless.feature.common.R.string.you_are_in_offline))
                }
            }
        }

        binding.btnConfigureCard.setOnClickListener {
            if (Preferences.isAdminLogin()) {
                defaultDialog(
                    this,
                    getString(R.string.disabled_in_admin_mode),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            } else {
                if (isPopupShow == 1) {
                    dialogLock.dismiss()
                }
//                start?.cancelTimer()
                if (CommonValues.isNetworkAvailable(this)) {
                    startActivityForResult(Intent(this, SelectRayonicsLock::class.java), 50)
                } else {
                    toast(getString(keyless.feature.common.R.string.you_are_in_offline))
                }
            }
        }

        binding.backBtn.setOnClickListener {
            if (isPopupShow == 1) {
                mBleLockSdk?.disconnect()
            }
            finish()
        }
    }

    private fun performSearch() {
        apiImplementation("show", 1, binding.svLock.text.toString())
    }

    private fun observerInit() {
        viewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        viewModel.error.observe(this) {
            toast(it)
        }
    }

    private fun apiImplementation(progressValue: String, page: Int, search: String) {
        isLoadingMore = true
        viewModel.getConfiguredCardList(sharePrefs.token, progressValue, page, search)
            .observe(this) {
                isLoadingMore = false
                binding.progressPagination.visibility = View.GONE
                if (it.data.size > 0) {
                    binding.svLock.isVisible = true
                    if (page == 1) {
                        listTotalCards.clear()
                    }
                    binding.rvAllCards.isVisible = true
                    binding.layNoData.isVisible = false
                    listTotalCards.addAll(it.data)
                    isLastPage = listTotalCards.size == it.total_count
                    adapterManageCards.update(listTotalCards)
                } else {
                    val network = CommonValues.isNetworkAvailable(this)
                    if (network) {
                        binding.layNoData.isVisible = true
                        binding.noInternetLayout.isVisible = false
                        binding.svLock.isVisible = false
                        binding.rvAllCards.isVisible = false
                    } else if (!network) {
                        binding.noInternetLayout.isVisible = true
                        binding.svLock.isVisible = false
                        binding.rvAllCards.isVisible = false
                    }
                    binding.svLock.isVisible = binding.svLock.text.toString().isNotEmpty()
                }
            }
        binding.progressBar.isVisible = false
    }

    private fun initz() {
        binding.addRayonicsCard.isVisible =
            Preferences.role.get() != Roles.CUSTOMER_SERVICES && Preferences.role.get() != Roles.VIEWER_ACCESS
        binding.btnConfigureCard.isVisible =
            Preferences.role.get() != Roles.CUSTOMER_SERVICES && Preferences.role.get() != Roles.VIEWER_ACCESS
    }

    override fun clickToManageCard(modelCards: DataModelCardManage) {
        selectedModel = modelCards

        if (selectedModel.lock_assign.lock.encrypted_key.isNotEmpty()) {
            val decryptedKey =
                CommonValues.decrypt(selectedModel.lock_assign.lock.encrypted_key, sharePrefs.uuid)
            if (selectedModel.lock_assign.lock.encrypted_key.isNotEmpty()) {
                decryptedAccessKey = decryptedKey!!
            }
//            else {
//                if (selectedModel.lock_assign.lock.provider == CommonValues.keyless){
//                    decryptedAccessKey = selectedModel.lock_assign.lock.access_key
//                }
//            }
        }

        dialogYesNo(
            this, getString(R.string.delete_card),
            getString(R.string.are_you_sure_you_want_delete_card),
            object : OnActionYesNo {
                override fun onYes(view: View) {
                    askPermission()
                }

                override fun onNo(view: View) {

                }

                override fun onClickData(view: View, data: String) {

                }
            })

//        val alertDialog = android.app.AlertDialog.Builder(this)
//        alertDialog.setTitle(getString(R.string.delete_card))
//        alertDialog.setMessage(getString(R.string.are_you_sure_you_want_delete_card))
//        alertDialog.setPositiveButton(
//            getString(R.string.yes)
//        ) { dialog, which ->
//
//
//        }
//        alertDialog.setNegativeButton(
//            getString(R.string.text_cancel)
//        ) { dialog, which -> dialog.cancel() }
//        alertDialog.show()
    }

    private fun showDialogForScanning() {
        isPopupShow = 1
        dialogLock = Dialog(this)
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        var animatedDots =
            dialogLock.findViewById<TextAndAnimationView>(keyless.feature.common.R.id.animatedDotsDialog)
        var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }
        cancelBtn.setOnClickListener {
            mBleLockSdk?.disconnect()
            dialogLock.dismiss()
        }
        dialogLock.show()
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        }

//        else {
//            progressBar.isVisible = true
//            initLock()
//        }
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                        initLock()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                var gps_enabled = false
                var network_enabled = false

                try {
                    gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                } catch (ex: java.lang.Exception) {
                }

                try {
                    network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                } catch (ex: java.lang.Exception) {
                }

                if (!gps_enabled && !network_enabled) {
                    displayLocationSettingsRequest()
                } else {
                    if (CommonValues.isBluetoothEnabled()) {
                        initLock()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        initLock()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this@ConfiguredCardsActivity,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        if (isPopupShow == 1) {
            mBleLockSdk?.disconnect()
        }
        finish()
    }

    private fun initLock() {
        showDialogForScanning()
        mBleName = ArrayList<String?>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CommonValues.ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            addAdapterItemRange(msg.obj as BleLockScanData)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
                else -> {
                }
            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }

        bluetoothDeviceNew = bluetoothDevice
        mBluetoothDevice = bluetoothDevice as BleLockScanData
        if (mBluetoothDevice!!.bleName == selectedModel.lock_assign.lock.unique_key) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            connectLock()
        } else {
            isScan = true
        }
    }

    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            val lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    private fun checkName(bleName: String): Boolean {
        if (null == bleName) return false
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                init sdk  
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
            rayo.logicsdk.utils.Log.d(
                "TAG",
                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun connect(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
            connect 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun authentication(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """ authentication ${
                JSON.toJSONString(
                    resultBean,
                    SerializerFeature.WriteDateUseDateFormat
                )
                }""",
                this@ConfiguredCardsActivity
            )

            if (resultBean.isRet) {
                val lockPermissionData: LockPermissionData = object : LockPermissionData() {
                    override fun findPermission(permissionData: PermissionData) {}
                    override fun setPermission(
                        count: Int,
                        pos: Int,
                        permissionData: PermissionData
                    ) {
                    }
                }

                val end = Calendar.getInstance()
                end.add(Calendar.DAY_OF_YEAR, 1)

                lockPermissionData.permissionData.add(
                    LockPermissionData.PermissionData(
                        PermissionActionEnum.DELETE_ENUM,
                        selectedModel.card_uid,
                        PermissionTypeEnum.CARD_ID_ENUM,
                        TimeUtils.dateFromYMDHMS(TimeUtils.dateToNotYMDHM(Date())),
                        TimeUtils.dateFromYMDHMS(TimeUtils.dateToNotYMDHM(end.time)),
                        true,
                        true,
                        true,
                        true,
                        true,
                        true,
                        true,
                        true,
                        true,
                        true
                    )
                )
                mBleLockSdk?.updateLockPermission(lockPermissionData, false)
            } else {
                binding.progressBar.isVisible = false
                if (JSON.toJSONString(
                                    resultBean,
                                    SerializerFeature.WriteDateUseDateFormat
                                ).lowercase(Locale.getDefault()).contains("timeout")
                ) {
                    defaultDialog(
                        this@ConfiguredCardsActivity,
                        getString(R.string.the_lock_could_not_be_connected),
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        }
                    )
                } else {
                    toast(
                        JSON.toJSONString(
                            resultBean,
                            SerializerFeature.WriteDateUseDateFormat
                        )
                    )
                }
            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                disConnect 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                registerLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockInfo 
                ${resultBean.obj}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockInfo 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
//            CommonValues.showMessage(
//                """
//                setLockTime
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@ConfiguredCardsActivity
//            )
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                setLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                checkLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockStatus 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                updateLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
            if (resultBean!!.isRet) {
                dialogLock.dismiss()
                mBleLockSdk?.disconnect()
                val jsonObject = JsonObject()
                jsonObject.addProperty("card_id", selectedModel.internal_id)
                jsonObject.addProperty("lock_id", selectedModel.lock_assign.lock._id)
                viewModel.deleteManageCard(sharePrefs.token, jsonObject).observe(
                    this@ConfiguredCardsActivity
                ) {
                    if (it.success) {
//                        start?.cancelTimer()
                        apiImplementation("hide", page, "")
                    }
                }
            }
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                bleOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                pincodeOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setCalendar 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockSerialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readCardByCylinder 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun onReport(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                onReport 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                get reader info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set reader serialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                get keyboard info 
                ${resultBean.obj}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set keyboard info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun disPlay(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                Bluetooth Data 
                ${resultBean.obj as String}
                """.trimIndent(),
                this@ConfiguredCardsActivity
            )
        }

        override fun setTempCard(p0: ResultBean<*>?) {
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }
    }

    private fun getEndTime(): Date {
        val date1 = Calendar.getInstance().time
        val df = SimpleDateFormat("dd-MM-yyyy HH:mm:ss")
        val formattedDate = df.format(date1)
        Log.e("// $formattedDate", "")
        val date: Date = df.parse(formattedDate)
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.add(Calendar.DAY_OF_YEAR, 1)
        Log.e("// " + calendar.time, "")
        return calendar.time
    }
}