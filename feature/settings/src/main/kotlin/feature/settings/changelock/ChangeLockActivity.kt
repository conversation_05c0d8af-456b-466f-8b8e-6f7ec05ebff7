package feature.settings.changelock

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.util.Log
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import com.iseo.v364sdk.services.exception.V364SdkException
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileHermesCmdService
import com.iseo.v364sdk.services.mobilecredentialservice.model.ILock
import com.iseo.v364sdk.services.mobilecredentialservice.model.IMobileCredentialsInfo
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockBatteryStatus
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockResponse
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.iseo.v364sdk.services.scanservice.model.IKeyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILegacyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo
import com.iseo.v364sdk.services.scanservice.model.IScanBTManagerEvent
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ControlLockCallback
import com.ttlock.bl.sdk.callback.ScanLockCallback
import com.ttlock.bl.sdk.callback.SetLockTimeCallback
import com.ttlock.bl.sdk.constant.ControlAction
import com.ttlock.bl.sdk.entity.ControlLockResult
import com.ttlock.bl.sdk.entity.LockError
import com.ttlock.bl.sdk.util.LogUtil
import core.locks.logs.models.LockLogActionType
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.AndroidAirBnkManager
import data.common.preferences.Constants
import data.common.preferences.Preferences
import data.network.android.logLock
import data.network.android.models.LockHistoryModel
import data.utils.android.CommonValues
import data.utils.android.applications.ServiceProvider
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import domain.common.ErrorHandler
import feature.common.dialogs.appToast
import feature.common.models.ModelForData
import feature.common.navigation.logoutAndNavToDashboard
import keyless.data.utils.android.R
import keyless.feature.settings.maintenance.databinding.ActivityResetLockTimeBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import org.koin.android.ext.android.inject
import rayo.logicsdk.bean.BleLockScanData
import rayo.logicsdk.bean.DSTClass
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.LockTimeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockBleOpenData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import rayo.logicsdk.utils.TimeUtils
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class ChangeLockActivity : AppCompatActivity(), IScanBTManagerEvent {

    private var isPressed: Boolean = false
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    private var mLockBasicInfo: LockBasicInfo? = null
    private lateinit var selectedItem: ModelForData
    private var decryptedAccessKey = ""
    lateinit var mViewModel: ChangeLockViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private lateinit var mobileCredentialHermesCmdService: IMobileHermesCmdService
    private var lock: ILockScanInfo? = null
    private var info: IMobileCredentialScanInfo? = null
    private var iseoLock: ILock? = null
    private var REQUEST_CHECK_SETTINGS = 3
    private lateinit var binding: ActivityResetLockTimeBinding

    private val airbnk: AirBnkManager by inject()
    private val handler: ErrorHandler by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityResetLockTimeBinding.inflate(layoutInflater)
        (airbnk as? AndroidAirBnkManager)?.bind(this)
        setContentView(binding.root)
//        noInternet()
        initz()
        apiChecking()
        askPermission()
        clickListeners()
        observerInit()

    }

    private fun apiChecking() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        mViewModel.getCheckUser(sharePrefs.token, jsonObject).observe(this) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (!it.success) {
                logoutAndNavToDashboard(
                    it.message,
                    this@ChangeLockActivity
                )
            }

        }
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                ),
                50
            )
        }

    }


    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                    if (CommonValues.isBluetoothEnabled()) {
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }

                } else {
                    if (::selectedItem.isInitialized) {
                        Preferences.logLock(
                            internalId = selectedItem.internal_id,
                            provider = selectedItem.provider,
                            action = LockLogActionType.Maintenance,
                            message = "Encrypted key not found",
                            data = "Permission Denied"
                        )
                    }
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                    val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {

                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {

                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this@ChangeLockActivity,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null),
                        ), 10
                    )
                }
            })
    }

    @SuppressLint("MissingPermission")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            if (CommonValues.isNetworkAvailable(this)) {
                binding.progressLay.isVisible = true
                if (selectedItem.provider == CommonValues.keyless) {
                    initLock()
                } else if (selectedItem.provider == CommonValues.iseo) {
                    mViewModel.upgradeMaintenance(sharePrefs.token, selectedItem.lock_id)
                        .observe(this@ChangeLockActivity) {
                            scanIseoLock()
                        }
                }
            } else {
                defaultDialog(this, "Internet connectivity required", object : OnActionOK {
                    override fun onClickData() {
                    }
                })
            }

        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }


    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }

    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[ChangeLockViewModel::class.java]
        selectedItem = intent.getParcelableExtra("selectedItem")!!
        if (selectedItem.encrypted_key.isNotEmpty()) {
            val decryptedKey = CommonValues.decrypt(selectedItem.encrypted_key, sharePrefs.uuid)
            if (decryptedKey == null) {
                decryptedAccessKey = ""
            } else if (selectedItem.encrypted_key.isNotEmpty()) {
                decryptedAccessKey = decryptedKey
            } else {
                if (selectedItem.provider == CommonValues.keyless) {
                    decryptedAccessKey = selectedItem.access_key
                } else {
                    if (::selectedItem.isInitialized) {
                        Preferences.logLock(
                            internalId = selectedItem.internal_id,
                            provider = selectedItem.provider,
                            action = LockLogActionType.Maintenance,
                            message = "Lock Found",
                            data = ""
                        )
                    }
                    selectedItem
                }
//                else if (lockDetails.lock.provider == CommonValues.iseo){
//                    decryptedAccessKey = lockDetails.lock.messer_token
////                    decryptedAccessKey = lockDetails.lock.access_key
//                }
            }
        }

    }

    @SuppressLint("MissingPermission")
    private fun clickListeners() {

        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        if (isPressed) {
                            if (selectedItem.provider == CommonValues.keyless) {
                                mBluetoothLeScan!!.stopReceiver()
                                isScan = false
                                mBleScanCallback.finishScan()
                                mBleLockSdk?.disconnect()
                            } else if (selectedItem.provider == CommonValues.iseo) {
                                scanManagerService.stopScan()
                                disconnectIseoLock()
                            } else if (
                                selectedItem.provider == CommonValues.oji ||
                                selectedItem.provider == CommonValues.linko ||
                                selectedItem.provider == Constants.primeSource
                            ) {
                                TTLockClient.getDefault().stopBTService();
                            }
                        }

                        setResult(52)
                        finish()
                    }
                })
            )
        }


        binding.backBtn.setOnClickListener {
            finish()
        }

        binding.btnNext.setOnClickListener {
            if (CommonValues.isBluetoothEnabled()) {
                if (CommonValues.isNetworkAvailable(this)) {
                    binding.progressLay.isVisible = true
                    isPressed = true
                    if (selectedItem.provider == CommonValues.keyless) {
                        initLock()
                    } else if (selectedItem.provider == CommonValues.iseo) {
                        mViewModel.upgradeMaintenance(sharePrefs.token, selectedItem.lock_id)
                            .observe(this@ChangeLockActivity) {
                                scanIseoLock()
                            }
                    } else if (
                        selectedItem.provider == CommonValues.oji ||
                        selectedItem.provider == CommonValues.linko ||
                        selectedItem.provider == Constants.primeSource
                    ) {
                        intOjiLock()
                    } else if (selectedItem.provider == CommonValues.lockWise) {
                        initAirBnkLock()
                    }
                } else {
                    defaultDialog(this, "Internet connectivity required", object : OnActionOK {
                        override fun onClickData() {


                        }
                    })
                }
            } else {
                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
            }
        }
    }

    private fun intOjiLock() {
        TTLockClient.getDefault().startScanLock(object : ScanLockCallback {
            override fun onScanLockSuccess(device: ExtendedBluetoothDevice?) {
                if (selectedItem.unique_key.contains(device!!.name) || device.name.contains(
                        selectedItem.unique_key
                    )
                ) {
                    findLockDone()
                    TTLockClient.getDefault().stopScanLock()
                    selectedItem.log(
                        action = LockLogActionType.ScanLock,
                        message = "Scan Lock Successfully!",
                        data = decryptedAccessKey
                    )
                }
            }

            override fun onFail(error: LockError?) {
                selectedItem.log(
                    action = LockLogActionType.ScanError,
                    message = error.toString(),
                    data = decryptedAccessKey
                )
            }
        })

    }

    private fun findLockDone() {

        TTLockClient.getDefault().setLockTime(
            System.currentTimeMillis(),
            decryptedAccessKey,
            object : SetLockTimeCallback {
                override fun onFail(p0: LockError?) {
                    selectedItem.log(
                        action = LockLogActionType.SetTime,
                        message = p0!!.errorMsg.toString(),
                        data = decryptedAccessKey
                    )
                }

                override fun onSetTimeSuccess() {
                    selectedItem.log(
                        action = LockLogActionType.SetTime,
                        message = "Set Time Successfully",
                        data = decryptedAccessKey
                    )
                    openOjiLock()
                }

            })


    }

    private fun openOjiLock() {

        TTLockClient.getDefault()
            .controlLock(ControlAction.UNLOCK, decryptedAccessKey, object : ControlLockCallback {
                override fun onControlLockSuccess(controlLockResult: ControlLockResult) {
                    selectedItem.log(
                        action = LockLogActionType.Unlock,
                        message = "Unlock Successfully!",
                        data = decryptedAccessKey
                    )
                    apiLockHistory()
                    mViewModel.postBatteryPercentage(
                        sharePrefs.token,
                        controlLockResult.battery, selectedItem.internal_id
                    )

                    updateMaintenance()
                }

                override fun onFail(error: LockError) {
                    LogUtil.d("lockAction:$error")
                    selectedItem.log(
                        action = LockLogActionType.UnlockError,
                        message = error.toString(),
                        data = decryptedAccessKey
                    )
                }
            })
    }

    private fun updateMaintenance() {

        val jsonObject = JsonObject()
        jsonObject.addProperty("lockId", selectedItem.lock_id)

        mViewModel.updateMaintenanceStatus(sharePrefs.token, jsonObject, Preferences.userRole.get())
            .observe(this@ChangeLockActivity) {
                defaultDialog(
                    this@ChangeLockActivity,
                    getString(keyless.feature.common.R.string.battery_status_has_been_updated),
                    object : OnActionOK {
                        override fun onClickData() {
                            TTLockClient.getDefault().stopBTService();
                            setResult(52)
                            finish()
                        }
                    })
                selectedItem.log(
                    action = LockLogActionType.Maintenance,
                    message = "Authentication Successful",
                    data = it.message
                )
            }

    }

    private fun apiLockHistory() {

        val backup = arrayListOf(
            LockHistoryModel(
                device_model = CommonValues.getDeviceNameApi(),
                lockId = selectedItem.lock_id,
                mobile_id = sharePrefs.uuid,
                created_at = CommonValues.dateForLockHistory(),
                company_id = selectedItem.owner_id
            )
        )
        mViewModel.updateLogs(
            this@ChangeLockActivity,
            sharePrefs.token,
            backup
        ) {

        }
    }

    private fun scanIseoLock() {
        scanManagerService = ServiceProvider.scanManagerService
        mobileCredentialService = ServiceProvider.mobileCredentialService
        mobileCredentialHermesCmdService =
            ServiceProvider.mobileCredentialHermesCmdService
        scanManagerService.setScanBTManagerEvent(this)
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                scanManagerService.stopScan()
                try {
                    scanManagerService.startScanLock(false)
                } catch (e: V364SdkException) {

                }
            }
        }

        selectedItem.log(
            action = LockLogActionType.Maintenance,
            message = "Scanning Lock",
            data = ""
        )

    }

    private fun initLock() {
        mBleName = ArrayList<String>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)

    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }


    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CommonValues.ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            addAdapterItemRange(msg.obj as BleLockScanData)
                        } catch (e: Exception) {
                        }
                    }
                }
            }
        }
    }


    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun checkName(bleName: String): Boolean {
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }
        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(selectedItem.unique_key)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            connectLock()
        } else {
            isScan = true
        }
    }


    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            var lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)

    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
//            CommonValues.showMessage(
//                """
//                init sdk
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@ChangeLockActivity
//            )

        }

        override fun connect(resultBean: ResultBean<*>) {
            selectedItem.log(
                action = LockLogActionType.Connected,
                message = "Maintenance",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )

        }

        override fun authentication(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """ authentication ${
                    JSON.toJSONString(
                        resultBean,
                        SerializerFeature.WriteDateUseDateFormat
                    )
                }""", this@ChangeLockActivity
            )

            if (resultBean.isRet) {
                val mLockBasicInfo = resultBean.obj as (LockBasicInfo)

                var batteryCount = 1000
                if (mLockBasicInfo.battery in 0..25) {
                    batteryCount = 0
                } else if (mLockBasicInfo.battery in 26..50) {
                    batteryCount = 1
                } else if (mLockBasicInfo.battery in 51..75) {
                    batteryCount = 2
                } else if (mLockBasicInfo.battery > 75) {
                    batteryCount = 3
                }

                mViewModel.postBatteryPercentage(
                    sharePrefs.token,
                    batteryCount, selectedItem.internal_id
                )
                val lockTimeClass = LockTimeClass()
                val timestamp = Timestamp(System.currentTimeMillis())
                println(timestamp)
                lockTimeClass.lockTime = timestamp
                val dstClass = DSTClass()
//                dstClass.beginTime = Calendar.getInstance().time
//                dstClass.endTime = Calendar.getInstance().time
                lockTimeClass.dstClass = dstClass
                mBleLockSdk?.setLockTime(lockTimeClass)
                binding.progressLay.isVisible = false

                selectedItem.log(
                    action = LockLogActionType.Maintenance,
                    message = "Authentication Successful",
                    data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
                )
            } else {
                if (JSON.toJSONString(
                                    resultBean,
                                    SerializerFeature.WriteDateUseDateFormat
                                ).lowercase(Locale.getDefault()).contains("timeout")
                ) {
                    defaultDialog(
                        this@ChangeLockActivity,
                        getString(R.string.the_lock_could_not_be_connected),
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        })

                    selectedItem.log(
                        action = LockLogActionType.Maintenance,
                        message = "Authentication Failed",
                        data = JSON.toJSONString(
                            resultBean,
                            SerializerFeature.WriteDateUseDateFormat
                        )
                    )
                } else {
                    toast(
                        JSON.toJSONString(
                            resultBean,
                            SerializerFeature.WriteDateUseDateFormat
                        )
                    )
                }
            }
            binding.progressLay.isVisible = false

        }

        fun endTimeFunction(): Date {
            val sdf = SimpleDateFormat("MM/dd/yyyy HH:mm:ss")
            val currentDateandTime =
                Calendar.MONTH.toString() + "/" + Calendar.DAY_OF_MONTH + "/" + Calendar.YEAR + " 00:00:00"
            val date = sdf.parse(currentDateandTime)
            val calendar = Calendar.getInstance()
            calendar.time = date
            calendar.add(Calendar.MINUTE, 10)
            return calendar.time
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                disConnect 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
            selectedItem.log(
                action = LockLogActionType.Maintenance,
                message = "Disconnected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                registerLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockInfo 
                ${resultBean.obj}
                """.trimIndent(),
                this@ChangeLockActivity
            )

            val jsonObject = JsonObject()
            jsonObject.addProperty("lockId", selectedItem.lock_id)
            selectedItem.log(
                action = LockLogActionType.Maintenance,
                message = "Authentication Successful",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )


            mViewModel.updateMaintenanceStatus(sharePrefs.token, jsonObject, Preferences.userRole.get())
                .observe(this@ChangeLockActivity) {
                    defaultDialog(
                        this@ChangeLockActivity,
                        getString(keyless.feature.common.R.string.battery_status_has_been_updated),
                        object : OnActionOK {
                            override fun onClickData() {
                                mBluetoothLeScan!!.stopReceiver()
                                isScan = false
                                mBleLockSdk?.disconnect()
                                setResult(52)
                                finish()
                            }
                        })

                    selectedItem.log(
                        action = LockLogActionType.Maintenance,
                        message = "Authentication Successful",
                        data = it.message
                    )
                }
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockInfo 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
            val lockBleOpenData = LockBleOpenData()
            val nowTime = Calendar.getInstance()
            nowTime.add(Calendar.MINUTE, -1)
            lockBleOpenData.beginTime = TimeUtils.dateFromNotYMDHMS(
                TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })
            nowTime.add(Calendar.HOUR_OF_DAY, 1)
            lockBleOpenData.endTime = TimeUtils.dateFromNotYMDHMS(
                TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })
            mBleLockSdk?.bleOpenLock(lockBleOpenData)
            mBleLockSdk?.getLockInfo()
            CommonValues.showMessage(
                """
                setLockTime 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
            selectedItem.log(
                action = LockLogActionType.Maintenance,
                message = "Setting Lock Time",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                setLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                checkLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockStatus 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                updateLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )

//            if (resultBean!!.isRet) {
//                val jsonObject = JsonObject()
//                jsonObject.addProperty("card_id", dataMain.internal_id)
//                jsonObject.addProperty("start_time", startDateApi)
//                jsonObject.addProperty("end_time", endDateApi)
//                jsonObject.addProperty("lock_id", selectedItem.lock._id)
//                if (searchUser) {
//                    jsonObject.addProperty("user_name", userNameApi)
//                    jsonObject.addProperty("email", emailForApi)
//                    jsonObject.addProperty("mobile_number", phoneApi)
//                } else {
//                    jsonObject.addProperty("user_name", etUserName.text.toString().trim())
//                    jsonObject.addProperty("email", etEmail.text.toString().trim())
//                    jsonObject.addProperty("mobile_number", etMobileNumber.text.toString().trim())
//                }
//
//                mViewModel.configureCard(sharePrefs.token, jsonObject)
//                    .observe(this@ChangeLockActivity) {
//                        if (it.success) {
//                            mBleLockSdk?.disconnect()
//                            setResult(151)
//                            finish()
//                        } else {
//                            toast(it.message)
//                        }
//                    }
//            }
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                bleOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
            val backup = arrayListOf(
                LockHistoryModel(
                    "1",
                    CommonValues.getDeviceNameApi(),
                    selectedItem.lock_id,
                    sharePrefs.uuid,
                    CommonValues.dateForLockHistory(),
                    selectedItem.owner_id
                )
            )
            mViewModel.updateLogs(
                this@ChangeLockActivity,
                sharePrefs.token,
                backup
            ) {

            }

            selectedItem.log(
                action = LockLogActionType.Maintenance,
                message = "Successfully Opened",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )

            binding.progressLay.isVisible = false

        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                pincodeOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setCalendar 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockSerialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readCardByCylinder 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )

        }

        override fun onReport(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                onReport 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                get reader info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set reader serialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                get keyboard info 
                ${resultBean.obj}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set keyboard info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(), this@ChangeLockActivity
            )
        }

        override fun disPlay(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                Bluetooth Data 
                ${resultBean.obj as String}
                """.trimIndent(),
                this@ChangeLockActivity
            )
        }

        override fun setTempCard(p0: ResultBean<*>?) {


        }

        override fun deleteTempCard(p0: ResultBean<*>?) {

        }

        override fun findTempCard(p0: ResultBean<*>?) {

        }

    }

    override fun onScanStarted() {


    }

    override fun onScanStopped() {

    }

    override fun onF9000Found(p0: IKeyScanInfo?) {

    }

    override fun onLockFound(p0: ILockScanInfo?, p1: IMobileCredentialScanInfo?) {
        runOnUiThread {
            if (p0!!.lockName == selectedItem.lock_uid) {
                scanManagerService.stopScan()
                lock = p0
                info = p1
                selectedItem.log(
                    action = LockLogActionType.Maintenance,
                    message = "Lock Found",
                    data = JSON.toJSONString(
                        p0,
                        SerializerFeature.WriteDateUseDateFormat
                    ) + " && " + JSON.toJSONString(p1, SerializerFeature.WriteDateUseDateFormat)
                )
                openISEOLock()
            }
        }
    }

    private fun openISEOLock() {
        refreshCredentials()
        CoroutineScope(Dispatchers.Main).launch {
            try {
                var response: ILockResponse? = null

                withContext(Dispatchers.IO) {
                    iseoLock = ServiceProvider.mobileCredentialConnLockService.connect(lock)
                    mobileCredentialHermesCmdService.enterInMaintenanceMode(iseoLock)
                    mobileCredentialHermesCmdService.readDeviceInfo(iseoLock)
                    mobileCredentialHermesCmdService.writeClock(iseoLock)
                    response = ServiceProvider.mobileCredentialUserCmdService.openLock(iseoLock)
                    scanManagerService.stopScan()
                    disconnectIseoLock()

                    selectedItem.log(
                        action = LockLogActionType.Maintenance,
                        message = "Opened Successfully",
                        data = response.toString()
                    )

                    CoroutineScope(Dispatchers.Main).launch {
                        mViewModel.downgradeMaintenance(sharePrefs.token, selectedItem.lock_id)
                            .observe(this@ChangeLockActivity) {
                                val battery = getBattery(response!!.batteryStatus)
                                mViewModel.postBatteryPercentage(
                                    sharePrefs.token,
                                    battery, selectedItem.internal_id
                                )

                                val backup = arrayListOf(
                                    LockHistoryModel(
                                        "1",
                                        CommonValues.getDeviceNameApi(),
                                        selectedItem.lock_id,
                                        sharePrefs.uuid,
                                        CommonValues.dateForLockHistory(),
                                        selectedItem.owner_id
                                    )
                                )

                                selectedItem.log(
                                    action = LockLogActionType.Maintenance,
                                    message = "Downgrade Maintenance",
                                    data = it.message
                                )

                                mViewModel.updateLogs(
                                    this@ChangeLockActivity,
                                    sharePrefs.token,
                                    backup
                                ) {
                                }
                                val jsonObject = JsonObject()
                                jsonObject.addProperty("lockId", selectedItem.lock_id)
                                mViewModel.updateMaintenanceStatus(
                                    sharePrefs.token,
                                    jsonObject,
                                    Preferences.userRole.get()
                                )
                                    .observe(this@ChangeLockActivity) {
                                        defaultDialog(
                                            this@ChangeLockActivity,
                                            getString(keyless.feature.common.R.string.battery_status_has_been_updated),
                                            object : OnActionOK {
                                                override fun onClickData() {
                                                    scanManagerService.stopScan()
                                                    disconnectIseoLock()
                                                    setResult(52)
                                                    finish()
                                                }
                                            })
                                    }
                            }

                    }


                }
            } catch (e: V364SdkException) {
                if (!isFinishing) {
                    defaultDialog(
                        this@ChangeLockActivity,
                        getString(
                            keyless.feature.common.R.string.this_lock_is_either_not_in_range_or_connected_to_another_device
                        ),
                        object : OnActionOK {
                            override fun onClickData() {
                                finish()
                            }
                        })
                }
                e.printStackTrace()
            }
        }
    }


    private fun getBattery(batteryStatus: ILockBatteryStatus): Int {
        var batteryCount = 1000

        when (batteryStatus.batteryStatus.name) {
            "BATTERY_OK" -> {
                batteryCount = 3
            }

            "BATTERY_LOW" -> {
                batteryCount = 2
            }

            "BATTERY_VERYLOW" -> {
                batteryCount = 1
            }

            "BATTERY_EXTRALOW" -> {
                batteryCount = 0
            }

        }

        return batteryCount
    }

    private fun refreshCredentials() {
        scanManagerService = ServiceProvider.scanManagerService
        mobileCredentialService = ServiceProvider.mobileCredentialService
        CoroutineScope(Dispatchers.Main).launch {
//            updateStateNeutral("REFRESHING")
            delay(500)
            withContext(Dispatchers.IO) {
                try {
//                    val url = "https://sirademo.iseov364.com"
                    mobileCredentialService.refreshMobileCredential(
                        sharePrefs.iseoUrl,
                        sharePrefs.plantName
                    )
                    val mobileCredentialInfo: IMobileCredentialsInfo =
                        mobileCredentialService.getMobileCredentials(sharePrefs.iseoUrl)

                    val message = "ResCode:" + mobileCredentialInfo.resCode + "\n" +
                            "ResultDetails:" + mobileCredentialInfo.resultDetails + "\n" +
                            "ValidityStart:" + mobileCredentialInfo.validityStart + "\n" +
                            "ValidityEnd:" + mobileCredentialInfo.validityEnd + "\n" +
                            "ValidationEnd:" + mobileCredentialInfo.validationEnd + "\n" +
                            "Credential count:" + mobileCredentialInfo.mobileCredentials.size
                    withContext(Dispatchers.Main) {
                        Log.e("REFRESH SUCCESS\n$message", "")

                    }
                    selectedItem.log(
                        action = LockLogActionType.Maintenance,
                        message = "Credentials Successfully Refresh",
                        data = ""
                    )

//                    getSharedPreferences("prefs", Context.MODE_PRIVATE).edit().putString("url", url).apply()
                } catch (e: V364SdkException) {
                    withContext(Dispatchers.Main) {
                        selectedItem.log(
                            action = LockLogActionType.Maintenance,
                            message = "ISEO Credential is not valid",
                            data = e.toString()
                        )
//                        updateState(e.v364SdkErrorCode)
                    }
//                    Log.e(TAG, "startService: ", e)
                }
            }
        }

    }


    private fun disconnectIseoLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                withContext(Dispatchers.IO) {
                    mobileCredentialHermesCmdService.disconnect(iseoLock)
                    selectedItem.log(
                        action = LockLogActionType.Maintenance,
                        message = "Disconnected",
                        data = ""
                    )
                }
            } catch (e: V364SdkException) {
            }
        }

    }

    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {

    }

    private fun initAirBnkLock() {
        lifecycleScope.launch {
            handler.async(
                onError = {
                    selectedItem.log(
                        action = LockLogActionType.SetTime,
                        message = it.toString(),
                        data = decryptedAccessKey
                    )
                    appToast(it.message ?: "Error happened")
                    airbnk.disconnect()
                },
                work = {
                    airbnk.connect(decryptedAccessKey)
                    resetAirBnkTime()
                }
            )
        }
    }

    private suspend fun resetAirBnkTime() {
        selectedItem.log(
            action = LockLogActionType.ScanLock,
            message = "Scan Lock Successfully",
            data = decryptedAccessKey
        )
        airbnk.getByKey(decryptedAccessKey).setLockTime(Clock.System.now().toEpochMilliseconds())

        selectedItem.log(
            action = LockLogActionType.SetTime,
            message = "Set Time Successfully",
            data = decryptedAccessKey
        )
        airbnk.getByKey(decryptedAccessKey).unlock()
        delay(8000)
        airbnk.getByKey(decryptedAccessKey).lock()

        apiLockHistory()

        mViewModel.postBatteryPercentage(
            sharePrefs.token,
            airbnk.getByKey(decryptedAccessKey).batteryLevel(),
            selectedItem.internal_id
        )

        updateMaintenance()
    }
}