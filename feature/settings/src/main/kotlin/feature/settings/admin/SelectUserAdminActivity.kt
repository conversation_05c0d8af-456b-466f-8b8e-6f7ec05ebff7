package feature.settings.admin

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.JsonObject
import data.common.preferences.Preferences
import data.network.android.models.UserModelAdmin
import data.utils.android.CommonValues
import data.utils.android.hideKeyboard
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.application.App.DASHBOARD_ACTIVITY_CLASS
import feature.common.dialogs.ProgressDialogUtils
import keyless.feature.settings.databinding.ActivitySelectUserAdminBinding

class SelectUserAdminActivity : AppCompatActivity(), AdapterUsersAdmin.ClickToLogin {

    lateinit var adapterUser: AdapterUsersAdmin
    lateinit var mViewModel: AdminUsersViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    var userType = ""
    private lateinit var binding: ActivitySelectUserAdminBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySelectUserAdminBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setAdapter()
        observerInit()
        clickListeners()
    }

    override fun clickLogin(model: UserModelAdmin) {
        mViewModel.hitLoginAsUser(sharePrefs.token, model._id).observe(this) {
            Preferences.setAdminLogin(true)
            CommonValues.adminTempToken = "Bearer " + it.token
            Preferences.setAdminTempToken("Bearer " + it.token)
            CommonValues.adminTempUserType = it.userType
            Preferences.setAdminTempUserType(it.userType)
            CommonValues.adminTempRole = it.role
            Preferences.setAdminTempRole(it.role)
            Preferences.isLoggedIn.set(true)
            startActivityForResult(
                Intent(
                    this,
                    DASHBOARD_ACTIVITY_CLASS
                    ).putExtra("runSplash", false),
                35
            )
            toast(it.message)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 35 && resultCode == RESULT_OK) {
            hideKeyboard()
            binding.svLock.clearFocus()
            Preferences.setAdminLogin(false)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun clickListeners() {
//        scrollView.setOnTouchListener { v, event ->
//            //show dialog here
//            if (svLock.requestFocus()) {
//                hideKeyboard()
//                performSearch()
//                svLock.clearFocus()
//            }
//            false
//        }

        val textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                val trimmedText = s?.toString()?.trim()
                if (s.toString() != trimmedText) {
                    s?.replace(0, s.length, trimmedText)
                }
            }
        }

        binding.svLock.addTextChangedListener(textWatcher)

        binding.backBtn.setOnClickListener {
            finish()
        }
        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.stopSearch.isVisible = false
            binding.svLock.setText("")
            val array = ArrayList<UserModelAdmin>()
            adapterUser.updateList(array)
            binding.noDataView.isVisible = true
//            apiInitilization("")
        }

        binding.svLock.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    binding.stopSearch.isVisible = true
                    return@OnEditorActionListener true
                }
                false
            }
        )
    }

    private fun performSearch() {
        hideKeyboard()
        apiInitilization(binding.svLock.text.toString().trim())
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[AdminUsersViewModel::class.java]
        if (intent.getStringExtra("type") == "0") {
            binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_project_manager_user)
            userType = "Integrator"
        } else {
            binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_guest_user)
            userType = "Guest"
        }
    }

    private fun apiInitilization(keyword: String) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("keyword", keyword)
        jsonObject.addProperty("user_type", userType)
        mViewModel.getAllUsers(sharePrefs.token, jsonObject).observe(this) {
            if (it.success) {
                val array = ArrayList<UserModelAdmin>()
                it.user.role = it.role
                array.add(it.user)
                adapterUser.updateList(array)
                CommonValues.adminTempUserId = it.user._id
                binding.noDataView.isVisible = false
            } else {
                val array = ArrayList<UserModelAdmin>()
                val model = UserModelAdmin()
                array.add(model)
                adapterUser.updateList(array)
                binding.noDataView.isVisible = true
                toast(it.message)
            }
        }
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            val array = ArrayList<UserModelAdmin>()
            adapterUser.updateList(array)
            binding.noDataView.isVisible = true
            toast(it)
        }
    }

    private fun setAdapter() {
        binding.rvUsers.layoutManager = LinearLayoutManager(this)
        adapterUser = AdapterUsersAdmin(this)
        binding.rvUsers.adapter = adapterUser
    }
}