package feature.settings.admin

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import com.google.gson.JsonObject
import data.network.android.ApiUtils
import data.keyless.authentication.models.SendOtpResponse
import data.network.android.models.GetAdminUserModel
import presentation.common.domain.repositories.ErrorMessageHandler

class AdminUsersViewModel : ViewModel() {

    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress

    fun getAllUsers(token: String, jsonObject: JsonObject) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.hitGetAllUsers(token, jsonObject, {
            it as GetAdminUserModel
            if (it.success) {
                _progress.value = false
                emit(it)
            } else {
                _progress.value = false
                _error.value = it.message
            }
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun hitLoginAsUser(token: String, _id: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.hitLoginAsUser(token, _id, {
            it as SendOtpResponse
            if (it.isSuccess) {
                _progress.value = false
                emit(it)
            } else {
                _progress.value = false
                _error.value = it.message
            }
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it)
        })
    }
}