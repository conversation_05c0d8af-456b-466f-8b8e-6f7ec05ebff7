<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <ImageView
        android:id="@+id/backBtn"
        style="@style/ImageMirror"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:padding="20dp"
        android:src="@drawable/iv_back_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleToolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/configure_cards"
        android:textColor="@color/white"
        android:textSize="18dp"
        app:layout_constraintBottom_toBottomOf="@+id/backBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/backBtn" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:id="@+id/addRayonicsCard"
        android:src="@drawable/iv_add_access"
        app:layout_constraintBottom_toBottomOf="@+id/titleToolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/titleToolbar" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backBtn"
        app:layout_constraintVertical_bias="0.0">


        <EditText
            android:id="@+id/svLock"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_btn_round"
            android:backgroundTint="@color/bg_edit_grey"
            android:drawableStart="@drawable/ic_baseline_search_24"
            android:drawablePadding="10dp"
            android:ellipsize="end"
            android:visibility="gone"
            style="@style/mirrorText"
            android:focusableInTouchMode="true"
            android:fontFamily="@font/poppins_regular_400"
            android:hint="@string/search_by_lock_name"
            android:imeOptions="actionSearch"
            android:includeFontPadding="false"
            android:padding="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingRight="30dp"
            android:singleLine="true"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvAllCards"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/svLock"
            app:layout_constraintVertical_bias="0.0" />


        <ImageView
            android:id="@+id/stopSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:src="@drawable/iv_cross_black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/svLock"
            app:layout_constraintEnd_toEndOf="@+id/svLock"
            app:layout_constraintTop_toTopOf="@+id/svLock" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@drawable/img_no_internet"
            android:drawablePadding="15dp"
            android:visibility="gone"
            android:id="@+id/noInternetLayout"
            android:fontFamily="@font/poppins_medium_500"
            android:src="@drawable/common_google_signin_btn_icon_dark_normal"
            android:text="@string/no_internet_connection"
            android:textColor="@color/black"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/layNoData"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/iv_no_managed_card" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/no_configured_card_found"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_lock" />



            <TextView
                android:id="@+id/btnConfigureCard"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="60dp"
                android:layout_marginTop="43dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="41dp"
                android:includeFontPadding="false"
                android:background="@drawable/bg_btn_round"
                android:fontFamily="@font/poppins_medium_500"
                android:gravity="center"
                android:visibility="gone"
                android:text="@string/configure_new_card"
                android:textColor="@color/black"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>


    <ProgressBar
        android:id="@+id/progress_pagination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:theme="@style/progressBarBlue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />





    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressBar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>





</androidx.constraintlayout.widget.ConstraintLayout>