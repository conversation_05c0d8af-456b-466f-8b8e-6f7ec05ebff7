package feature.settings.maintenance.scan

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import core.lock.common.models.LockBrand
import data.common.preferences.Preferences
import data.lock.common.lock.models.lock.Lock
import data.user.home.repositories.locks
import feature.common.compose.screens.AppTitledPage
import feature.common.compose.surface.AppColumn
import feature.common.compose.text.AppBodyText
import feature.common.compose.text.AppTextField
import keyless.feature.settings.maintenance.R

@Composable
internal fun VerifyNearByLockScreen(
    modifier: Modifier = Modifier,
    brands: List<LockBrand>,
    onLockSelect: (Lock) -> Unit,
    onBackClick: () -> Unit
) {
    var search by remember { mutableStateOf("") }
    var locks by remember { mutableStateOf<List<Lock>>(listOf()) }
    val filteredLocks = remember(locks, search) {
        locks.filter { it.name.lowercase().contains(search.lowercase()) }
    }

    LaunchedEffect(Unit) {
        locks = Preferences.locks().filter { brands.map { it.toString().lowercase() }.contains(it.provider.lowercase()) }
    }

    AppTitledPage(
        modifier = modifier
            .fillMaxSize(),
        title = stringResource(id = R.string.select_lock),
        isBackEnabled = true,
        onBackPress = onBackClick
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                AppTextField(
                    value = search,
                    hint = stringResource(id = keyless.feature.common.R.string.search_by_lock_name),
                    onValueChange = { search = it },
                    leading = { Icon(imageVector = Icons.Default.Search, contentDescription = "") },
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                     trailing = {
                        IconButton(
                            onClick = { search = "" }
                        ) {
                            Icon(imageVector = Icons.Default.Close, contentDescription = "")
                        }
                    }
                )
            }

            for(lock in filteredLocks) {
                item {
                    LockItem(lock = lock, onLockSelect = onLockSelect)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LockItem(
    modifier: Modifier = Modifier,
    lock: Lock,
    onLockSelect: (Lock) -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = colorResource(id = keyless.feature.common.R.color.grey_round)
        ),
        onClick = { onLockSelect(lock) }
    ) {
        AppColumn(
            modifier = Modifier
                .padding(16.dp),
        ) {
            AppBodyText(
                text = lock.name,
                weight = FontWeight.Bold
            )

            AppBodyText(text = lock.property.name)

            if (lock.property.apartmentNumber.isBlank()) {
                AppBodyText(
                    text = lock.property.floor + " " + stringResource(id = keyless.feature.common.R.string.txt_floor)
                )
            } else {
                AppBodyText(
                    text = lock.property.apartmentNumber + ", " +
                            lock.property.floor + " " +
                            stringResource(id = keyless.feature.common.R.string.txt_floor)
                )
            }
        }
    }
}