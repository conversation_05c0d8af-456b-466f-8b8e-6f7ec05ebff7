package feature.settings.maintenance.scan

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.lifecycleScope
import core.common.error.KError
import core.lock.common.models.LockBrand
import core.locks.manager.LocksManager
import core.permissions.manager.AndroidBluetoothManager
import core.permissions.manager.BluetoothManager
import data.common.preferences.Constants
import data.lock.common.lock.models.lock.Lock
import domain.common.ErrorHandler
import feature.common.compose.theme.AppTheme
import feature.common.dialogs.appToast
import feature.common.dialogs.scanningDialog
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject

class VerifyNearByLockActivity : ComponentActivity() {

    private val lockManager: LocksManager by inject()
    private val handler: <PERSON>rrorHandler by inject()
    private val bluetooth: BluetoothManager by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val brands = intent
            .extras
            ?.getStringArray("brands")?.map { LockBrand.fromString(it) }
            ?: LockBrand.values().toList()

        (bluetooth as? AndroidBluetoothManager)?.bind(this)

        setContent {
            AppTheme {
                VerifyNearByLockScreen(
                    onBackClick = { onBackPressedDispatcher.onBackPressed() },
                    brands = brands,
                    onLockSelect = ::onLockSelect
                )
            }
        }
    }

    private fun onLockSelect(lock: Lock) {
        var scanJob: Job? = null
        val dialog = scanningDialog(onCancel = { scanJob?.cancel() })

        scanJob = lifecycleScope.launch {
            handler.async(
                onError = {
                    appToast(it.message ?: "Error Happened")
                    dialog.dismiss()
                }
            ) {
                if (!lockManager.bluetoothCheck()) throw KError.Info("Bluetooth is not enabled")

                if (lock.provider == Constants.lockWise) {
                    scanAirBnk(lock)
                } else {
                    scanLocks(lock)
                }

                dialog.dismiss()

                val result = Intent()
                result.putExtra(VERIFY_NEARBY_LOCK_UNIQUE_KEY, lock.uniqueKey)
                setResult(RESULT_OK, result)
                finish()
            }
        }
    }

    private suspend fun scanAirBnk(lock: Lock) {
        lockManager.scan(bluetoothNames = listOf(lock.uniqueKey)).first { it.name == lock.uniqueKey }
    }

    private suspend fun scanLocks(lock: Lock) {
        lockManager.scan().first { it.name == lock.uniqueKey }
    }

    companion object {
        const val VERIFY_NEARBY_LOCK_UNIQUE_REQUEST_CODE = 774
        const val VERIFY_NEARBY_LOCK_UNIQUE_KEY = "VERIFY_NEARBY_LOCK_ACTIVITY_KEY"
    }
}