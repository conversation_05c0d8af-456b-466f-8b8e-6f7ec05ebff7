package feature.settings.support.diagnostics

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import data.common.preferences.Preferences
import data.user.home.repositories.locks
import domain.settings.support.models.SideEffect
import feature.common.adapters.AdapterLock
import feature.common.adapters.SelectLockAdapter
import feature.common.adapters.toAdapter
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import presentation.common.feature.components.StatusListener
import presentation.common.feature.components.StatusListenerImpl
import keyless.feature.settings.support.databinding.ActivityLockSelectScreenBinding
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class DiagnosticsScreen : AppCompatActivity(), SelectLockAdapter.ClickToConnect,
    StatusListener by StatusListenerImpl() {


    private lateinit var adapterLock: SelectLockAdapter
    lateinit var viewModel: DiagnosticsViewModel
    private lateinit var binding: ActivityLockSelectScreenBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLockSelectScreenBinding.inflate(layoutInflater)
        setContentView(binding.root)
        viewModel = ViewModelProvider(this)[DiagnosticsViewModel::class.java]
        registerStatusListener(this)
        lifecycleScope.launch { setAdapter() }
        clickListeners()
    }

    private fun clickListeners() {
        binding.svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                adapterLock.let {
                    adapterLock.filter.filter(p0.toString())
                    binding.stopSearch.isVisible = p0.toString().isNotEmpty()
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearch.setOnClickListener {
            binding.svLock.setText("")
            adapterLock.filter.filter("")
        }

        binding.backBtn.setOnClickListener {
            finish()
        }

        viewModel
            .sideEffects
            .onEach {
                when(it) {
                    is SideEffect.SendLogsResponse -> {
                        defaultDialog(
                            this@DiagnosticsScreen,
                            it.message,
                            object : OnActionOK { override fun onClickData() {} }
                        )
                    }

                    is SideEffect.NoLogsFound -> {
                        defaultDialog(
                            this@DiagnosticsScreen,
                            getString(keyless.feature.common.R.string.no_diagnostic_data_found_for_this_lock),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            }
                        )
                    }
                }
            }
            .launchIn(lifecycleScope)
    }

    private suspend fun setAdapter() {
        val mainList = ArrayList<AdapterLock>()

        for (i in Preferences.locks()) {
                mainList.add(i.toAdapter())
        }

        if (mainList.size > 0) {
            binding.rvRayonicLocks.layoutManager = LinearLayoutManager(this)
            adapterLock = SelectLockAdapter(mainList, this)
            binding.rvRayonicLocks.adapter = adapterLock
            binding.layNoDataFullPage.isVisible = false
            binding.lockListView.isVisible = true
        } else {
            binding.layNoDataFullPage.isVisible = true
            binding.lockListView.isVisible = false
        }
    }

    override fun clickConnecting(locksModel: AdapterLock) {
        viewModel.sendErrorLogs(locksModel.internalId)
    }
}