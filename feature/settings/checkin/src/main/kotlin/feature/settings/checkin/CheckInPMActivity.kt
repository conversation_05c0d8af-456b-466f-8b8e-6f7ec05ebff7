package feature.settings.checkin

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.widget.Filter
import android.widget.Filterable
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import domain.settings.checkin.models.ScreenEvent
import domain.settings.checkin.models.ScreenState
import feature.common.device.hideKeyboard
import presentation.common.feature.components.StatusListener
import presentation.common.feature.components.StatusListenerImpl
import keyless.feature.settings.checkin.databinding.ActivityCheckInPmactivityBinding
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.androidx.viewmodel.ext.android.viewModel

class CheckInPMActivity : AppCompatActivity(), AdapterCheckInPM.ClickCheckIn, Filterable,
    StatusListener by StatusListenerImpl() {

    lateinit var binding: ActivityCheckInPmactivityBinding
    lateinit var adapterCheckIn: AdapterCheckInPM
    private val viewModel: PMViewModel by viewModel()
    private var screenState: ScreenState? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerStatusListener(this)
        binding = ActivityCheckInPmactivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setAdapter()
        initz()
        clickListeners()
        collectState()
    }

    private fun setAdapter() {
        binding.rvCheckIn.layoutManager = LinearLayoutManager(this)
        adapterCheckIn = AdapterCheckInPM(this)
        binding.rvCheckIn.adapter = adapterCheckIn
    }

    override fun clickingCheckIn(modelAssignCheckIns: ModelAssignCheckIns) {
        startActivityForResult(
            Intent(this, CheckInStartActivity::class.java).putExtra("bookingNumber", modelAssignCheckIns.bookingNumber),
            100
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 90) {
            finish()
        }
    }

    private fun clickListeners() {
        binding.viewBack.setOnClickListener {
            finish()
        }

        binding.svCheckIn.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                filter.filter(p0.toString())
                binding.stopSearch.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.svCheckIn.setText("")
            viewModel.onEvent(ScreenEvent.Init)
        }
    }

    private fun initz() {
        viewModel.onEvent(ScreenEvent.Init)
    }

    override fun getFilter(): Filter {
        var listFiltered: List<ModelAssignCheckIns>

        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                val checkins = screenState?.checkins?.map { it.toModel() } ?: emptyList()
                listFiltered = if (charString.isEmpty()) {
                    checkins
                } else {
                    checkins
                        .filter {
                            it.fullName.contains(constraint!!, true) or
                                    it.lastName.contains(constraint, true) or
                                    it.bookingNumber.contains(constraint, true) or
                                    it.lastName.contains(constraint, true)
                        }

                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as List<ModelAssignCheckIns>
                }
                binding.noStaffMember.isVisible = listFiltered.isEmpty()
                adapterCheckIn.updateValues(listFiltered)
            }
        }
    }

    private fun noData() {
        binding.noStaffMember.isVisible = true
        binding.svCheckIn.isVisible = false
        binding.rvCheckIn.isVisible = false
        binding.stopSearch.isVisible = false
    }

    private fun collectState() {
        viewModel
            .screenStream
            .onEach { runCatching { onState(it) } }
            .launchIn(lifecycleScope)
    }

    private fun onState(screenState: ScreenState) {
        this.screenState = screenState
        if (screenState.checkins.isNotEmpty()) {
            binding.svCheckIn.isVisible = true
            binding.rvCheckIn.isVisible = true
            val list = ArrayList<ModelAssignCheckIns>()
            for (i in screenState.checkins) {
                val model = ModelAssignCheckIns(
                    lastName = i.lastName,
                    validFrom = i.validFrom,
                    validTo = i.validTo,
                    bookingNumber = i.bookingNumber,
                    firstName = i.firstName,
                    lockName = i.lockName,
                    fullName = i.firstName + " " + i.lastName,
                )

                list.add(model)
            }
            adapterCheckIn.updateValues(list)
            binding.noStaffMember.isVisible = screenState.checkins.isEmpty()
        } else {
            noData()
        }
    }
}