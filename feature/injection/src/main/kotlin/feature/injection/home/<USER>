package feature.injection.home

import core.monitoring.common.repository.Logger
import data.user.home.local.UserHomeLocal
import data.user.home.local.UserHomeLocalImpl
import data.user.home.remote.UserHomeRemote
import data.user.home.remote.UserHomeRemoteImpl
import data.user.home.repositories.UserHomeRepository
import data.user.home.repositories.UserHomeRepositoryImpl
import domain.home.ViewModel
import domain.home.usecases.HomeUseCases
import keyless.feature.injection.ConfigValues
import org.koin.dsl.module

internal val homeDomainModule = module {
    single<UserHomeRepository> {
        val userHomeRemote: UserHomeRemote = UserHomeRemoteImpl(
            logger = get(),
            hostname = ConfigValues.baseUrl,
            client = get()
        )
        val userHomeLocal: UserHomeLocal = UserHomeLocalImpl(
            driver = get(),
            logger = get()
        )
        UserHomeRepositoryImpl(
            logger = get(),
            remote = userHomeRemote,
            local = userHomeLocal
        )
    }
    single<ViewModel> { homeViewModel(get(), get()) }
}

fun useCases(userHomeRepository: UserHomeRepository, logger: Logger): HomeUseCases {
    return HomeUseCases(
        userHomeRepository = userHomeRepository,
        logger = logger
    )
}

private fun homeViewModel(userHomeRepository: UserHomeRepository, logger: Logger): ViewModel {
    return ViewModel(
        useCases = useCases(userHomeRepository, logger)
    )
}