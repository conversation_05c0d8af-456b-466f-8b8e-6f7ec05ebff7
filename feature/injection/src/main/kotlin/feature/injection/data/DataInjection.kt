package feature.injection.data

import android.content.Context
import app.cash.sqldelight.async.coroutines.synchronous
import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.android.AndroidSqliteDriver
import core.caching.AndroidKeyValueCache
import core.caching.KeyValueCache
import core.common.coroutines.AbstractCoroutineDispatcher
import core.common.coroutines.CoroutineDispatchers
import core.common.lifecycle.Lifecycle
import core.common.status.CommonStatus
import core.common.status.StatusRepository
import core.http.client.HttpClient
import core.http.client.ktor.KtorHttpClient
import core.http.download.AndroidDownloader
import core.http.download.Downloader
import core.location.common.LocationRepository
import core.location.google.AndroidLocationRepository
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.AndroidAirBnkManager
import core.lock.iseo.AndroidIseoManager
import core.lock.iseo.IseoManager
import core.lock.mst.AndroidMSTManager
import core.lock.mst.MSTManager
import core.lock.nordicfirmware.NordicFirmwareRepository
import core.lock.rayonics.AndroidRayonicsManager
import core.lock.rayonics.RayonicsManager
import core.lock.ttlock.AndroidTTLockManager
import core.lock.ttlock.TTLockManager
import core.locks.logs.impl.DefaultLockLogsRemoteRepository
import core.locks.logs.impl.DefaultLockLogsRepository
import core.locks.logs.repostiories.LockLogsRepository
import core.locks.manager.LocksManager
import core.monitoring.common.repository.Logger
import core.monitoring.firebase.FirebaseLogger
import core.permissions.manager.AndroidBluetoothManager
import core.permissions.manager.AndroidNetworkManager
import core.permissions.manager.AndroidPermissionsManager
import core.permissions.manager.BluetoothManager
import core.permissions.manager.NetworkManager
import core.permissions.manager.PermissionsManager
import core.regula.documents.AndroidRegulaDocuments
import core.regula.documents.RegulaDocuments
import core.regula.documents.RegulaFace
import core.regula.face.AndroidRegulaFace
import data.common.preferences.Constants
import data.database.extensions.createOrMigrate
import data.keyless.authentication.AuthenticationRepository
import data.lock.common.logs.DefaultLockLogsLocalRepository
import data.tedee.repositories.TedeeRepository
import feature.injection.database.applicationContext
import io.ktor.client.engine.android.Android
import keyless.data.database.Keyless
import keyless.feature.injection.ConfigValues
import keyless.feature.injection.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.koin.core.qualifier.named
import org.koin.dsl.module

internal val dataInjection = module {
    single<Logger> {
        FirebaseLogger(
            appContext = get(),
            isDebugging = true,
            filteredLogs = emptyList()
        )
    }

    single<KeyValueCache>(named("regular")) {
        AndroidKeyValueCache(
            applicationContext = get(),
            logger = get(),
            name = "KEYLESS_SAVE"
        )
    }
    single<KeyValueCache>(named("old")) {
        val name = "${get<Context>().packageName}.KeyValueStorage"
        AndroidKeyValueCache(
            applicationContext = get(),
            logger = get(),
            name = "$name.KeyValueStorage"
        )
    }

    single<KeyValueCache>(named("persistent")) {
        AndroidKeyValueCache(
            applicationContext = get(),
            name = "KEYLESS_SAVE_MANAGER",
            logger = get()
        )
    }

    single<KeyValueCache> {
        get(named("regular"))
    }

    single<StatusRepository> { CommonStatus(get()) }

    single<HttpClient> {
        KtorHttpClient(
            log = get(),
            engine = Android.create {},
            unsafeEngine = Android.create { },
        )
    }

    single<CoroutineScope>(named(Constants.appCoroutineScopeName)) { CoroutineScope(Dispatchers.Main) }

    single<SqlDriver> {
        AndroidSqliteDriver(Keyless.Schema.synchronous(), applicationContext, "Keyless.db").apply {
            runCatching { runBlocking { Keyless.Schema.createOrMigrate(this@apply) } }
        }
    }

    single<LockLogsRepository> {
        val remote = DefaultLockLogsRemoteRepository(
            client = get(),
            logger = get(),
            hostname = ConfigValues.baseUrl
        )
        val local = DefaultLockLogsLocalRepository(
            sqlDriver = get(),
            logger = get()
        )
        DefaultLockLogsRepository(
            remote = remote,
            local = local,
            logger = get()
        )
    }

    single<RegulaDocuments> {
        AndroidRegulaDocuments(
            context = get(),
            logger = get(),
            licenseRes = R.raw.regula
        )
    }

    single<RegulaFace> {
        AndroidRegulaFace(
            context = get(),
            logger = get(),
        )
    }

    single<Downloader> {
        AndroidDownloader(
            context = get(),
            logger = get()
        )
    }

    single {
        NordicFirmwareRepository(
            context = get(),
            logger = get()
        )
    }

    single<RayonicsManager> {
        AndroidRayonicsManager(
            context = get(),
            dispatcher = Dispatchers.Default,
            downloader = get(),
            nordic = get(),
            logger = get()
        )
    }

    single<IseoManager> {
        AndroidIseoManager(
            context = get(),
            logger = get(),
            dispatcher = Dispatchers.Default,
            settings = get<KeyValueCache>()
        )
    }

    single<TTLockManager> {
        AndroidTTLockManager(
            context = get(),
            logger = get(),
            dispatcher = Dispatchers.Default,
        )
    }

    single<AirBnkManager> {
        AndroidAirBnkManager(
            logger = get()
        )
    }

    single<PermissionsManager> {
        AndroidPermissionsManager(
            applicationContext = get(),
            logger = get()
        )
    }

    single<BluetoothManager> {
        AndroidBluetoothManager(
            context = get(),
            permissions = get(),
            logger = get()
        )
    }

    single<NetworkManager> {
        AndroidNetworkManager(
            context = get(),
            logger = get()
        )
    }

    single<LocationRepository> {
        AndroidLocationRepository(
            applicationContext = get(),
            logger = get()
        )
    }

    single<MSTManager> {
        AndroidMSTManager(
            applicationContent = get(),
            logger = get(),
            lifecycle = get(),
            bluetooth = get()
        )
    }

    single {
        LocksManager(
            ttlock = get(),
            rayonics = get(),
            iseo = get(),
            bluetooth = get(),
            airBnk = get(),
            tedee = get(),
            logger = get(),
            network = get(),
            location = get(),
            mst = get()
        )
    }

    single {
        TedeeRepository(
            client = get(),
            logger = get(),
            hostname = ConfigValues.baseUrl
        )
    }

    single { Lifecycle }

    single { AuthenticationRepository(client = get(), logger = get(), hostname = ConfigValues.baseUrl) }

    single<AbstractCoroutineDispatcher> { CoroutineDispatchers() }
}