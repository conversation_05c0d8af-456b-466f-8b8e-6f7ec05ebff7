plugins {
    id("keyless.android.library")
    id("config.values")
    id("keyless.koin")
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-caching-key-value"))
    implementation(project(":core-http-client-ktor"))
    implementation(project(":core-location-google"))
    implementation(project(":core-lock-logs"))
    implementation(project(":core-lock-airbnk"))
    implementation(project(":core-lock-iseo"))
    implementation(project(":core-locks-manager"))
    implementation(project(":core-lock-rayonics"))
    implementation(project(":core-lock-tedee"))
    implementation(project(":core-lock-ttlock"))
    implementation(project(":core-monitoring-firebase"))
    implementation(project(":core-monitoring-test"))
    implementation(project(":core-permissions-manager"))
    implementation(project(":core-regula-documents"))
    implementation(project(":core-regula-face"))

    implementation(project(":data-common"))
    implementation(project(":data-company"))
    implementation(project(":data-database"))
    implementation(project(":data-keyless"))
    implementation(project(":data-lock-common"))
    implementation(project(":data-user-account"))
    implementation(project(":data-user-guest"))
    implementation(project(":data-user-home"))

    implementation(project(":domain-common"))
    implementation(project(":domain-home"))
    implementation(project(":domain-regula"))
    implementation(project(":domain-settings-checkin"))
    implementation(project(":domain-settings-company"))
    implementation(project(":domain-settings-company-staff"))
    implementation(project(":domain-settings-profile"))
    implementation(project(":domain-settings-support"))

    implementation(project(":feature-regula-refactored"))
    implementation(project(":feature-settings"))
    implementation(project(":feature-settings-checkin"))
    implementation(project(":feature-settings-company-profile"))
    implementation(project(":feature-settings-company-staff"))
    implementation(project(":feature-settings-profile"))

    implementation(keyless.ktor.client.android)
    implementation(keyless.sqldelight.runtime)
    implementation(keyless.sqldelight.android.driver)
}

configValues {
    val baseUrl = getLocalProperty("base.url") as String? ?: throw GradleException(
        "Missing base.url property in local.properties"
    )
    val baseImageUrl = getLocalProperty("base.image.url") as String? ?: throw GradleException(
        "Missing base.image.url property in local.properties"
    )
    val forgotPasswordUrl = getLocalProperty("forgot.password.url") as String? ?: throw GradleException(
        "Missing forgot.password.url property in local.properties"
    )

    setValues(
        "baseUrl" to baseUrl,
        "baseImageUrl" to baseImageUrl,
        "forgotPasswordUrl" to forgotPasswordUrl
    )
}