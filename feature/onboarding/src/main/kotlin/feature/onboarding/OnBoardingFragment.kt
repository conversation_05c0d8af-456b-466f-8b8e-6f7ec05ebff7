package feature.onboarding

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import keyless.feature.onboarding.R
import keyless.feature.onboarding.databinding.OnboardingFragmentBinding
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow

class OnBoardingFragment : Fragment(R.layout.onboarding_fragment) {

    lateinit var binding: OnboardingFragmentBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = OnboardingFragmentBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUpViewPager()
        clickListeners()
    }

    private fun clickListeners() {
        binding.onboardSignBtn.setOnClickListener {
            val intent = Intent(requireContext(), AuthenticationActivity::class.java)
            intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.SIGNUP)
            startActivity(intent)
        }

        binding.loginTxt.setOnClickListener {
            val intent = Intent(requireContext(), AuthenticationActivity::class.java)
            intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.LOGIN)
            startActivity(intent)
        }
    }

    private fun setUpViewPager() {
        binding.onboardViewPager.adapter = OnboardAdapter(
            requireActivity()
        )
        binding.tabIndicator.setupWithViewPager(binding.onboardViewPager)

        startVideo()
    }

    private fun startVideo() {
        binding.onboardVidView.setVideoURI(
            Uri.parse("android.resource://" + requireActivity().packageName + "/" + R.raw.onboard_vid)
        )
        binding.onboardVidView.start()
        binding.onboardVidView.setOnCompletionListener {
            binding.onboardVidView.start()
        }

        binding.onboardVidView.setOnPreparedListener {
            val videoProportion: Float = getVideoProportion()
            val screenWidth = resources.displayMetrics.widthPixels
            val screenHeight = resources.displayMetrics.heightPixels
            val screenProportion = screenHeight.toFloat() / screenWidth.toFloat()
            val lp: ViewGroup.LayoutParams = binding.onboardVidView.layoutParams

            if (videoProportion < screenProportion) {
                lp.height = screenHeight
                lp.width = (screenHeight.toFloat() / videoProportion).toInt()
            } else {
                lp.width = screenWidth
                lp.height = (screenWidth.toFloat() * videoProportion).toInt()
            }
            binding.onboardVidView.layoutParams = lp
        }
    }

    private fun getVideoProportion(): Float {
        return 1.5F
    }

    override fun onStop() {
        super.onStop()
        binding.onboardVidView?.pause()
        binding.onboardVidView?.stopPlayback()
    }

    override fun onResume() {
        super.onResume()
        startVideo()
    }
}