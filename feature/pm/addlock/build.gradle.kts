plugins {
    id("keyless.android.feature")
    id("org.jetbrains.kotlin.kapt")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-lock-iseo"))
    implementation(project(":rayonicsSdk"))
    implementation(project(":core-lock-mst"))
    // implementation(project(":ttlockSdk"))

    implementation(project(":core-common"))
    implementation(project(":core-lock-airbnk"))
    implementation(project(":core-locks-manager"))
    implementation(project(":core-lock-ttlock"))
    implementation(project(":data-common"))
    implementation(project(":data-company"))
    implementation(project(":data-lock-common"))
    implementation(project(":data-network-android"))
    implementation(project(":data-user-home"))
    implementation(project(":data-utils-android"))
    implementation(project(":domain-common"))
    implementation(project(":feature-common"))
    implementation(project(":feature-settings-maintenance"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.retrofit)

    implementation("com.mikhaellopez:circularimageview:4.3.1")
    implementation("org.greenrobot:eventbus:3.3.1")
    implementation ("com.github.mukeshsolanki:android-otpview-pinview:3.2.0")
    implementation ("com.github.khoyron:Actionsheet-android:4")

    implementation("me.dm7.barcodescanner:zxing:1.9.8")
    implementation("com.karumi:dexter:6.2.3")

    implementation("com.alibaba:fastjson:1.1.60.android")

    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")

    implementation(keyless.glide)
    kapt(keyless.glide.compiler)
}