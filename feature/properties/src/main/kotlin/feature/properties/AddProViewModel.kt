package feature.properties

import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import data.network.android.ApiUtils
import data.network.android.models.GetAllIconsModel
import data.network.android.models.GetPropertyResponse
import presentation.common.domain.repositories.ErrorMessageHandler
import data.utils.android.settings.SharedPreferenceUtils
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AddProViewModel : ViewModel() {

    private var _getResponse = MutableLiveData<GetPropertyResponse>()
    val getResponse: LiveData<GetPropertyResponse> = _getResponse
    private var _getResponseInstallerProperty = MutableLiveData<GetPropertyResponse>()
    val getResponseInstallerList: LiveData<GetPropertyResponse> = _getResponseInstallerProperty

    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    fun hitGetCreatePropertyApi(
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        laundary_number: String,
        grocery_number: String,
        icon_id: String,
        supportCall: String,
        whatsapp: String
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.getCreateProperty(
                area = area,
                total_floors = total_floors,
                building_name = building_name,
                emirate = emirate,
                longitude = longitude,
                token = token,
                latitude = latitude,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id, supportCall, whatsapp,
                p1 = {
                    (it as GetPropertyResponse)
                    _getResponse.value = it
                },
                p2 = {
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }

            )
        }
    }

    fun updateProperty(
        propertyID: String,
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        laundary_number: String,
        grocery_number: String,
        icon_id: String,
        callString: String,
        whatsapp: String,
        context: Context,
        callBack: (GetPropertyResponse?) -> Unit

    ) {
        viewModelScope.launch(
            Dispatchers.IO + CoroutineExceptionHandler { coroutineContext, throwable ->
//            _error.value = ApiErrorHandler.handleException(throwable as Exception)
                var msg = ErrorMessageHandler.handleException(throwable as Exception)
                Log.e("//", "updateProperty: " + msg)

                (context as Activity).runOnUiThread {
                    _error.value = msg
                }
//            callBack(response)
            }
        ) {
            Firebase.crashlytics.log(latitude)
            Firebase.crashlytics.log(longitude)
            val response = ApiUtils.servicesInterface.updateProperty(
                token = token,
                propertyID,
                latitude = latitude,
                longitude = longitude,
                emirate = emirate,
                building_name = building_name,
                total_floors = total_floors,
                area = area,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id,
                callString,
                whatsapp
            )
            callBack(response)
        }
    }

    fun getIcons(context: Context, callBack: (GetAllIconsModel?) -> Unit) {
        viewModelScope.launch {
            val token = SharedPreferenceUtils.getInstance(context).token
            ApiUtils.getAllIcons(
                token,
                {
                    it as GetAllIconsModel
                    if (it.success) {
                        callBack(it)
                    } else {
                        callBack(null)
                    }
                },
                {
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }
            )
        }
    }

    fun hitCreateInstallerProperty(
        area: String,
        total_floors: String,
        building_name: String,
        emirate: String,
        longitude: String,
        token: String,
        latitude: String,
        grocery_number: String,
        laundary_number: String,
        icon_id: String,
        supportCall: String,
        whatsapp: String,
        id: String
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.hitCreateInstallerProperty(
                area = area,
                total_floors = total_floors,
                building_name = building_name,
                emirate = emirate,
                longitude = longitude,
                token = token,
                latitude = latitude,
                laundary_number = laundary_number,
                grocery_number = grocery_number,
                icon_id, supportCall, whatsapp, id,
                p1 = {
                    (it as GetPropertyResponse)
                    _getResponseInstallerProperty.value = it
                },
                p2 = {
                    _error.value = ErrorMessageHandler.handleException(it).toString()
                }

            )
        }
    }
}