<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorAccent">


    <TextView
        android:id="@+id/titlePage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/poppins_bold_700"
        android:text="@string/buildings"
        android:textColor="@color/white"
        android:textSize="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:visibility="gone"
        android:id="@+id/ivSettings"
        android:src="@drawable/iv_settings"
        app:layout_constraintBottom_toBottomOf="@+id/titlePage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/titlePage" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mainFilter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titlePage"
        app:layout_constraintVertical_bias="0.0">


        <ImageView
            android:layout_width="15dp"
            android:id="@+id/ivLock"
            android:src="@drawable/iv_checked_radio"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_height="15dp"/>


        <TextView
            android:id="@+id/txtLocks"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:layout_weight="1"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/map"
            android:layout_marginStart="10dp"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivLock"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/mapRadio"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivProperties"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:layout_width="15dp"
            android:id="@+id/ivProperties"
            android:layout_marginStart="32dp"
            android:src="@drawable/iv_not_checked_grey"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/txtLocks"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_height="15dp"/>

        <TextView
            android:id="@+id/txtProperties"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/list"
            android:layout_marginStart="10dp"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/txtLocks"
            app:layout_constraintStart_toEndOf="@+id/ivProperties"
            app:layout_constraintTop_toTopOf="@+id/txtLocks" />

        <View
            android:id="@+id/listRadio"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/txtProperties"
            app:layout_constraintStart_toStartOf="@+id/ivProperties"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/addProperty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/iv_bg_new"
            android:padding="6dp"
            android:src="@drawable/iv_add_black"
            app:layout_constraintBottom_toBottomOf="@+id/txtLocks"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/txtLocks" />


    </androidx.constraintlayout.widget.ConstraintLayout>



    <FrameLayout
        android:id="@+id/frameContainerHome"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/white_top_corners"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainFilter"
        app:layout_constraintVertical_bias="0.0" >
        <androidx.cardview.widget.CardView
            android:visibility="gone"
            android:id="@+id/mapLayout"
            android:elevation="0dp"
            android:layout_marginBottom="-12dp"
            app:cardCornerRadius="32dp"
            app:cardElevation="0dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <FrameLayout
                android:paddingBottom="20dp"
                android:id="@+id/mapFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.cardview.widget.CardView>

    </FrameLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:visibility="gone"
        android:layout_marginStart="20dp"
        android:id="@+id/propertyItemView"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_button_rounded"
        android:backgroundTint="@color/white"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <include
            android:id="@+id/includeMarkLayout"
            layout="@layout/property_item_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <me.grantland.widget.AutofitTextView
            android:includeFontPadding="false"
            android:layout_marginTop="8dp"
            android:id="@+id/viewDetailBtn"
            android:singleLine="true"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:paddingStart="30dp"
            android:paddingTop="8dp"
            android:paddingEnd="30dp"
            android:paddingBottom="8dp"
            android:textSize="14dp"
            app:minTextSize="8dp"
            android:autoSizeMaxTextSize="14dp"
            android:text="@string/view_details"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/directionBtn"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/includeMarkLayout" />

        <me.grantland.widget.AutofitTextView
            android:includeFontPadding="false"
            android:id="@+id/directionBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:paddingStart="30dp"
            android:paddingTop="8dp"
            android:textSize="14dp"
            app:minTextSize="8dp"
            android:autoSizeMaxTextSize="14dp"
            android:paddingEnd="30dp"
            android:paddingBottom="8dp"
            android:singleLine="true"
            android:text="@string/direction"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="@+id/viewDetailBtn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/viewDetailBtn" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <EditText
        android:id="@+id/svProperties"
        style="@style/mirrorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_btn_round"
        android:backgroundTint="@color/bg_edit_grey"
        android:drawableStart="@drawable/ic_baseline_search_24"
        android:drawablePadding="10dp"
        android:ellipsize="end"
        android:focusableInTouchMode="true"
        android:fontFamily="@font/poppins_regular_400"
        android:hint="@string/search_by_building_name"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:padding="8dp"
        android:visibility="gone"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/frameContainerHome"
        app:layout_constraintVertical_bias="0.0" />

    <ImageView
        android:id="@+id/stopSearchProperties"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:src="@drawable/iv_cross_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/svProperties"
        app:layout_constraintEnd_toEndOf="@+id/svProperties"
        app:layout_constraintTop_toTopOf="@+id/svProperties" />




    <androidx.recyclerview.widget.RecyclerView
        android:visibility="gone"
        android:paddingTop="18dp"
        android:layout_marginStart="18dp"
        android:layout_marginEnd="18dp"
        android:id="@+id/propertiesListRV"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/svProperties" />

    <TextView
        android:visibility="gone"
        android:id="@+id/noInternetLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawableTop="@drawable/img_no_internet"
        android:drawablePadding="15dp"
        android:fontFamily="@font/poppins_medium_500"
        android:text="@string/no_internet_connection"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainFilter" />

 <TextView
        android:visibility="gone"
        android:id="@+id/noDataProperties"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:drawableTop="@drawable/iv_properties_grey"
        android:drawablePadding="15dp"
        android:fontFamily="@font/poppins_semibold_600"
        android:text="@string/no_properties"
        android:textColor="@color/black"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainFilter" />





</androidx.constraintlayout.widget.ConstraintLayout>
