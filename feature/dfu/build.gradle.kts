plugins {
    id("keyless.android.feature")
    id("dagger.hilt.android.plugin")
    id("org.jetbrains.kotlin.kapt")
    alias(keyless.plugins.compose.compiler)
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
        compose = true
    }
}

dependencies {
    implementation(project(":core-lock-ttlock"))
    implementation(project(":data-common"))
    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":feature-common"))
    implementation(project(":core-lock-iseo"))
    implementation(project(":rayonicsSdk"))
    //implementation(project(":ttlockSdk"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.retrofit)

    implementation("com.mikhaellopez:circularimageview:4.3.1")
    implementation("org.greenrobot:eventbus:3.3.1")
    implementation ("com.github.mukeshsolanki:android-otpview-pinview:3.2.0")
    implementation ("com.github.khoyron:Actionsheet-android:4")
    implementation("io.github.chaosleung:pinview:1.4.4")
    implementation("com.alibaba:fastjson:1.1.60.android")

    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")

    implementation ("com.google.dagger:hilt-android:2.53.1")
    kapt ("com.google.dagger:hilt-android-compiler:2.53.1")

    implementation ("no.nordicsemi.android:dfu:2.4.2")
    implementation(keyless.nordic.navigation)
    implementation(keyless.nordic.theme)
    implementation(keyless.nordic.ui.logger)
    implementation(keyless.androidx.compose.material.icons.extended)
    implementation(keyless.androidx.compose.activity)
    implementation("androidx.compose.ui:ui-tooling-preview-android:1.5.2")

}