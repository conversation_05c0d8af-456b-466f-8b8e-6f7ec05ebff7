package feature.dfu.view.nearbyDfuActivity

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.app.DownloadManager
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileHermesCmdService
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import dagger.hilt.android.AndroidEntryPoint
import data.common.preferences.Preferences
import data.network.android.LocksListResponse
import data.network.android.models.LockInfoModel
import data.network.android.models.ModelDfuBackendData
import data.network.android.models.ModelMapLockproperty
import data.network.android.models.old
import data.user.home.repositories.UserHomeRepository
import data.utils.android.CommonValues
import data.utils.android.common.BleLockScanData
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import dots.animation.textview.TextAndAnimationView
import feature.dfu.data.DeviceTarget
import feature.dfu.view.dfuFirmUpdate.ProgressItemStatus
import feature.dfu.viewmodel.DfuViewModel
import keyless.data.utils.android.R
import keyless.feature.dfu.databinding.ActivityNearbyDfuDeviceBinding
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import rayo.logicsdk.bean.EnableEnum
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import java.io.File
import java.lang.Thread.sleep
import java.util.Date

@AndroidEntryPoint
class NearByDfuDeviceActivity : AppCompatActivity(), LockDfuAdapter.ClickLock,
    KoinComponent { //    , IScanBTManagerEvent

    private val userHomeRepository: UserHomeRepository by inject()
    private lateinit var scanRecordNew: ByteArray
    private var lockInfo: ArrayList<LockInfoModel> = ArrayList()
    private var property_details: ModelMapLockproperty = ModelMapLockproperty()
    private var mBleName: MutableList<String?> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private val ADD_ADAPTER = 1
    var horizontalLayoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    private lateinit var adapter: LockDfuAdapter
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private lateinit var mobileCredentialHermesCmdService: IMobileHermesCmdService
    var value = 0
    private lateinit var binding: ActivityNearbyDfuDeviceBinding
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mLockBasicInfo: LockBasicInfo? = null
    private lateinit var viewModel: DfuViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }

    private lateinit var onDownloadComplete: FirmwareDownloadReceiver
    private var target: DeviceTarget? = null
    private var downloadID: Long? = null
    private var firmwareFile: File? = null
    private var name: String = ""
    private var address: String = ""
    private var accessKey: String = ""
    private var firmwareId: String = ""
    private var lockId: String = ""
    private var connectingAgain: Int = 0
    lateinit var dialogLock: Dialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNearbyDfuDeviceBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setAdapter()
        clickListeners()
        askPermission()
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[DfuViewModel::class.java]
        if (intent.getStringExtra("installer") == "1") {
            lockInfo = intent.getParcelableArrayListExtra("lockInfo")!!
            property_details = intent.getParcelableExtra("property_details")!!
        }
    }

    @SuppressLint("MissingPermission")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            if (intent.getStringExtra("installer") == "1") {
                showDialogForScanning()
                initDataRange()
                // binding.noData.isVisible = false
                binding.deviceRV.isVisible = true
            } else {
                if (sharePrefs.getLockData().size > 0) {
                    showDialogForScanning()
                    initDataRange()
                    // binding.noData.isVisible = false
                    binding.deviceRV.isVisible = true
                } else {
                    binding.noData.isVisible = true
                    binding.deviceRV.isVisible = false
                }
            }
//            iseoLock()
//            scanISEOLock()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        }
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            if (intent.getStringExtra("installer") == "1") {
                showDialogForScanning()
                initDataRange()
                // binding.noData.isVisible = false
                binding.deviceRV.isVisible = true
            } else {
                if (sharePrefs.getLockData().size > 0) {
                    showDialogForScanning()
                    initDataRange()
                    // binding.noData.isVisible = false
                    binding.deviceRV.isVisible = true
                } else {
                    binding.noData.isVisible = true
                    binding.deviceRV.isVisible = false
                }
            }
//            iseoLock()
//            scanISEOLock()
        }
    }

    private fun showDialogForScanning() {
        binding.noData.isVisible = true
        dialogLock = Dialog(this)
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        val animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        val cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }
        cancelBtn.setOnClickListener {
            cancelScan()
            finish()
        }

        Thread {
            sleep(20000)
            cancelScan()
        }.start()

        if (!isFinishing) {
            dialogLock.show()
        }
    }

    private fun cancelScan() {
        mBleLockSdk?.disconnect()
        mBleScanCallback.finishScan()
        dialogLock.dismiss()
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onResume() {
        super.onResume()
        onDownloadComplete = FirmwareDownloadReceiver()
        registerReceiver(
            onDownloadComplete, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE), Context.RECEIVER_EXPORTED
        )
//        registerReceiver(
//            onDownloadComplete,
//            IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
//            Context.RECEIVER_EXPORTED
//        )
    }

    override fun onPause() {
        super.onPause()
        unregisterReceiver(onDownloadComplete)
    }

    override fun onStop() {
        EventBus.getDefault().unregister(this)
        super.onStop()
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                        (binding.deviceRV.adapter as LockDfuAdapter).clean()
//                        binding.deviceProgressBar.isVisible = true
                        val handler = Handler(Looper.getMainLooper())
                        handler.postDelayed({
                            if (value == 0) {
                                if (intent.getStringExtra("installer") == "1") {
                                    showDialogForScanning()
                                    initDataRange()
                                    // binding.noData.isVisible = false
                                    binding.deviceRV.isVisible = true
                                } else {
                                    if (sharePrefs.getLockData().size > 0) {
                                        showDialogForScanning()
                                        initDataRange()
                                        // binding.noData.isVisible = false
                                        binding.deviceRV.isVisible = true
                                    } else {
                                        binding.noData.isVisible = true
                                        binding.deviceRV.isVisible = false
                                    }
                                }
                            } else {
                                if (mBluetoothLeScan != null) {
                                    mBluetoothLeScan!!.stopReceiver()
                                    isScan = false
                                    mBluetoothDeviceHashMap = HashMap()
                                    mBluetoothLeScan!!.startReceiver()
                                    isScan = true
                                }
                            }
//                            iseoLock()
//                            scanISEOLock()
                        }, 1000)
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    override fun onDestroy() {
        super.onDestroy()
    }

//    private fun iseoLock() {
//        scanManagerService = ServiceProvider.scanManagerService
//        mobileCredentialService = ServiceProvider.mobileCredentialService
//        mobileCredentialHermesCmdService =
//            ServiceProvider.mobileCredentialHermesCmdService
//        scanManagerService.setScanBTManagerEvent(this)
//
//    }

//    private fun scanISEOLock() {
//        CoroutineScope(Dispatchers.Main).launch {
//            withContext(Dispatchers.IO) {
//                scanManagerService.stopScan()
//                try {
//                    scanManagerService.startScanLock(false)
//                } catch (e: V364SdkException) {
//
//                }
//            }
//        }
//
//    }

    private fun setAdapter() {
        binding.deviceRV.layoutManager = horizontalLayoutManager
        horizontalLayoutManager.reverseLayout = false
        binding.deviceRV.itemAnimator = null

        adapter = LockDfuAdapter(this)
        binding.deviceRV.adapter = adapter

//        adapter.onItemClick = { namee, addresss, accessKeyy ->
//
//        }
    }

    override fun connecting(
        bleName: String?,
        bleMac: String?,
        accessKeyy: String?,
        scanRecord: ByteArray?,
        _id: String
    ) {
//        isScan = false
//        mBluetoothLeScan!!.stopReceiver()
        binding.deviceProgressBar.isVisible = true
        isSeenOnce = 0
        name = bleName!!
        address = bleMac!!
        scanRecordNew = scanRecord!!
        accessKey = accessKeyy!!
        lockId = _id
        mBleLockSdk?.disconnect()
        connectLock()
    }

    // Downloads file in internal memory
    // Temp Static Url ::: "http://api.phase1-dev.keyless-dev.ae/firmware/gf3ntj37.zip"
    @SuppressLint("Range")
    fun downloadFile(url: String): Long {
        Log.e("TAG", "downloading file")
        binding.deviceProgressBar.isVisible = true
        binding.progressTxt.text = getString(keyless.data.utils.android.R.string.downloading_firmware)

        val downloadManager = getSystemService(DownloadManager::class.java)
//        val destination = externalCacheDir.toString()
//        firmFile = File(destination, "keylessFirmware.zip")

        firmwareFile = getFirmFile()

//        if (firmwareFile?.exists() == true) {
//            firmwareFile?.delete()
//        }
        val request = DownloadManager.Request(url.toUri())
            .setMimeType("application/zip")
            .setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI)
            .setNotificationVisibility(
                DownloadManager.Request.VISIBILITY_VISIBLE or
                    DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_ONLY_COMPLETION
            )
            .setTitle("Downloading Firmware")
            .setDestinationUri(firmwareFile?.toUri())

        Log.e("TAG", "Enqueueing REQ")
        return downloadManager.enqueue(request)
    }

    var isUpdating = false
    private var isSeenOnce = 0

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: DownloadStatus?) {
        Log.e("TAG", "onMessageEvent: Event on $downloadID")
        if (event?.isCompleted == true) {
            Log.e("TAG", "onMessageEvent: Completed $downloadID")
            binding.deviceProgressBar.isVisible = true
            binding.progressTxt.text = getString(keyless.data.utils.android.R.string.download_completed)

            Log.e("TAG", "onMessageEvent: $firmwareFile")
            if (firmwareFile != null && !isUpdating) {
                isUpdating = true
                Log.e("TAG", "isUpdating: $isUpdating")

                isFileValid()
                viewModel.onZipFileSelected(firmwareFile!!.toUri(), applicationContext)
                Log.e("TAG", "onMessageEvent: Starting Updating")
                mBleLockSdk?.disconnect()
                initiateFirmwareUpdate()
            }
        }
    }

    private fun isFileValid() {
    }

    private fun saveResponseInPrefs(respose: ModelDfuBackendData) {
        sharePrefs.putFirmResponse(respose)
    }

    private fun isSpContainsID(id: String): Boolean {
        val response = sharePrefs.getFirmResponse() ?: return false

        if (response.keyless._id == id) {
            return true
        }

        return false
    }

    private fun isFirmFileExists(): Boolean {
        val firmFile = getFirmFile()
        return firmFile.exists()
    }

    private fun getFirmFile(): File {
        val destination = externalCacheDir.toString()
        return File(destination, "keylessFirmware.zip")
    }

    private fun initiateFirmwareUpdate() {
        viewModel.onDeviceSelected(target!!)
        binding.deviceProgressBar.isVisible = true
        binding.progressTxt.text = getString(keyless.data.utils.android.R.string.start_updating)
        viewModel.progressLiveData.observe(this) { it ->
            val status = it.status
            Log.e(
                "TAG",
                "initiateFirmwareUpdate: " +
                    "\ndfu_status ${status.dfuStatus}" +
                    "\nbootloader status ${status.bootloaderStatus}" +
                    "\nresult status ${status.resultStatus}" +
                    "\ninstallation status ${status.installationStatus}" +
                    "\nprogress status% ${status.progress.progress}" +
                    "\nAvg Speed ${status.progress.avgSpeed}"
            )

            if (status.isRunning()) {
                binding.deviceProgressBar.isVisible = true
            }
            binding.progressTxt.text =
                getString(keyless.data.utils.android.R.string.uploading) + " " + status.progress.progress + "%"
            if (status.installationStatus == ProgressItemStatus.DISABLED) {
                binding.deviceProgressBar.isVisible = true
            } else if (status.installationStatus == ProgressItemStatus.SUCCESS) {
                connectingAgain = 2
                binding.progressTxt.visibility = View.GONE
                val jsonObject = JsonObject()
                // jsonObject.addProperty("firmwareId", firmwareId)
                jsonObject.addProperty("firmwareVersion", mLockBasicInfo?.softwareVersion)
                jsonObject.addProperty("lockId", lockId)
                jsonObject.addProperty("firmwareId", firmwareId)
                jsonObject.addProperty("access_key", accessKey)
                viewModel.updateFirmwareStatus(jsonObject, sharePrefs.token).observe(this) {
                    lifecycleScope.launch {
                        try {
                            val response = userHomeRepository.fetchUserHomeData(
                                uid = sharePrefs.uuid,
                                isAdmin = Preferences.isAdminLogin(),
                                token = sharePrefs.token
                            )
                            val gson = Gson()
                            val homeData: String = gson.toJson(response.old().locks)
                            sharePrefs.homeLockData(homeData)
                            Handler(Looper.getMainLooper()).postDelayed({
                                connectLock()
                            }, 4000)
                        } catch (ex: Exception) {

                        }
                    }
                }
            } else if (status.installationStatus == ProgressItemStatus.ERROR) {
                connectingAgain = 0
                if (getFirmFile().exists()) {
                    getFirmFile().delete()
                    firmwareFile = null
                }

                isUpdating = false

                if (isSeenOnce == 0) {
                    isSeenOnce = 1
                    defaultDialog(
                        this,
                        getString(
                            R.string
                                .there_is_an_error_in_the_firmware_file_please_contact_keyless_support
                        ),
                        object : OnActionOK {
                            override fun onClickData() {
                                mBleLockSdk?.disconnect()
                                binding.deviceProgressBar.isVisible = false
                            }
                        }
                    )
                }

                Log.e("TAG_!!!!", "initiateFirmwareUpdate: ${status.errorMessage}")
            }
        }
        viewModel.onInstall()
    }

    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            var lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = accessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                address,
                scanRecordNew,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                init sdk  
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@NearByDfuDeviceActivity
            )
            rayo.logicsdk.utils.Log.d(
                "TAG",
                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun connect(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
            connect 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@NearByDfuDeviceActivity
            )
        }

        override fun authentication(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                when (connectingAgain) {
                    0 -> {
                        connectingAgain = 1
                        target = DeviceTarget(name, address)

                        Log.e("TAG", "setAdapter: onItemClick ::: $name $address  $accessKey")

                        //            binding.deviceProgressBar.isVisible = true
                        binding.progressTxt.text = getString(keyless.data.utils.android.R.string.downloading_file)
                        mLockBasicInfo = resultBean.obj as (LockBasicInfo)

                        viewModel.getFirmware(sharePrefs.token)
                            .observe(this@NearByDfuDeviceActivity) {
                                if (it.success) {
                                    Log.e("TAG info lock ", mLockBasicInfo!!.softwareVersion)
                                    Log.e("TAG info ", it.keyless.version)
                                    var softwareVersion =
                                        mLockBasicInfo!!.softwareVersion.toString().replace(".", "")
                                            .replace("RCSWV", "")
                                    var fileVersion =
                                        it.keyless.version.replace(".", "").replace("RCSWV", "")
                                    mBleLockSdk?.disconnect()
                                    if (fileVersion == softwareVersion) {
                                        binding.deviceProgressBar.isVisible = false
                                        defaultDialog(
                                            this@NearByDfuDeviceActivity,
                                            getString(
                                                R.string
                                                    .this_lock_is_already_on_latest_firmware_version
                                            ),
                                            object : OnActionOK {
                                                override fun onClickData() {
                                                    mBleLockSdk?.disconnect()
                                                    finish()
                                                }
                                            }
                                        )
                                    } else if (fileVersion > softwareVersion) {
                                        firmwareId = it.keyless._id
                                        saveResponseInPrefs(it)
                                        // already contains file
                                        if (getFirmFile().exists()) {
                                            getFirmFile().delete()
                                            firmwareFile = null
                                        }
                                        downloadFileFromApi(it)
                                    } else {
                                        firmwareId = it.keyless._id
                                        if (!isSpContainsID(it.keyless._id)) {
                                            Log.e("TAG", "setAdapter: SAVING IN PREFs")
                                            saveResponseInPrefs(it)
                                            // download file
                                            downloadFileFromApi(it)
                                            // update firm
                                        } else {
                                            // already contains file
                                            Log.e("TAG", "setAdapter: Already Have in Prefs")
                                            // check if file exists
                                            if (isFirmFileExists()) {
                                                firmwareFile = getFirmFile()
                                                // yes - select and update
                                                connectLock()
                                            } else {
                                                // no  - download and update
                                                Log.e(
                                                    "TAG",
                                                    "setAdapter: File Doesn't exists ,, Downloading....."
                                                )
                                                downloadFileFromApi(it)
                                            }
                                        }
                                    }
                                } else {
                                    binding.deviceProgressBar.isVisible = false
                                    binding.progressTxt.text = ""
                                    defaultDialog(
                                        this@NearByDfuDeviceActivity,
                                        it.message,
                                        object : OnActionOK {
                                            override fun onClickData() {
                                                connectingAgain = 0
                                            }
                                        }
                                    )
                                }
                            }
                    }

                    1 -> {
                        connectingAgain = 2
                        Log.e("TAG", "setAdapter: Setting File in VM ,, initiating up")
                        Log.e("TAG", "setAdapter: File $firmwareFile")
                        // connectLock
                        viewModel.onZipFileSelected(
                            firmwareFile!!.toUri(),
                            applicationContext
                        )
                        mBleLockSdk?.disconnect()
                        initiateFirmwareUpdate()
                    }

                    2 -> {
                        mLockBasicInfo!!.setmUpgradeFlag(EnableEnum.ENABLE_ENUM)
                        mBleLockSdk?.setLockInfo(mLockBasicInfo)
                    }
                }
            } else {
                Log.e("TAG", "authentication: CONNECTION failed")
            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            Log.e("disConnect", "authentication: CONNECTION failed")
//            connectLock()
//            if (!disconnectByMe) {
// //                txtConnecting.isVisible = true
// //                animatedDots.isVisible = true
// //                tv_unlock.isVisible = false
//                binding.ripplView.stopRippleAnimation()
//                autCase = "0"
// //                initLock()
//            }
//            CommonValues.saveLockLogs(
//                lockDetails.lock.provider,
//                lockDetails.lock.internal_id,
//                "Disconnected",
//                "Unlock",
//                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat),
//                this@LockDetailsActivity
//            )
        }

        override fun registerLock(resultBean: ResultBean<*>) {
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
            Log.e(
                "setLockInfo",
                """
            setLockInfo 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent()
            )
            CommonValues.showMessage(
                """
            setLockInfo 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@NearByDfuDeviceActivity
            )

            defaultDialog(
                this@NearByDfuDeviceActivity,
                getString(R.string.firmware_updated_successfully),
                object : OnActionOK {
                    override fun onClickData() {
                        mBleLockSdk?.disconnect()
                        finish()
                    }
                }
            )
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
//            CommonValues.saveLockLogs(
//                lockDetails.lock.provider,
//                lockDetails.lock.internal_id,
//                "Opened Successfully",
//                "Unlock",
//                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat),
//                this@NearByDfuDeviceActivity
//            )
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
        }

        override fun onReport(resultBean: ResultBean<*>?) {
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
        }

        override fun disPlay(resultBean: ResultBean<*>) {
        }

        override fun setTempCard(p0: ResultBean<*>?) {
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }
    }

    private fun downloadFileFromApi(it: ModelDfuBackendData) {
        if (it.keyless.packageDownloadUrl.isNotEmpty()) {
            downloadID = downloadFile(it.keyless.packageDownloadUrl)
        } else {
            binding.deviceProgressBar.isVisible = false
            defaultDialog(
                this@NearByDfuDeviceActivity,
                getString(
                    R.string
                        .there_is_an_error_in_the_firmware_file_please_contact_keyless_support
                ),
                object : OnActionOK {
                    override fun onClickData() {
                        binding.deviceProgressBar.isVisible = false
                        finish()
                    }
                }
            )
        }
    }

    private fun initDataRange() {
        mBleName = ArrayList()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_S_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()

        Handler(Looper.getMainLooper()).postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()

            isScan = true
        }, 1000)
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {

        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
//        super.onBackPressed()
        if (!binding.deviceProgressBar.isVisible) {
            if (mBluetoothLeScan != null) {
                mBluetoothLeScan!!.stopReceiver()
                isScan = false
                mBleLockSdk?.disconnect()
            }
            finish()
        }
    }

    private fun clickListeners() {
        binding.backBtnDevice.setOnClickListener {
            if (!binding.deviceProgressBar.isVisible) {
                if (mBluetoothLeScan != null) {
                    mBluetoothLeScan!!.stopReceiver()
                    isScan = false
                    mBleLockSdk?.disconnect()
                }
                finish()
            }
        }

        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (
                    object :
                        OnBackPressedCallback(true),
                        OnBackInvokedCallback {
                        override fun handleOnBackPressed() {
                        }

                        override fun onBackInvoked() {
                            if (!binding.deviceProgressBar.isVisible) {
                                if (mBluetoothLeScan != null) {
                                    mBluetoothLeScan!!.stopReceiver()
                                    isScan = false
                                    mBleLockSdk?.disconnect()
                                }
                                finish()
                            }
                        }
                    }
                    )
            )
        }
    }

    private inner class BleHandler : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                ADD_ADAPTER -> addAdapterItemRange(msg.obj as BleLockScanData)
                else -> {
                }
            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItem(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItem(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice.bleName.contains(CommonValues.EXTRA_C_BLE_NAME) || bluetoothDevice.bleName.contains(
                CommonValues.EXTRA_D_BLE_NAME
            ) || bluetoothDevice.bleName.contains(CommonValues.EXTRA_B_BLE_NAME) || bluetoothDevice.bleName.contains(
                    CommonValues.EXTRA_S_BLE_NAME
                ) || bluetoothDevice.bleName.contains(CommonValues.EXTRA_E_BLE_NAME)
        ) {
            var blenameModel: List<LocksListResponse.LocksModel> = ArrayList()

            if (intent.getStringExtra("installer") == "1") {
                if (lockInfo[0].unique_key == bluetoothDevice.bleName) {
                    val modelLocking = LocksListResponse.LockModel()
                    modelLocking.name = lockInfo[0].name
                    modelLocking.access_key = lockInfo[0].access_key

                    var modelProperty = LocksListResponse.PropertyDetailsModel()
                    modelProperty.appartment_number = property_details.appartment_number
                    modelProperty.floor = property_details.floor_number

//                for (i in blenameModel1.indices) {
                    binding.noData.isVisible = false
                    adapter.addItems(
                        bluetoothDevice,
                        1,
                        true,
                        modelProperty,
                        modelLocking
                    )
                    binding.noData.visibility = View.GONE
                    dialogLock.dismiss()
//                    binding.deviceProgressBarFirst.isVisible = false
                }

//                }
            } else {
                val mainList = SharedPreferenceUtils.getInstance(this).getLockData()
                val keylessList = mainList.filter { it.lock.provider == "Keyless" }
                blenameModel = keylessList.filter {
                    it.lock.unique_key == bluetoothDevice.bleName
                }
                for (i in blenameModel.indices) {
                    binding.noData.isVisible = false
                    adapter.addItems(
                        bluetoothDevice,
                        1,
                        true,
                        blenameModel[i].property_details,
                        blenameModel[i].lock
                    )
                    binding.noData.visibility = View.GONE
                    if (!isFinishing) {
                        dialogLock.dismiss()
                    }
//                    binding.deviceProgressBarFirst.isVisible = false
                }

//                binding.noData.visibility = View.GONE
            }

            if (blenameModel.isEmpty()) {
//                adapter.addItems(bluetoothDevice, 1, false, null, null)
            }
        } else if (bluetoothDevice.bleName.length == 8) {
            val mainList = SharedPreferenceUtils.getInstance(this).getLockData()
            val keylessList = mainList.filter { it.lock.provider == "Messerschmitt" }
            val blenameModel =
                keylessList.filter { it.lock.unique_key == bluetoothDevice.bleName }
            for (i in blenameModel.indices) {
//                adapter.addItems(
//                    bluetoothDevice,
//                    2,
//                    true,
//                    blenameModel[i].property_details,
//                    blenameModel[i].lock
//                )
            }
            if (blenameModel.isEmpty()) {
//                adapter.addItems(bluetoothDevice, 2, false, null, null)
            }
        }

//        binding.deviceProgressBar.visibility = View.GONE
    }

    private fun checkName(bleName: String): Boolean {
        if (null == bleName) return false
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

//    override fun onScanStarted() {
//
//
//    }
//
//    override fun onScanStopped() {
//
//    }
//
//    override fun onF9000Found(p0: IKeyScanInfo?) {
//
//
//    }

//    override fun onLockFound(p0: ILockScanInfo?, p1: IMobileCredentialScanInfo?) {
//        runOnUiThread {
// //            binding.noData.visibility = View.GONE
// //            binding.deviceProgressBar.visibility = View.GONE
//            val mainList = SharedPreferenceUtils.getInstance(this).getLockData()
//            val iseoList = mainList.filter { it.lock.provider == "ISEO" }
//
//            val blenameModel =
//                iseoList.filter { it.lock.lock_uid == p1?.gateUid.toString() }
//            for (i in blenameModel.indices) {
// //                adapter.addData(
// //                    LockDetailsActivity.LockInfo(p0, p1),
// //                    true,
// //                    blenameModel[i].property_details, blenameModel[i].lock
// //                )
//            }
//            if (blenameModel.isEmpty()) {
// //                adapter.addData(
// //                    LockDetailsActivity.LockInfo(p0, p1),
// //                    false,
// //                    null, null
// //                )
//            }
//        }
//
//    }
//
//    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {
//
//    }
}