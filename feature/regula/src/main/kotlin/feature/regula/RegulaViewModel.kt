package feature.regula

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import data.network.android.ApiUtils
import data.network.android.ModelMessage
import presentation.common.domain.repositories.ErrorMessageHandler
import org.json.JSONArray

class RegulaViewModel : ViewModel() {

    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    private val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress

    fun checkInComplete(
        name: String,
        bookingNumber: String,
        documentNumber: String?,
        status: String,
        documentType: String,
        identity1: String?,
        identity2: String?,
        documentImageApi: String?,
        myImageApi: String?,
        similarity: String?,
        token: String,
        receivedJsonArray: JSONArray,
        expiry_date: String
    ) = liveData {
        ApiUtils.checkInComplete(
            name,
            bookingNumber,
            documentNumber,
            status,
            documentType,
            identity1,
            identity2,
            documentImageApi,
            myImageApi,
            similarity,
            token, receivedJsonArray, expiry_date,
            {
                (it as ModelMessage)
                _progress.value = false
                emit(it)
            }
        ) {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        }
    }
}