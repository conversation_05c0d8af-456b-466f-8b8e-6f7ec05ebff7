package feature.regula
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View.GONE
import android.view.View.VISIBLE
import android.widget.PopupMenu
import android.widget.Toast
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.google.gson.Gson
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.regula.documentreader.api.DocumentReader
import com.regula.documentreader.api.completions.IDocumentReaderCompletion
import com.regula.documentreader.api.config.ScannerConfig
import com.regula.documentreader.api.enums.DocReaderAction
import com.regula.documentreader.api.enums.eCheckResult
import com.regula.documentreader.api.results.DocumentReaderComparison
import com.regula.documentreader.api.results.DocumentReaderResults
import com.regula.documentreader.api.results.DocumentReaderValidity
import com.regula.facesdk.FaceSDK
import com.regula.facesdk.enums.ImageType
import com.regula.facesdk.exception.InitException
import com.regula.facesdk.model.MatchFacesImage
import com.regula.facesdk.model.results.LivenessResponse
import com.regula.facesdk.model.results.matchfaces.MatchFacesResponse
import com.regula.facesdk.model.results.matchfaces.MatchFacesSimilarityThresholdSplit
import com.regula.facesdk.request.MatchFacesRequest
import data.network.android.LocksListResponse
import data.utils.android.settings.SharedPreferenceUtils
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import feature.regula.model.DocIntentModel
import feature.regula.model.DocumentModel
import keyless.feature.regula.databinding.ActivityDocScanBinding
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class DocScanActivity : AppCompatActivity() {

    companion object {
        lateinit var modell: LivenessResponse

    }

    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }

    private var valueForResult: Int = 0
    private lateinit var photoFile: File
    private lateinit var docSelfi: Bitmap
    private lateinit var mySelfi: Bitmap
    private var goFurther: Boolean = false
    private var isExpiredBtn: Boolean = false
    private var isDocumentValidBtn: Boolean = false
    lateinit var binding: ActivityDocScanBinding
    private lateinit var popupSize: PopupMenu
    var docTypes = ArrayList<String>()
    var identityNumberPassport: String? = null
    var docIntentModel: DocIntentModel? = null
    val jsonArray = JsonArray()
    val jsonObject1 = JsonObject()
    val jsonObject2 = JsonObject()

    @Transient
    private val completion = IDocumentReaderCompletion { action, results, error ->

        if (action == DocReaderAction.COMPLETE
            || action == DocReaderAction.TIMEOUT
        ) {
            if (DocumentReader.Instance().functionality().isManualMultipageMode) {
                if (results?.morePagesAvailable != 0) {
                    DocumentReader.Instance().startNewPage()
                    DocumentReader.Instance().processParams().dateFormat = "YYYY-MM-DD"
//                    DocumentReader.Instance().processParams(). = "YYYY-MM-DD"
                    Handler(Looper.getMainLooper()).postDelayed({
                        showScanner(valueForResult)
                    }, 100)
                    return@IDocumentReaderCompletion
                } else {
                    DocumentReader.Instance().processParams().dateFormat = "YYYY-MM-DD"
                    DocumentReader.Instance().functionality().edit().setManualMultipageMode(false)
                        .apply()
                }
            }
            displayResults(results!!)
        } else
            if (action == DocReaderAction.CANCEL) {
                if (DocumentReader.Instance().functionality().isManualMultipageMode)
                    DocumentReader.Instance().processParams().dateFormat = "YYYY-MM-DD"
                DocumentReader.Instance().functionality().edit().setManualMultipageMode(false)
                    .apply()

                Toast.makeText(
                    this, getString(keyless.data.utils.android.R.string.scanning_cancelled), Toast.LENGTH_LONG
                ).show()
            } else if (action == DocReaderAction.ERROR) {
                Toast.makeText(this, "Error:$error", Toast.LENGTH_LONG).show()
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDocScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
        onInit()
        hideAll()
        popupSetUp()
        onClicks()
//        showScanner()
//        recognizeImage()

    }

    private fun onInit() {
        docIntentModel = DocIntentModel()
    }

    private fun popupSetUp() {
        docTypes.add(getString(keyless.data.utils.android.R.string.passport))
        docTypes.add(getString(keyless.data.utils.android.R.string.emirate_id))

        popupSize = PopupMenu(this, binding.selectDocType, keyless.feature.common.R.style.PopupMenu)
        popupSize.gravity = Gravity.START
        for (i in 0 until (docTypes.size)) {
            popupSize.menu.add(docTypes[i])
        }
        popupSize.setOnMenuItemClickListener { item ->
            binding.selectDocType.setText(item.title)
            binding.docImageFront.setImageBitmap(null)
            binding.docImageBack.setImageBitmap(null)
            binding.buttonFrame.isVisible = false
            binding.txtDocumentStatus.isVisible = false
            binding.txtDocument.isVisible = false
            docIntentModel?.identity2 = ""
            docIntentModel?.identity1 = ""

            if (item.title == getString(keyless.data.utils.android.R.string.passport)) {
                docIntentModel?.documentType = "1"
                showPassport()
            } else if (item.title == getString(keyless.data.utils.android.R.string.emirate_id)) {
                docIntentModel?.documentType = "2"
                showBothImages()
            }
            true
        }
    }


    private fun showBothImages() {
        binding.frontSideTxt.visibility = VISIBLE
        binding.mainLayFront.visibility = VISIBLE

        binding.backSideTxt.visibility = VISIBLE
        binding.mainLayBack.visibility = VISIBLE

        binding.buttonFrame.visibility = GONE
    }

    private fun showPassport() {
        binding.frontSideTxt.visibility = VISIBLE
        binding.mainLayFront.visibility = VISIBLE

        binding.backSideTxt.visibility = GONE
        binding.mainLayBack.visibility = GONE

        binding.buttonFrame.visibility = GONE
    }

    private fun hideAll() {
        binding.frontSideTxt.visibility = GONE
        binding.mainLayFront.visibility = GONE

        binding.backSideTxt.visibility = GONE
        binding.mainLayBack.visibility = GONE

        binding.buttonFrame.visibility = GONE
    }

    private fun onClicks() {


        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        setResult(400)
                        finish()
                    }
                })
            )

        }




        binding.viewBack.setOnClickListener {
            setResult(400)
            finish()
        }

        binding.selectDocType.setOnClickListener {
            popupSize.show()
        }
//        binding.takePic.setOnClickListener {
//            showScanner()
//        }
//            recognizeImage()
        binding.docImageFront.setOnClickListener {
            showScanner(1)
        }

        binding.docImageBack.setOnClickListener {
            showScanner(2)
        }




        binding.txtBtnProceed.setOnClickListener {
            if (docIntentModel?.documentType == "1") {
                if (docIntentModel?.identity1!!.isEmpty()) {
                    defaultDialog(
                        this,
                        getString(
                            keyless
                                .data
                                .utils
                                .android
                                .R
                                .string
                                .the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_the_front_side_of_the_passport
                        ),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })
                } else if (goFurther) {
                    binding.progressLay.isVisible = true
                    FaceSDK.Instance().init(this) { status: Boolean, e: InitException? ->
                        if (!status) {
                            Toast.makeText(
                                this@DocScanActivity,
                                "Init finished with error: " + if (e != null) e.message else "",
                                Toast.LENGTH_LONG
                            ).show()
                            return@init
                        } else {
                            FaceSDK.Instance().startLiveness(this) { it1 ->
                                modell = it1
                                if (modell.bitmap != null) {
                                    val myImageApi = getImageUri(modell.bitmap)
                                    Log.e("myImageApi", myImageApi.path)
                                    Log.e("myImageApi", myImageApi.name)
                                    docIntentModel?.myImageApi = myImageApi.toString()
                                    val options = BitmapFactory.Options()
                                    options.inSampleSize = 4
                                    options.inJustDecodeBounds = false
                                    matchFaces(docSelfi, modell.bitmap!!)
                                } else {
                                    binding.progressLay.isVisible = false
                                }
                            }
                        }
                    }
                } else {
                    binding.progressLay.isVisible = false
                    defaultDialog(
                        this,
                        getString(keyless.data.utils.android.R.string.the_document_you_are_trying_to_upload_is_invalid),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })
                }
            } else if (docIntentModel?.documentType == "2") {
                if (docIntentModel?.identity1!!.isEmpty() && docIntentModel?.identity2!!.isEmpty()) {
                    defaultDialog(
                        this,
                        getString(
                            keyless
                                .data
                                .utils
                                .android
                                .R
                                .string.the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_both_front_and_back_side_of_the_document
                        ),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })

                } else if (docIntentModel?.identity1!!.isEmpty()) {
                    defaultDialog(
                        this,
                        getString(keyless
                            .data
                            .utils
                            .android
                            .R
                            .string
                            .the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_both_front_and_back_side_of_the_document
                        ),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })
                } else if (docIntentModel?.identity2!!.isEmpty()) {
                    defaultDialog(
                        this,
                        getString(keyless
                            .data
                            .utils
                            .android
                            .R
                            .string
                            .the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_both_front_and_back_side_of_the_document
                        ),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })

                } else if (goFurther && isDocumentValidBtn && isExpiredBtn) {
                    binding.progressLay.isVisible = true
                    FaceSDK.Instance().init(this) { status: Boolean, e: InitException? ->
                        if (!status) {
                            Toast.makeText(
                                this@DocScanActivity,
                                "Init finished with error: " + if (e != null) e.message else "",
                                Toast.LENGTH_LONG
                            ).show()
                            return@init
                        } else {
                            FaceSDK.Instance().startLiveness(this) { it1 ->
                                modell = it1
                                if (modell.bitmap != null) {
                                    val myImageApi = getImageUri(modell.bitmap)
                                    docIntentModel?.myImageApi = myImageApi.toString()
                                    val options = BitmapFactory.Options()
                                    options.inSampleSize = 4
                                    options.inJustDecodeBounds = false
                                    matchFaces(docSelfi, modell.bitmap!!)
                                } else {
                                    binding.progressLay.isVisible = false
                                }
                            }
                        }
                    }
                } else {
                    binding.progressLay.isVisible = false
                    defaultDialog(
                        this,
                        getString(keyless.data.utils.android.R.string.the_document_you_are_trying_to_upload_is_invalid),
                        object : OnActionOK {
                            override fun onClickData() {

                            }
                        })
                }
            }
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        setResult(400)
        finish()
    }

    private fun createImageFile(): File {
        // Create an image file name
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale("en")).format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        val externalStorageVolumes =
            ContextCompat.getExternalFilesDirs(this, null)
        val primaryExternalStorage = externalStorageVolumes[0]
        //////////
        val mFile: File
        val fn = primaryExternalStorage.toString() + File.separator + imageFileName + ".jpg"
        mFile = File(fn)
        return mFile
    }

    @SuppressLint("WrongConstant")
    fun showScanner(i: Int) {
        valueForResult = i
        val scannerConfig = ScannerConfig.Builder("FullProcess").build()
        DocumentReader.Instance().showScanner(this, scannerConfig, completion)
        DocumentReader.Instance().processParams().dateFormat = "YYYY-MM-DD"
    }

    @SuppressLint("SuspiciousIndentation")
    private fun displayResults(documentReaderResults: DocumentReaderResults) {

        if (valueForResult == 1) {
            ResultsActivity.results = documentReaderResults

            val initResults = initResults()
            val initCompare = initCompare()

            //images
            var images: MutableList<Attribute>? = null
            var rawImage: MutableList<Attribute>? = null


            initResults.filter { it.type == "Graphics" }.forEach {
                images = it.items
            }

            initResults.filter { it.type == "Raw Image" }.forEach {
                rawImage = it.items
            }

            initResults.filter { it.type == "Visual OCR Extended" }.forEach {
                it.items.forEach { item ->
                    Log.d("MainActivity 1", item.name + "\n")
                    if (item.name == "Identity card number" || item.name == "رقم بطاقة الهوية") {
                        identityNumberPassport = item.value
                        Log.d("valueee", item.value + "\n")
                    }
                }
            }

            binding.docImageFront.setImageBitmap(rawImage?.get(0)?.image)
            var isPassExpired = true

            //name
            documentReaderResults.textResult?.fields?.forEach {
                Log.d("MainActivity 1", it.getFieldName(this) + "\n")
                jsonObject1.addProperty(it.getFieldName(this), it.value)
                if (docIntentModel?.documentType == "1") {
                    if (it.fieldType == 25) {
                        docIntentModel?.name = it.value
                    }
                    if (it.fieldType == 2) {
                        docIntentModel?.documentNumber = it.value
                    }

                    if (it.fieldType == 3) {
                        docIntentModel?.docExpiry = it.value
//                        if (it.value.toString().contains("/")){
//                            var split = it.value.toString().split("/")
//                            if (split[2].length == 2) {
//                                docIntentModel?.expiryDate =
//                                    "20" + split[2] + "-" + split[1] + "-" + split[0]
//                            } else {
//                                docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                            }
//                        }else if (it.value.toString().contains(".")){
//                            var split = it.value.toString().split(".")
//                            if (split[2].length == 2) {
//                                docIntentModel?.expiryDate =
//                                    "20" + split[2] + "-" + split[1] + "-" + split[0]
//                            } else {
//                                docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                            }
//                        }else if (it.value.toString().contains("-")){
//                            var split = it.value.toString().split("-")
//                            if (split[2].length == 2) {
//                                docIntentModel?.expiryDate =
//                                    "20" + split[2] + "-" + split[1] + "-" + split[0]
//                            } else {
//                                docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                            }
//                        }else {
                        docIntentModel?.expiryDate = it.value.toString()
//                        }

                        if (it.validityStatus == 0) {
                            isPassExpired = false
                        } else {
                            isPassExpired = true
                        }

                    }
                } else {
                    if (it.fieldType == 25) {
                        docIntentModel?.name = it.value
                    }
                    if (it.fieldType == 142) {
                        docIntentModel?.documentNumber = it.value
                    }
                }
            }

            var scannedInvalid = true
            var isShowDialog = true
            //id
            documentReaderResults.documentType.forEach {
                docIntentModel?.docType = it.dDescription
                if (docIntentModel?.documentType == "1") {
                    if (docIntentModel?.docType != "Passport") {
                        scannedInvalid = false
                        isShowDialog = true
                        defaultDialog(
                            this,
                            getString(
                                keyless.data.utils.android.R.string.the_scanning_of_the_document_has_failed_please_make_sure_you_are_scanning_the_front_side_of_the_passport // ktlint-disable max-line-length
                            ),
                            object : OnActionOK {
                                override fun onClickData() {
                                    //                        showScanner()
                                }
                            })
                        binding.docImageFront.setImageBitmap(null)
                    } else {
                        isShowDialog = false
                        scannedInvalid = true
                    }
                } else {
                    if (it.dType != 224 && it.dType != 12) {
                        scannedInvalid = false
                        isShowDialog = true
                        defaultDialog(
                            this,
                            getString(
                                keyless.data.utils.android.R.string.the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again // ktlint-disable max-line-length
                            ),
                            object : OnActionOK {
                                override fun onClickData() {
                                    //                        showScanner()
                                }
                            })
                        binding.docImageFront.setImageBitmap(null)
                    } else {
                        isShowDialog = false
                        scannedInvalid = true
                    }
                }
            }


            var identity1 = getImageUri(rawImage?.get(0)?.image)
            Log.e("identity1", identity1.path)
            Log.e("identity1", identity1.name)
            docIntentModel?.identity1 = identity1.toString()

            if (initResults.size >= 3) {
                //saving doc selfi
                images?.first()?.image?.let {
                    docSelfi = it
                    docIntentModel?.docSelfie = docSelfi
                    val documentImage = getImageUri(docIntentModel?.docSelfie)
                    docIntentModel?.documentImageApi = documentImage.toString()
                    Log.e("documentImageApi", documentImage.path)
                    Log.e("documentImageApi", documentImage.name)
                }
                goFurther = true

            } else {
                goFurther = false
                defaultDialog(
                    this,
                    getString(
                        keyless.data.utils.android.R.string
                            .the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again
                    ),
                    object : OnActionOK {
                        override fun onClickData() {
//                        showScanner()
                        }
                    })
                binding.docImageFront.setImageBitmap(null)
            }

            if (docIntentModel?.documentType == "1") {
                if (isShowDialog) {
                    binding.txtDocumentStatus.isVisible = false
                    binding.txtDocument.isVisible = false
                    binding.buttonFrame.visibility = GONE
                } else if (goFurther && scannedInvalid && isPassExpired) {
                    binding.buttonFrame.visibility = VISIBLE
                    binding.txtDocumentStatus.isVisible = true
                    binding.txtDocument.isVisible = true
                    binding.txtDocumentStatus.text = getString(keyless.data.utils.android.R.string.valid)
                    binding.txtDocumentStatus.setTextColor(resources.getColor(keyless.feature.common.R.color.green))
                } else {
                    binding.txtDocumentStatus.isVisible = true
                    binding.txtDocument.isVisible = true
                    binding.txtDocumentStatus.text = getString(keyless.data.utils.android.R.string.invalid)
                    binding.txtDocumentStatus.setTextColor(
                        resources.getColor(keyless.feature.common.R.color.red_negative)
                    )
                    binding.buttonFrame.visibility = GONE
                }
            } else {
                binding.txtDocumentStatus.isVisible = false
                binding.txtDocument.isVisible = false
                binding.buttonFrame.visibility = GONE
            }


        } else {
            ResultsActivity.results = documentReaderResults
            val initResults = initResults()
            var rawImage2: MutableList<Attribute>? = null
            var images2: MutableList<Attribute>? = null
            initResults.filter { it.type == "Raw Image" }.forEach {
                rawImage2 = it.items
            }

            initResults.filter { it.type == "Graphics" }.forEach {
                images2 = it.items
            }

            documentReaderResults.textResult?.fields?.forEach {
                if (it.fieldType == 25) {
                    docIntentModel?.name = it.value
                }
            }

            var valueId = ""
            documentReaderResults.textResult?.fields?.forEach {
                if (it.fieldType == 142) {
                    valueId = it.value.toString()
                    return@forEach
                }
            }

            if (identityNumberPassport != valueId) {
                binding.docImageBack.setImageBitmap(null)
                defaultDialog(
                    this,
                    getString(
                        keyless.data.utils.android.R.string
                            .the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again
                    ),
                    object : OnActionOK {
                        override fun onClickData() {
//                        showScanner()
                        }
                    })
                return
            }

            //expiry
            documentReaderResults.textResult?.fields?.forEach {
                jsonObject2.addProperty(it.getFieldName(this), it.value)
                if (it.fieldType == 3) {
//                    val date = getDateInFormat(it.value!!)
//                    docIntentModel?.docExpiry = it.value!!

//                    docIntentModel?.docExpiry = it.value
//                    if (it.value.toString().contains("/")){
//                        var split = it.value.toString().split("/")
//                        if (split[2].length == 2) {
//                            docIntentModel?.expiryDate =
//                                "20" + split[2] + "-" + split[1] + "-" + split[0]
//                        } else {
//                            docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                        }
//                    }else if (it.value.toString().contains(".")){
//                        val split = it.value.toString().split(".")
//                        if (split[2].length == 2) {
//                            docIntentModel?.expiryDate =
//                                "20" + split[2] + "-" + split[1] + "-" + split[0]
//                        } else {
//                            docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                        }
//                    }else if (it.value.toString().contains("-")){
//                        var split = it.value.toString().split("-")
//                        if (split[2].length == 2) {
//                            docIntentModel?.expiryDate =
//                                "20" + split[2] + "-" + split[1] + "-" + split[0]
//                        } else {
//                            docIntentModel?.expiryDate = split[2] + "-" + split[1] + "-" + split[0]
//                        }
//                    }else {
                    docIntentModel?.expiryDate = it.value.toString()
                    docIntentModel?.docExpiry = it.value!!
                    Log.e("dateNew" + docIntentModel?.docExpiry, "")
//                    }
                }
            }


            //for expiry
            val documentModel =
                Gson().fromJson(documentReaderResults.rawResult, DocumentModel::class.java)
            documentModel.ContainerList?.DocModel?.forEach {

                when (it.status?.detailsOptical?.expiry) {
                    0 -> {
                        isExpiredBtn = false
                        binding.docImageBack.setImageBitmap(null)
                        //"Not Valid"
                        defaultDialog(
                            this,
                            getString(
                                keyless.data.utils.android.R.string.the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again // ktlint-disable max-line-length
                            ),
                            object : OnActionOK {
                                override fun onClickData() {
                                    //                        showScanner()
                                }
                            })
                        //                toast("expiry issue.")
                    }

                    2 -> {
                        isExpiredBtn = false
                        binding.docImageBack.setImageBitmap(null)
                        //Not Valid
                        defaultDialog(
                            this,
                            getString(
                                keyless.data.utils.android.R.string.the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again // ktlint-disable max-line-length
                            ),
                            object : OnActionOK {
                                override fun onClickData() {
                                    //                        showScanner()
                                }
                            })
                        //                toast("expiry issue.")
                    }

                    else -> {
                        binding.docImageBack.setImageBitmap(rawImage2?.get(0)?.image)
                        val identity2 = getImageUri(rawImage2?.get(0)?.image)
                        docIntentModel?.identity2 = identity2.toString()
                        Log.e("identity2", identity2.path)
                        Log.e("identity2", identity2.name)
                        isExpiredBtn = true
                        //OK
                    }
                }
            }


            //for doc type
            documentModel.ContainerList?.DocModel?.forEach {
                val docType = it.OneCandidate?.FDSIDList?.dType
                val countryName = it.OneCandidate?.FDSIDList?.dCountryName
                docType?.let {
                    if (docType == 17) {
                        isDocumentValidBtn = false
                        binding.docImageBack.setImageBitmap(null)
                        defaultDialog(
                            this,
                            getString(
                                keyless.data.utils.android.R.string.the_uploaded_document_is_either_not_valid_or_not_properly_scanned_please_try_again // ktlint-disable max-line-length
                            ),
                            object : OnActionOK {
                                override fun onClickData() {
//                        showScanner()
                                }
                            })
//                return
                    } else {
                        isDocumentValidBtn = true
                    }
                }
            }

            if (isExpiredBtn && isDocumentValidBtn) {
                binding.buttonFrame.isVisible = true
                binding.txtDocumentStatus.isVisible = true
                binding.txtDocument.isVisible = true
                binding.txtDocumentStatus.text = getString(keyless.data.utils.android.R.string.valid)
                binding.txtDocumentStatus.setTextColor(resources.getColor(keyless.feature.common.R.color.green))
            } else {
                binding.buttonFrame.isVisible = false
                binding.txtDocumentStatus.isVisible = true
                binding.txtDocument.isVisible = true
                binding.txtDocumentStatus.text = getString(keyless.data.utils.android.R.string.invalid)
                binding.txtDocumentStatus.setTextColor(
                    resources.getColor(keyless.feature.common.R.color.red_negative)
                )
            }

        }

    }

    private fun getImageUri(image: Bitmap?): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss.SSS", Locale("en")).format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        val filesDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val file = File(filesDir, imageFileName)
        val outputStream = FileOutputStream(file)
        image!!.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        outputStream.flush()
        outputStream.close()
        return file
    }

    private fun initResults(): List<GroupedAttributes> {
        val pickerData = mutableListOf<GroupedAttributes>()
        val attributes = mutableListOf<Attribute>()

        ResultsActivity.results.textResult?.fields?.forEach {
            val name = it.getFieldName(this)
            for (value in it.values) {
                val valid = getValidity(value.field.validityList, value.sourceType)
                val item = Attribute(
                    name!!,
                    value.value,
                    it.lcid,
                    value.pageIndex,
                    valid,
                    value.sourceType
                )
                attributes.add(item)
            }
        }

        ResultsActivity.results.graphicResult?.fields?.forEach {
            val name = it.getFieldName(this) + " [${it.pageIndex}]"
            val image = it.bitmap
            val item = Attribute(name, "", source = it.sourceType, image = image)
            attributes.add(item)
        }

        val types = attributes.map { it.source }.toSet()
        for (type in types) {
            val typed = attributes.filter { it.source == type }.toMutableList()
            val group = GroupedAttributes(Helpers.getResultTypeTranslation(type!!), typed)
            pickerData.add(group)
        }
        pickerData.sortBy { it.type }

        return pickerData
    }

    private fun getValidity(
        list: List<DocumentReaderValidity>,
        type: Int?
    ): Int {
        for (validity in list) {
            if (validity.sourceType == type)
                return validity.status
        }

        return eCheckResult.CH_CHECK_WAS_NOT_DONE
    }


    private fun initCompare(): List<GroupedAttributes> {
        var pickerData = mutableListOf<GroupedAttributes>()

        if (ResultsActivity.results.textResult == null)
            return pickerData

        val values = ResultsActivity.results.textResult!!.fields.map { it.values }.flatten()
        val comparisonTypes = values.map { it.sourceType }.toSet()

        val typePairs = combinationsFrom(comparisonTypes.toMutableList(), 2)

        for (pair in typePairs) {
            val groupType =
                "${Helpers.getResultTypeTranslation(pair[1])} - ${
                    Helpers.getResultTypeTranslation(
                        pair[0]
                    )
                }"
            val comparisonGroup = GroupedAttributes(groupType, mutableListOf(), pair[1], pair[0])
            pickerData.add(comparisonGroup)
        }

        for (field in ResultsActivity.results.textResult!!.fields)
            for (comparison in field.comparisonList)
                tryToAddValueToGroup(
                    field.getFieldName(this)!!,
                    comparison,
                    pickerData
                )

        for (group in pickerData)
            group.items = group.items.toSet().toMutableList()
        pickerData = pickerData.filter { it.items.size > 0 }.toMutableList()

//        if (pickerData.size == 0)
//            turnTabOff(1)

        return pickerData
    }

    private fun tryToAddValueToGroup(
        name: String,
        comparison: DocumentReaderComparison,
        groups: MutableList<GroupedAttributes>
    ) {
        for (index in groups.indices) {
            val group = groups[index]
            if (group.comparisonLHS == null || group.comparisonRHS == null) return
            val sourceEquality =
                comparison.sourceTypeRight == group.comparisonLHS || comparison.sourceTypeRight == group.comparisonRHS
            val targetEquality =
                comparison.sourceTypeLeft == group.comparisonLHS || comparison.sourceTypeLeft == group.comparisonRHS
            if (sourceEquality && targetEquality) {
                val item =
                    Attribute(
                        name,
                        null,
                        valid = comparison.status,
                        source = comparison.sourceTypeLeft
                    )
                groups[index].items.add(item)
                break
            }
        }
    }

    private fun <T> combinationsFrom(
        elements: MutableList<T>,
        taking: Int
    ): MutableList<MutableList<T>> {
        if (elements.size < taking) return mutableListOf()
        if (elements.isEmpty() || taking <= 0) return mutableListOf(mutableListOf())
        if (taking == 1) return elements.map { mutableListOf(it) }.toMutableList()

        val combinations = mutableListOf<MutableList<T>>()
        while (elements.size > 1) {
            val element = elements[0]
            elements.removeAt(0)
            combinations += combinationsFrom(elements, taking - 1).map {
                (mutableListOf(element) + it).toMutableList()
            }
        }

        return combinations
    }


    //gallery
//    fun recognizeImage() {
//        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
//            != PackageManager.PERMISSION_GRANTED
//        ) {
//            ActivityCompat.requestPermissions(
//                this,
//                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
//                PERMISSIONS_REQUEST_READ_EXTERNAL_STORAGE
//            )
//        } else
//            createImageBrowsingRequest()
//    }

    private fun createImageBrowsingRequest() {
        val intent = Intent()
        intent.type = "image/*"
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        intent.action = Intent.ACTION_GET_CONTENT
        startActivityForResult(Intent.createChooser(intent, "Select Picture"), 101)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == 60 || resultCode == 0) {
            setResult(70)
            finish()
        }
    }

    private fun matchFaces(first: Bitmap, second: Bitmap) {
//        dialogWithProgress(this, "Please wait...", callBack = { title, okBtn, progress ->
        val firstImage = MatchFacesImage(first, ImageType.LIVE, true)
        val secondImage = MatchFacesImage(second, ImageType.LIVE, true)
        val matchFacesRequest = MatchFacesRequest(arrayListOf(firstImage, secondImage))

        FaceSDK.Instance()
            .matchFaces(matchFacesRequest) { matchFacesResponse: MatchFacesResponse ->
                val split = MatchFacesSimilarityThresholdSplit(matchFacesResponse.results, 0.75)
                if (split.matchedFaces.size > 0) {
                    val similarity = split.matchedFaces[0].similarity
                    val similarity2digit =
                        String.format(Locale.ENGLISH, "%.2f", similarity).toDouble()
                    docIntentModel?.similarity = similarity2digit.toString()

                    Log.e("similarity", docIntentModel?.similarity.toString())
                    //"Similarity: " + String.format("%.2f", similarity * 100) + "%"
                } else {
                    docIntentModel?.similarity = "0.00"
                    //show dialog that image not matched.
                }

                val jsonObjectMain = JsonObject()
                jsonObjectMain.add("front", jsonObject1)
                jsonObjectMain.add("back", jsonObject2)
                jsonArray.add(jsonObjectMain)

                val lockDetails =
                    intent.getParcelableExtra<LocksListResponse.LocksModel>("lockDetails")
                startActivityForResult(
                    Intent(
                        this@DocScanActivity,
                        DocResultActivity::class.java
                    ).putExtra("model", docIntentModel).putExtra("lockDetails", lockDetails)
                        .putExtra("jsonArray", jsonArray.toString()), 100
                )
                binding.progressLay.isVisible = false
            }
//        })
    }

    fun arabicToEnglish(str: String): String {
        var result = ""
        var en = '0'
        for (ch in str) {
            en = ch
            when (ch) {
                '۰' -> en = '0'
                '۱' -> en = '1'
                '۲' -> en = '2'
                '۳' -> en = '3'
                '۴' -> en = '4'
                '۵' -> en = '5'
                '۶' -> en = '6'
                '۷' -> en = '7'
                '۸' -> en = '8'
                '۹' -> en = '9'
            }
            result = "${result}$en"
        }
        return result
    }


}

internal fun normalizeDate(date: String): String {
    val parts = date
        .replace(".", "-")
        .replace("/", "-")
        .replace("\\", "-")
        .map { if (Character.isDigit(it)) Character.getNumericValue(it).digitToChar() else it }
        .joinToString("")
        .fixDate()
        .split("-")
        .map { if (it.length == 1) "0$it" else it }
        .toMutableList()

    if ((parts[1].toIntOrNull() ?: 0) > 12) {
        val temp = parts[1]
        parts[1] = parts[2]
        parts[2] = temp
    }

    return parts.joinToString("-")
}

private fun String.fixDate(): String {
    return when {
        this.matches("\\d{1,2}-\\d{1,2}-\\d{2}".toRegex()) -> {
            val parts = this.split("-")
            "20${parts[2]}-${parts[0]}-${parts[1]}"
        }

        this.matches("\\d{1,2}-\\d{1,2}-\\d{4}".toRegex()) -> {
            val parts = this.split("-")
            "${parts[2]}-${parts[1]}-${parts[0]}"
        }

        else -> this
    }
}