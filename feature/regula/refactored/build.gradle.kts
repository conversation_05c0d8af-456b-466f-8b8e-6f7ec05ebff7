plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":core-common"))
    implementation(project(":core-regula-common"))
    implementation(project(":core-regula-documents"))
    implementation(project(":core-regula-face"))
    implementation(project(":data-network-android"))
    implementation(project(":domain-common"))
    implementation(project(":domain-regula"))
    implementation(project(":feature-common"))

    implementation("androidx.biometric:biometric-ktx:1.2.0-alpha05")

    implementation(keyless.androidx.lifecycle.livedata)

}