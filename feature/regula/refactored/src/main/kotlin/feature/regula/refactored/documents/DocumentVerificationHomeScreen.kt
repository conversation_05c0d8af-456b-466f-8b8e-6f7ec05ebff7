package feature.regula.refactored.documents

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import domain.regula.models.Event
import feature.common.compose.screens.AppTitledPage
import feature.common.compose.theme.AppTheme
import feature.regula.refactored.components.LoadingScreen
import feature.regula.refactored.documents.models.Samples
import feature.regula.refactored.documents.models.UILoadingState
import feature.regula.refactored.documents.models.UIReadyState
import feature.regula.refactored.documents.models.UIScreenState
import keyless.feature.regula.refactored.R

@Composable
internal fun DocumentVerificationHomeScreen(
    modifier: Modifier = Modifier,
    state: UIScreenState,
    onBackClick: () -> Unit,
    onEvent: (Event) -> Unit
) {
    AppTitledPage(
        modifier = modifier,
        title = stringResource(id = R.string.upload_documents),
        isBackEnabled = state !is UILoadingState,
        onBackPress = onBackClick
    ) {
        when (state) {
            is UILoadingState -> {
                LoadingScreen()
            }

            is UIReadyState -> {
                DocumentVerificationReadyScreen(state = state, onEvent = onEvent)
            }
        }
    }
}

@Composable
private fun Sample() {
    DocumentVerificationHomeScreen(
        state = Samples.sampleState(),
        onBackClick = {},
        onEvent = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun CheckInHomeScreenPreview() {
    AppTheme {
        Sample()
    }
}