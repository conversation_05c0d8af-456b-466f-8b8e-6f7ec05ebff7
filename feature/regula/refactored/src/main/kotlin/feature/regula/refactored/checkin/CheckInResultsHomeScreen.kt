package feature.regula.refactored.checkin

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import domain.regula.models.Event
import feature.common.compose.screens.AppTitledPage
import feature.common.compose.theme.AppTheme
import feature.regula.refactored.checkin.models.Samples
import feature.regula.refactored.checkin.models.UILoadingState
import feature.regula.refactored.checkin.models.UIReadyState
import feature.regula.refactored.checkin.models.UIScreenState
import keyless.feature.regula.refactored.R

@Composable
fun CheckInResultsHomeScreen(
    modifier: Modifier = Modifier,
    state: UIScreenState,
    onBackClick: () -> Unit,
    onEvent: (Event) -> Unit
) {
    AppTitledPage(
        modifier = modifier,
        title = stringResource(id = R.string.check_in),
        isBackEnabled = state !is UILoadingState,
        onBackPress = onBackClick
    ) {
        when (state) {
            is UILoadingState -> {
                // Loading state
            }

            is UIReadyState -> {
                CheckInResultsReadyScreen(state = state, onEvent = onEvent)
            }
        }
    }
}

@Composable
private fun Sample() {
    CheckInResultsHomeScreen(
        state = Samples.sampleState(LocalContext.current),
        onBackClick = {},
        onEvent = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun CheckInResultsHomeScreenPreview() {
    AppTheme {
        Sample()
    }
}