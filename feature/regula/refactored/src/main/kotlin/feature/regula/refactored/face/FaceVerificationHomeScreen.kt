package feature.regula.refactored.face

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import domain.regula.models.Event
import feature.common.compose.screens.AppTitledPage
import feature.common.compose.theme.AppTheme
import feature.regula.refactored.components.LoadingScreen
import feature.regula.refactored.face.models.Samples
import feature.regula.refactored.face.models.UILoadingState
import feature.regula.refactored.face.models.UIReadyState
import feature.regula.refactored.face.models.UIScreenState
import keyless.feature.regula.refactored.R

@Composable
fun FaceVerificationHomeScreen(
    modifier: Modifier = Modifier,
    state: UIScreenState,
    onBackClick: () -> Unit,
    onEvent: (Event) -> Unit
) {
    AppTitledPage(
        modifier = modifier,
        title = stringResource(id = R.string.face_verification),
        isBackEnabled = state !is UILoadingState,
        onBackPress = onBackClick
    ) {
        when (state) {
            is UILoadingState -> {
                LoadingScreen()
            }

            is UIReadyState -> {
                FaceVerificationReadyScreen(state = state, onEvent = onEvent)
            }
        }
    }
}

@Composable
private fun Sample() {
    FaceVerificationHomeScreen(
        state = Samples.sampleState(),
        onBackClick = {},
        onEvent = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun FaceVerificationHomeScreenPreview() {
    AppTheme {
        Sample()
    }
}