package feature.regula.refactored.checkin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import domain.regula.models.SideEffect
import feature.common.compose.theme.AppTheme
import feature.common.dialogs.appToast
import presentation.common.feature.components.StatusListener
import presentation.common.feature.components.StatusListenerImpl
import feature.regula.refactored.checkin.models.Samples
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.androidx.viewmodel.ext.android.viewModel

class CheckInResultsFragment : Fragment(), StatusListener by StatusListenerImpl() {

    private val viewModel by viewModel<CheckInResultsAndroidViewModel>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        registerStatusListener(this)
        return ComposeView(requireContext()).apply {
            setContent {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)

                AppTheme { Screen() }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel
            .sideEffects
            .onEach {
                when (it) {
                    SideEffect.NavigateToFaceVerification -> {}

                    SideEffect.NavigateToResultsScreen -> {}

                    is SideEffect.CheckInSuccess -> {
                        requireActivity().appToast(it.message)
                        requireActivity().setResult(80)
                        requireActivity().finish()
                    }
                }
            }
            .launchIn(lifecycleScope)
    }

    @Composable
    private fun Screen() {
        val state = viewModel
            .screenStream
            .collectAsStateWithLifecycle(initialValue = Samples.initState())
            .value

        CheckInResultsHomeScreen(
            state = state,
            onEvent = viewModel::onEvent,
            onBackClick = { findNavController().navigateUp() }
        )
    }
}