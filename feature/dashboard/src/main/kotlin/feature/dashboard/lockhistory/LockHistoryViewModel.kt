package feature.dashboard.lockhistory

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import data.network.android.ApiUtils
import data.network.android.models.HistoryLockResponse
import data.network.android.models.LockHistoryModel
import data.utils.android.CommonValues
import presentation.common.domain.repositories.ErrorMessageHandler
import data.utils.android.settings.SharedPreferenceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LockHistoryViewModel : ViewModel() {

    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    private val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress
    private var _getResponseHistory = MutableLiveData<HistoryLockResponse>()
    val getResponseHistory: LiveData<HistoryLockResponse> = _getResponseHistory

    fun updateLogs(
        context: Context,
        token: String,
        listBackup: ArrayList<LockHistoryModel>,
        isDirectUpload: Boolean = false,
        callBack: (HistoryLockResponse) -> Unit
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val pref = SharedPreferenceUtils.getInstance(context)
            val history: ArrayList<LockHistoryModel>
            if (isDirectUpload) {
                history = listBackup
            } else {
                history = pref.getLockHistoryBackup()
                for (i in listBackup) {
                    history.add(
                        LockHistoryModel(
                            device_model = i.device_model,
                            lockId = i.lockId,
                            mobile_id = i.mobile_id,
                            created_at = CommonValues.dateForLockHistory(),
                            company_id = i.company_id
                        )
                    )
                }
                pref.setLockHistoryBackup(history)
            }

            ApiUtils.updateLockLogs(token, history, {
                (it as HistoryLockResponse)
                _progress.value = false
                pref.setLockHistoryBackup(arrayListOf())
//                _getResponseLog.value = it
                callBack(it)
            }) {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        }
    }

    fun hitLocksAccessApi(
        token: String,
        id: String,
        jsonObject: JsonObject,
        paginationStatus: String
    ) {
        _progress.value = false
        if (paginationStatus == "no") {
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.getLockAccessLog(token, id, jsonObject, {
                (it as HistoryLockResponse)
                _progress.value = false
                it.whichScreen = 0
                _getResponseHistory.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun hitLocksSharedApi(
        token: String,
        id: String,
        jsonObject: JsonObject,
        paginationStatus: String
    ) {
        _progress.value = false
        if (paginationStatus == "no") {
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.getLockSharedLog(token, id, jsonObject, {
                (it as HistoryLockResponse)
                _progress.value = false
                it.whichScreen = 1
                _getResponseHistory.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }
}