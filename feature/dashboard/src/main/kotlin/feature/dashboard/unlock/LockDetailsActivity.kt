package feature.dashboard.unlock

import android.Manifest
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.IntentSender.SendIntentException
import android.content.pm.PackageManager
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import com.iseo.v364droidsdk.AndroidContext
import com.iseo.v364sdk.services.exception.V364SdkException
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileCredentialService
import com.iseo.v364sdk.services.mobilecredentialservice.IMobileHermesCmdService
import com.iseo.v364sdk.services.mobilecredentialservice.model.ILock
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockBatteryStatus
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.ILockResponse
import com.iseo.v364sdk.services.mobilecredentialservice.model.user.state.ErrorCode
import com.iseo.v364sdk.services.scanservice.IScanManagerService
import com.iseo.v364sdk.services.scanservice.model.IKeyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILegacyScanInfo
import com.iseo.v364sdk.services.scanservice.model.ILockScanInfo
import com.iseo.v364sdk.services.scanservice.model.IMobileCredentialScanInfo
import com.iseo.v364sdk.services.scanservice.model.IScanBTManagerEvent
import com.khoiron.actionsheets.ActionSheet
import com.khoiron.actionsheets.callback.ActionSheetCallBack
import com.messerschmitt.mstblelib.MSTBleDevice
import com.messerschmitt.mstblelib.MSTBleInterface
import com.messerschmitt.mstblelib.MSTBleUtils
import com.messerschmitt.mstblelib.MSTsmartkey
import com.ttlock.bl.sdk.api.ExtendedBluetoothDevice
import com.ttlock.bl.sdk.api.TTLockClient
import com.ttlock.bl.sdk.callback.ControlLockCallback
import com.ttlock.bl.sdk.callback.GetLockTimeCallback
import com.ttlock.bl.sdk.callback.ScanLockCallback
import com.ttlock.bl.sdk.constant.ControlAction
import com.ttlock.bl.sdk.entity.ControlLockResult
import com.ttlock.bl.sdk.entity.LockError
import com.ttlock.bl.sdk.util.LogUtil
import core.location.common.LocationRepository
import core.location.google.AndroidLocationRepository
import core.locks.logs.models.LockLogActionType
import core.lock.airbnk.AirBnkManager
import core.lock.airbnk.AndroidAirBnkManager
import core.lock.airbnk.models.AirBnkLock
import core.lock.airbnk.models.AirBnkStatus
import core.lock.airbnk.models.AndroidAirBnkLock
import core.permissions.manager.AndroidNetworkManager
import core.permissions.manager.NetworkManager
import data.common.preferences.Constants
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.common.preferences.Roles.SYSTEM_MANAGER
import data.network.android.LocksListResponse
import data.network.android.log
import data.network.android.models.LockHistoryModel
import data.network.android.models.MoreInfoModel
import data.tedee.models.LockState
import data.tedee.repositories.TedeeRepository
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADD_ADAPTER
import data.utils.android.CommonValues.Companion.ADMIN
import data.utils.android.CommonValues.Companion.DATE_FORMAT
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.CommonValues.Companion.INTEGRATOR
import data.utils.android.CommonValues.Companion.OWNER
import data.utils.android.applications.ServiceProvider
import data.utils.android.applications.ServiceProvider.mobileCredentialConnLockService
import data.utils.android.applications.ServiceProvider.mobileCredentialUserCmdService
import data.utils.android.common.BleLockScanData
import data.utils.android.common.FileUtils.readStringFromFile
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import domain.common.ErrorHandler
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.appToast
import feature.common.navigation.logoutAndNavToDashboard
import feature.dashboard.moreinfo.MoreInfoAdapter
import feature.settings.checkin.CheckInStartActivity
import keyless.feature.dashboard.R
import keyless.feature.dashboard.databinding.ActivityLockDetailsBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.koin.android.ext.android.inject
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockBleOpenData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import rayo.logicsdk.utils.Log
import rayo.logicsdk.utils.TimeUtils
import java.io.UnsupportedEncodingException
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.LocalTime
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.time.Duration.Companion.hours

class LockDetailsActivity :
    AppCompatActivity(),
    MoreInfoAdapter.ClickOnPrivacy,
    IScanBTManagerEvent {

    private var timeEnded: Boolean = false
    private var decryptedAccessKey: String = ""
    private var mssFound: Int = 0
    private lateinit var repeatUnLock: MSTBleDevice
    private lateinit var checkWithLockDate: Date
    private lateinit var checkWithLockDateStart: Date
    private var lockFind: Boolean = false
    private var isValid: Boolean = false
    private var lockDetails: LocksListResponse.LocksModel = LocksListResponse.LocksModel()
    private lateinit var adapterMoreInfo: MoreInfoAdapter
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var stopSearching = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    var autCase = "0"
    private var mLockBasicInfo: LockBasicInfo? = null
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private var deviceName: String = ""
    lateinit var mViewModel: LockDetailsViewModel
    private lateinit var scanManagerService: IScanManagerService
    private lateinit var mobileCredentialService: IMobileCredentialService
    private var lock: ILockScanInfo? = null
    private var info: IMobileCredentialScanInfo? = null
    var disconnectByMe = false
    var REQUEST_CHECK_SETTINGS = 3
    private var iseoLock: ILock? = null
    private lateinit var mobileCredentialHermesCmdService: IMobileHermesCmdService
    private var mBleUtils: MSTBleUtils? = null
    private var mstBleInterface: MSTBleInterface? = null
    private var mstSmartkey: MSTsmartkey? = null
    private var DEVUID = ""
    lateinit var binding: ActivityLockDetailsBinding
    private var isPasscodeSupported = false

    private val airbnk: AirBnkManager by inject()
    private val handler: ErrorHandler by inject()
    private var airbnkListenStarted = false
    private var airbnkLastStatus = AirBnkStatus.LOCKED

    private val tedee: TedeeRepository by inject()
    private var lastTedeeState = LockState.Locked

    private val network: NetworkManager by inject()
    private val location: LocationRepository by inject()

    private var receiver = object : BroadcastReceiver() {
        @SuppressLint("SetTextI18n")
        override fun onReceive(p0: Context?, br: Intent?) {
            br?.let {
                it.getParcelableExtra<LocksListResponse.LocksModel>("model")?.let { model ->
                    lockDetails.lock = model.lock
                    lockDetails.unit_id = model.unit_id
                    lockDetails.property_details = model.property_details
                    setUpMoreInfoRecycler()
                    binding.txtLockName.text = lockDetails.lock.name
                    binding.tvFloor.text =
                        lockDetails.property_details.floor + " " + getString(
                            keyless.data.utils.android.R.string.txt_floor
                        )
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onStart() {
        super.onStart()
        val filter = IntentFilter()
        filter.addAction(CommonValues.LOCK_UPDATE)
//        registerReceiver(receiver, filter)
        this.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED);

    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(receiver)
        LocalBroadcastManager.getInstance(this@LockDetailsActivity)
            .unregisterReceiver(MSTBleStatusChangeReceiver)
        try {
            when (lockDetails.lock.provider) {
                CommonValues.keyless -> {
                    isScan = false
                    mBluetoothLeScan!!.stopReceiver()
//                    mBleScanCallback.finishScan()
                    mBleLockSdk?.disconnect()
                    disconnectByMe = true
                }

                CommonValues.messerSchimtt -> {
                    disconnectByMe = true
                    LocalBroadcastManager.getInstance(this)
                        .unregisterReceiver(MSTBleStatusChangeReceiver)
                }

                CommonValues.iseo -> {
                    scanManagerService.stopScan()
                    disconnectIseoLock()
                }

                CommonValues.lockWise -> {
                    airbnk.disconnect()
                }
            }
            finish()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        (airbnk as? AndroidAirBnkManager)?.bind(this)
        (network as? AndroidNetworkManager)?.bind(this)
        (location as? AndroidLocationRepository)?.bind(this)
        binding = ActivityLockDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        kotlin.runCatching { AndroidContext.setContext(this) }
        initz()
        apiChecking()
        setUpMoreInfoRecycler()
        deviceName = CommonValues.getDeviceNameApi()
        binding.animatedDots.animate()
        askPermission()
        clickEvents()
        binding.clRipple.isClickable = false
//        clRipple.isFocusable = false
//        clRipple.isFocusableInTouchMode = true
        observerInit()
    }

    private fun apiChecking() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        mViewModel.getCheckUser(sharePrefs.token, jsonObject).observe(this@LockDetailsActivity) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (!it.success) {
                logoutAndNavToDashboard(it.message, this@LockDetailsActivity)
            }

        }
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                ),
                50
            )
        }
    }

    private fun initializationCommon() {

        when (lockDetails.lock.provider) {
            CommonValues.keyless -> {
                initLock()
            }

            CommonValues.oji, CommonValues.linko, Constants.primeSource -> {
                initOji()
            }

            CommonValues.messerSchimtt -> {
                try {
                    loadSmartkey()
                } catch (e: UnsupportedEncodingException) {
                    e.printStackTrace()
                }
                initMesserSchmit()

            }

            CommonValues.iseo -> {
                try {
                    scanManagerService = ServiceProvider.scanManagerService
                    mobileCredentialService = ServiceProvider.mobileCredentialService
                    mobileCredentialHermesCmdService =
                        ServiceProvider.mobileCredentialHermesCmdService
                    scanManagerService.setScanBTManagerEvent(this@LockDetailsActivity)
                    scanISEOLock()
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }

            CommonValues.lockWise -> {
                lifecycleScope.launch {
                    handler.async(onError = {
                        lockDetails.lock.log(
                            action = LockLogActionType.ScanError,
                            message = it.stackTraceToString(),
                            data = decryptedAccessKey
                        )
                        appToast(it.message ?: "Error happened")
                    }
                    ) { initAirBnk() }
                }
            }

            CommonValues.tedee -> {
                lifecycleScope.launch {
                    handler.async(onError = {
                        lockDetails.lock.log(
                            action = LockLogActionType.ScanError,
                            message = it.stackTraceToString(),
                            data = decryptedAccessKey
                        )
                        appToast(it.message ?: "Error happened")
                    }
                    ) { initTedee() }
                }
            }
        }
    }

    private fun initOji() {
        TTLockClient.getDefault().prepareBTService(this);
        TTLockClient.getDefault().startScanLock(object : ScanLockCallback {
            override fun onScanLockSuccess(device: ExtendedBluetoothDevice?) {
                if (lockDetails.lock.unique_key.contains(device!!.name) || device.name.contains(lockDetails.lock.unique_key)) {
                    lockDetails.lock.log(
                        action = LockLogActionType.ScanLock,
                        message = "Scan Lock Successfully!",
                        data = decryptedAccessKey
                    )

                    findLockDone()
                    TTLockClient.getDefault().stopScanLock()
                }
            }

            override fun onFail(error: LockError?) {
                lockDetails.lock.log(
                    action = LockLogActionType.ScanError,
                    message = error.toString(),
                    data = decryptedAccessKey
                )

            }
        })


    }

    private fun loadSmartkey() {
        mstSmartkey = MSTsmartkey(
            decryptedAccessKey,
            DEVUID.toByteArray(
                StandardCharsets.UTF_8
            )
        )
    }

    private fun initMesserSchmit() {
        mBleUtils = MSTBleUtils(this, -85, -75)
        mstBleInterface = MSTBleInterface(mBleUtils, this, mstSmartkey)

        val mIsBluetoothOn: Boolean = mBleUtils!!.isBluetoothOn
        val mIsBluetoothLePresent: Boolean = mBleUtils!!.isBluetoothLeSupported

        mBleUtils!!.askUserToEnableBluetoothIfNeeded()
        if (mIsBluetoothOn && mIsBluetoothLePresent) {
            LocalBroadcastManager.getInstance(this).registerReceiver(
                MSTBleStatusChangeReceiver,
                makeGattUpdateIntentFilter()
            )
            mstBleInterface!!.mstOpenDoor(lockDetails.lock.unique_key)


        } else {
            toast(
                getString(
                    keyless.data.utils.android.R.string.bluetooth_not_enabled_by_user_or_it_will_not_support_by_device
                )
            )
        }
    }


    private fun makeGattUpdateIntentFilter(): IntentFilter {
        val intentFilter = IntentFilter()
        intentFilter.addAction(MSTBleInterface.ACTION_START_OPEN)
        intentFilter.addAction(MSTBleInterface.ACTION_SCAN_TIMEOUT)
        intentFilter.addAction(MSTBleInterface.ACTION_CONNECT_TO)
        intentFilter.addAction(MSTBleInterface.ACTION_SPECIAL_NAME)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR)
        intentFilter.addAction(MSTBleInterface.ACTION_OPEN_THE_DOOR_FAILED)
        intentFilter.addAction(MSTBleInterface.ACTION_FINISH_OPEN)
        intentFilter.addAction(MSTBleInterface.ACTION_DATE_TIME)
        intentFilter.addAction(MSTBleInterface.ACTION_BATT_STATUS)
        intentFilter.addAction(MSTBleInterface.ERROR_DEVICE_RESPONSE)
        intentFilter.addAction(MSTBleInterface.ERROR_DISCOVER_SERVICES)
        intentFilter.addAction(MSTBleInterface.ERROR_CONNECT)
        intentFilter.addAction(MSTBleInterface.ERROR_DOOR_STATE)
        intentFilter.addAction(MSTBleInterface.ERROR_KEY_RESULT)
        intentFilter.addAction(MSTBleInterface.ERROR_ENABLE_NOTIFY)
        intentFilter.addAction(MSTBleInterface.ERROR_AUTH_FAILED)
        return intentFilter
    }


    private val MSTBleStatusChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val action1 = intent.getParcelableExtra<MSTBleDevice>("data")
            val action2 = intent.getBooleanExtra("dataBattery", false)
            var dateLock: Date? = null

            if (action1 != null) {
                repeatUnLock = action1
            }
            //*********************
            if (action == MSTBleInterface.ACTION_START_OPEN) {

            }
            //*********************
            if (action == MSTBleInterface.ACTION_SCAN_TIMEOUT) {

            }
            //*********************
            if (action == MSTBleInterface.ACTION_CONNECT_TO) {
                mssFound = 1
                binding.clRipple.progressDrawable =
                    ContextCompat.getDrawable(this@LockDetailsActivity, R.drawable.bg_grey_rounded)
                binding.ivLock.background =
                    ContextCompat.getDrawable(this@LockDetailsActivity, R.drawable.ic_close_lock)
                binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
                binding.clRipple.progress = 0
                binding.txtConnecting.isVisible = false
                binding.animatedDots.isVisible = false
                binding.ivLock.isVisible = true
                binding.tvUnlock.isVisible = true
                binding.clRipple.isClickable = true
                Log.e("opening", "7")
                binding.ripplView.stopRippleAnimation()
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Connected",
                    data = ""
                )

            }
            //*********************
            if (action == MSTBleInterface.ACTION_SPECIAL_NAME) {
            }
            //*********************
            if (action == MSTBleInterface.ACTION_OPEN_THE_DOOR) {

            }
            //*********************
            if (action == MSTBleInterface.ACTION_OPEN_THE_DOOR_FAILED) {

            }

            if (action == MSTBleInterface.ACTION_DATE_TIME) {
                if (action1 != null) {
                    dateLock = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale("en")).parse(action1.dt)
                    println(dateLock.time)
                }
            }

            if (action == MSTBleInterface.ACTION_BATT_STATUS) {
                Log.e("// battery Status cddhgdty $action2", "")

                var batteryCount = 1000
                if (!action2) {
                    batteryCount = 1
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery0)
                } else if (action2) {
                    batteryCount = 3
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery3)
                }
                mViewModel.postBatteryPercentage(
                    sharePrefs.token,
                    batteryCount, lockDetails.lock.internal_id
                )

                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Battery Status",
                    data = batteryCount.toString()
                )
            }

            //*********************
            if (action == MSTBleInterface.ACTION_FINISH_OPEN) {
                if (dateLock != null) {
                    if (dateLock.after(checkWithLockDate) && Preferences.userRole.get() == GUEST) {
                        defaultDialog(
                            this@LockDetailsActivity,
                            getString(keyless.data.utils.android.R.string.access_denied),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                        binding.ripplView.stopRippleAnimation()
                        binding.clRipple.isClickable = true
                        Log.e("opening", "8")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Access Denied",
                            data = ""
                        )
                    } else {
                        binding.clRipple.progressDrawable =
                            ContextCompat.getDrawable(
                                this@LockDetailsActivity,
                                R.drawable.bg_grey_rounded
                            )
                        binding.ivLock.background =
                            ContextCompat.getDrawable(
                                this@LockDetailsActivity,
                                R.drawable.ic_close_lock
                            )
                        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
                        binding.clRipple.progress = 0
                        binding.txtConnecting.isVisible = false
                        binding.animatedDots.isVisible = false
                        binding.ivLock.isVisible = true
                        binding.tvUnlock.isVisible = true
                        binding.ripplView.stopRippleAnimation()
                        binding.clRipple.isClickable = true
                        Log.e("opening", "9")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Open Successfully",
                            data = ""
                        )
                        apiLockHistory()
                    }
                } else {
                    binding.clRipple.progressDrawable =
                        ContextCompat.getDrawable(
                            this@LockDetailsActivity,
                            R.drawable.bg_grey_rounded
                        )
                    binding.ivLock.background =
                        ContextCompat.getDrawable(
                            this@LockDetailsActivity,
                            R.drawable.ic_close_lock
                        )
                    binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
                    binding.clRipple.progress = 0
                    binding.txtConnecting.isVisible = false
                    binding.animatedDots.isVisible = false
                    binding.ivLock.isVisible = true
                    binding.tvUnlock.isVisible = true
                    binding.ripplView.stopRippleAnimation()
                    binding.clRipple.isClickable = true
                    Log.e("opening", "10")
//                mssFound = 0
                    apiLockHistory()
                }
            }

            //*********************
            if (action == MSTBleInterface.ERROR_CONNECT) {
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Connection Error",
                    data = ""
                )
            }
            //*********************
            if (action == MSTBleInterface.ERROR_DEVICE_RESPONSE) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_DISCOVER_SERVICES) {

            }
            //*********************
            if (action == MSTBleInterface.ERROR_DOOR_STATE) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_ENABLE_NOTIFY) {

            }
            //*********************
            if (action == MSTBleInterface.ERROR_KEY_RESULT) {
            }
            //*********************
            if (action == MSTBleInterface.ERROR_AUTH_FAILED) {

            }
        }
    }

    private fun scanISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            withContext(Dispatchers.IO) {
                scanManagerService.stopScan()
                try {
                    scanManagerService.startScanLock(false)
                } catch (e: V364SdkException) {

                }
            }
        }
        lockDetails.lock.log(
            action = LockLogActionType.Unlock,
            message = "Scanning Lock",
            data = ""
        )
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }

                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {

                    val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                            initializationCommon()
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                }
            }
        }
    }


    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this@LockDetailsActivity,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }


    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(keyless.data.utils.android.R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null),
                        ), 10
                    )
                }
            })
    }

    private fun observerInit() {
        mViewModel.getResponse.observe(this) {
            CommonValues.refreshApi = true
            if (it.valueStatus == 1) {
                adapterMoreInfo.updateSwitch()
                openLockChecks()
            }
        }

        mViewModel.getResponseLog.observe(this) {
        }

        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        mViewModel.error.observe(this) {
            toast(it)
        }

    }


    @SuppressLint("SetTextI18n", "SimpleDateFormat")
    private fun initz() {
        mViewModel = ViewModelProvider(this)[LockDetailsViewModel::class.java]
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        isPasscodeSupported = lockDetails.lock.provider == CommonValues.oji ||
                lockDetails.lock.provider == CommonValues.linko ||
                lockDetails.lock.provider == CommonValues.tedee
        if (lockDetails.lock.encrypted_key.isNotEmpty()) {
            Log.e(" encrypted key " + lockDetails.lock.encrypted_key + " " + sharePrefs.uuid)
            val decryptedKey = CommonValues.decrypt(lockDetails.lock.encrypted_key, sharePrefs.uuid)
            if (decryptedKey == null) {
                decryptedAccessKey = ""
            } else if (lockDetails.lock.encrypted_key.isNotEmpty()) {
                decryptedAccessKey = decryptedKey
            }
        } else {
            if (lockDetails.lock.provider == CommonValues.keyless) {
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Encrypted key not found",
                    data = ""
                )
            }
        }

        binding.ripplView.startRippleAnimation()
        binding.txtConnecting.isVisible = true
        binding.animatedDots.isVisible = true
        binding.txtLockName.text = lockDetails.lock.name
        binding.tvFloor.text =
            lockDetails.property_details.floor + " " + getString(keyless.data.utils.android.R.string.txt_floor)

        if (Preferences.userRole.get() == GUEST) {
            if (lockDetails.lock.primary) {
                if (lockDetails.checkin && lockDetails.companyCheckin) {
                    binding.txtCheckIn.isVisible = true
                    binding.txtCheckIn.text =
                        getString(keyless.data.utils.android.R.string.start_check_in) +
                                lockDetails.totalCheckins.toString() + " " +
                                getString(keyless.data.utils.android.R.string.completed)
                } else {
                    binding.txtCheckIn.isVisible = false
                }
            }
        } else {
            binding.txtCheckIn.isVisible = false
        }

        DEVUID = lockDetails.lock.lock_uid


        when (Preferences.userRole.get()) {
            GUEST -> {
                binding.ivMap.isVisible = false
                binding.ivBatteryCicular.setImageResource(keyless.feature.common.R.drawable.map_colored)
                binding.ivBatteryCicular.setOnClickListener {
                    binding.ivMap.callOnClick()
                }
            }

            else -> {
                when (lockDetails.lock.battery_level) {
                    0 -> {
                        binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery0)
                    }

                    1 -> {
                        binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery1)
                    }

                    2 -> {
                        binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery2)
                    }

                    3 -> {
                        binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery3)
                    }

                    else -> {
                        binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery3)
                    }
                }
            }
        }

        if (Preferences.role.get() != OWNER && Preferences.role.get() != ADMIN) {
            val startDate =
                CommonValues.formattedDateOnlyEn(
                    lockDetails.assignment?.assignment_data?.valid_from!!.split("+")[0]
                )
            val endDate =
                CommonValues.formattedDateOnlyEn(
                    lockDetails.assignment?.assignment_data?.valid_to!!.split("+")[0]
                )


            val sdf = SimpleDateFormat(DATE_FORMAT, Locale("en"))
            sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
            val currentDate = sdf.format(Date())

            val sdf1 = SimpleDateFormat(DATE_FORMAT, Locale("en"))
            val startDateTimeMain: Date = sdf1.parse(startDate)!!
            val endDateTimeMain: Date = sdf1.parse(endDate)!!
            val currentDateTimeMain: Date = sdf1.parse(currentDate)!!
            checkWithLockDate = endDateTimeMain
            checkWithLockDateStart = startDateTimeMain
            if ((currentDateTimeMain.after(startDateTimeMain) || currentDateTimeMain == startDateTimeMain)
                && (currentDateTimeMain.before(endDateTimeMain) || currentDateTimeMain == endDateTimeMain)
            ) {
                val valueWeek = LocalDate.now().dayOfWeek.value
                for (i in lockDetails.assignment?.time_ranges!!) {
                    if (i.allowed_days[valueWeek - 1] == 1) {
                        val startHour: String = if (i.time_slot.start_hour.toString() == "0") {
                            "00"
                        } else {
                            if (i.time_slot.start_hour.toString().trim().length <= 1) {
                                "0" + i.time_slot.start_hour.toString()
                            } else {
                                i.time_slot.start_hour.toString()
                            }
                        }
                        val startMin: String = if (i.time_slot.start_min.toString() == "0") {
                            "00"
                        } else {
                            if (i.time_slot.start_min.toString().trim().length <= 1) {
                                "0" + i.time_slot.start_min.toString()
                            } else {
                                i.time_slot.start_min.toString()
                            }
                        }
                        val endHour: String = if (i.time_slot.end_hour.toString() == "0") {
                            "00"
                        } else {
                            if (i.time_slot.end_hour.toString().trim().length <= 1) {
                                "0" + i.time_slot.end_hour.toString()
                            } else {
                                i.time_slot.end_hour.toString()
                            }
                        }
                        val endMin: String = if (i.time_slot.end_min.toString() == "0") {
                            "00"
                        } else {
                            if (i.time_slot.end_min.toString().trim().length <= 1) {
                                "0" + i.time_slot.end_min.toString()
                            } else {
                                i.time_slot.end_min.toString()
                            }
                        }
                        val timeStart = LocalTime.parse("$startHour:$startMin")
                        val timeEnd = LocalTime.parse("$endHour:$endMin")
                        val currentTimeLocal: LocalTime =
                            if (startHour.toInt() > 12 || endHour.toInt() > 12) {
                                val sdfNew = SimpleDateFormat("HH:mm", Locale("en"))
                                sdfNew.timeZone =
                                    TimeZone.getTimeZone(Preferences.timeZoneName.get())
                                val str = sdfNew.format(Date())

                                LocalTime.parse(str)
                            } else {
                                val sdfNew = SimpleDateFormat("hh:mm", Locale("en"))
                                sdfNew.timeZone =
                                    TimeZone.getTimeZone(Preferences.timeZoneName.get())
                                val str = sdfNew.format(Date())
                                LocalTime.parse(str)
                            }
                        isValid =
                            currentTimeLocal.isAfter(timeStart) && currentTimeLocal.isBefore(timeEnd)
                        //                        break
                    } else {
                        isValid = false
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Access not allowed for this day",
                            data = ""
                        )
                    }
                }
            } else {
                isValid = false
                timeEnded = true
                if (lockDetails.lock.provider == CommonValues.keyless) {
                    lockDetails.lock.log(
                        action = LockLogActionType.RoutineHours,
                        message = "Access not allowed",
                        data = ""
                    )
                }
            }
        }


    }


    private fun initLock() {
        mBleName = ArrayList()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            if (!stopSearching) {
                                addAdapterItemRange(msg.obj as BleLockScanData)
                            }
                        } catch (e: Exception) {
                        }
                    }
                }
            }
        }
    }


    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {

                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }
        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(lockDetails.lock.unique_key)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            findLockDone()

        } else {
            isScan = true
        }
    }

    private fun findLockDone() {
        stopSearching = true
        binding.clRipple.progressDrawable =
            ContextCompat.getDrawable(this, R.drawable.bg_grey_rounded)
        binding.ivLock.background = ContextCompat.getDrawable(this, R.drawable.ic_close_lock)
        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
        if (!lockFind) {
            binding.clRipple.isClickable = true
            Log.e("opening", "15")
        }
        Log.e("opening", "11")
        binding.clRipple.progress = 0
        binding.txtConnecting.isVisible = false
        binding.animatedDots.isVisible = false
        binding.ivLock.isVisible = true
        binding.tvUnlock.isVisible = true
        binding.ripplView.stopRippleAnimation()
    }

    private fun onStopScan() {

        scanManagerService.stopScan()
    }

    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            val config = readStringFromFile(applicationContext)
            var lockCodeClass = JSON.parseObject(
                config,
                LockCodeClass::class.java
            )
            if (null == lockCodeClass) {
                lockCodeClass = LockCodeClass()
            }

            lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
//            showMessage(
//                """
//                init sdk
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@LockDetailsActivity
//            )
//            Log.d(
//                "TAG",
//                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
//            )
        }

        override fun connect(resultBean: ResultBean<*>) {
//            showMessage(
//                """
//            connect
//            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//            """.trimIndent(),
//                this@LockDetailsActivity
//            )

            lockDetails.lock.log(
                action = LockLogActionType.Unlock,
                message = "Connected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )


        }

        override fun authentication(resultBean: ResultBean<*>) {
//                """ authentication ${
//                    JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    )
//                }""", this@LockDetailsActivity
//            )
//            showMessage(


            if (resultBean.isRet) {
                val mLockBasicInfo = resultBean.obj as (LockBasicInfo)
//                mBleLockSdk?.setLockInfo(mLockBasicInfo)
                var batteryCount = 1000
                if (mLockBasicInfo.battery in 0..25) {
                    batteryCount = 0
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery0)
                } else if (mLockBasicInfo.battery in 26..50) {
                    batteryCount = 1
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery1)
                } else if (mLockBasicInfo.battery in 51..75) {
                    batteryCount = 2
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery2)
                } else if (mLockBasicInfo.battery > 75) {
                    batteryCount = 3
                    binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery3)
                }

                mViewModel.postBatteryPercentage(
                    sharePrefs.token,
                    batteryCount, lockDetails.lock.internal_id
                )

                val lockDate = mLockBasicInfo.lockTimeClass.lockTime
                val sdf = SimpleDateFormat(DATE_FORMAT, Locale("en"))
                sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                val lockDateTimeZone = sdf.format(lockDate)
                val sdf1 = SimpleDateFormat(DATE_FORMAT, Locale("en"))
                val lockDateTimeZoneFinal: Date = sdf1.parse(lockDateTimeZone)!!

                if (Preferences.role.get() != OWNER && Preferences.role.get() != ADMIN) {
                    if (((lockDateTimeZoneFinal.after(checkWithLockDate) || lockDateTimeZoneFinal.equals(
                            checkWithLockDate
                        )) || ((lockDateTimeZoneFinal.before(
                            checkWithLockDateStart
                        )) || lockDateTimeZoneFinal.equals(checkWithLockDateStart))) && Preferences.userRole.get() == GUEST
                    ) {
                        defaultDialog(
                            this@LockDetailsActivity,
                            getString(keyless.data.utils.android.R.string.access_denied),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                        binding.ripplView.stopRippleAnimation()
                        binding.clRipple.isClickable = true
                        Log.e("opening", "12")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Shared time expire for today",
                            data = ""
                        )

                    } else {
                        openLock(resultBean)
                    }
                } else {
                    openLock(resultBean)
                }


            } else {
                if (JSON.toJSONString(
                        resultBean,
                        SerializerFeature.WriteDateUseDateFormat
                    ).lowercase(Locale.getDefault()).contains("timeout")
                ) {
                    if (!isFinishing) {
                        defaultDialog(
                            this@LockDetailsActivity,
                            getString(keyless.data.utils.android.R.string.the_lock_could_not_be_connected),
                            object : OnActionOK {
                                override fun onClickData() {
                                    binding.clRipple.progressDrawable =
                                        ContextCompat.getDrawable(
                                            this@LockDetailsActivity,
                                            R.drawable.bg_grey_rounded
                                        )
                                    binding.ivLock.background = ContextCompat.getDrawable(
                                        this@LockDetailsActivity,
                                        R.drawable.ic_close_lock
                                    )
                                    binding.clRipple.progress = 0
                                    mBleLockSdk?.disconnect()
                                    binding.tvUnlock.text = getString(
                                        keyless.data.utils.android.R.string.tap_to_unlock_door
                                    )
                                    binding.ripplView.stopRippleAnimation()
                                    binding.txtConnecting.isVisible = false
                                    binding.animatedDots.isVisible = false
                                    binding.clRipple.isClickable = true
                                    Log.e("opening", "13")

                                }
                            })
                    }
                    lockDetails.lock.log(
                        action = LockLogActionType.Unlock,
                        message = "Timeout",
                        data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
                    )

                } else {
//                    toast(
//                        JSON.toJSONString(
//                            resultBean,
//                            SerializerFeature.WriteDateUseDateFormat
//                        )
//                    )
                }
            }

        }

        override fun disConnect(resultBean: ResultBean<*>?) {

            if (!disconnectByMe) {
//                txtConnecting.isVisible = true
//                animatedDots.isVisible = true
//                tv_unlock.isVisible = false
                binding.ripplView.stopRippleAnimation()
                autCase = "0"
//                initLock()
            }
            lockDetails.lock.log(
                action = LockLogActionType.Unlock,
                message = "Disconnected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun registerLock(resultBean: ResultBean<*>) {

        }

        override fun getLockInfo(resultBean: ResultBean<*>) {

        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
//            showMessage(
//                """
//            setLockInfo
//            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//            """.trimIndent(),
//                this@LockDetailsActivity
//            )

        }

        override fun setLockTime(resultBean: ResultBean<*>?) {

        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {

        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {

        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {

        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {

        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {

        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {

        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {

        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {

            lockDetails.lock.log(
                action = LockLogActionType.Unlock,
                message = "Opened Successfully",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )


        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {

        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {

        }

        override fun setCalendar(resultBean: ResultBean<*>?) {

        }

        override fun resetLock(resultBean: ResultBean<*>?) {

        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {

        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {

        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {

        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {

        }

        override fun onReport(resultBean: ResultBean<*>?) {

        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {

        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {

        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {

        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {

        }

        override fun disPlay(resultBean: ResultBean<*>) {

        }

        override fun setTempCard(p0: ResultBean<*>?) {


        }

        override fun deleteTempCard(p0: ResultBean<*>?) {

        }

        override fun findTempCard(p0: ResultBean<*>?) {

        }

    }

    private fun openLock(resultBean: ResultBean<*>) {
        binding.ripplView.stopRippleAnimation()
        binding.txtConnecting.isVisible = false
        binding.animatedDots.isVisible = false
        binding.ivLock.isVisible = true
        binding.tvUnlock.isVisible = true
        ObjectAnimator.ofInt(binding.clRipple, "progress", 100)
            .setDuration(1000)
            .start()
        binding.clRipple.progressDrawable =
            ContextCompat.getDrawable(
                this@LockDetailsActivity,
                R.drawable.bg_green_rounded
            )
        openChineseLock()
        lockDetails.lock.log(
            action = LockLogActionType.Unlock,
            message = "Authentication Successful",
            data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
        )
    }


    private fun checkName(bleName: String): Boolean {
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }


    @SuppressLint("ResourceAsColor")
    private fun clickEvents() {

        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        backCommon()
                    }
                })
            )
        }




        binding.txtCheckIn.setOnClickListener {
            startActivityForResult(
                Intent(
                    this,
                    CheckInStartActivity::class.java
                )
                    .putExtra("lockDetails", lockDetails)
                    .putExtra("bookingNumber", lockDetails.booking_number), 100
            )

        }

        binding.clRipple.setOnClickListener {
            if (Preferences.isAdminLogin()) {
                defaultDialog(
                    this,
                    getString(keyless.data.utils.android.R.string.disabled_in_admin_mode),
                    object : OnActionOK {
                        override fun onClickData() {

                        }
                    })
            } else {
                Log.e("openingggggg")
                binding.clRipple.isClickable = false
                binding.clRipple.isFocusable = false
                openLockChecks()
            }
        }

        binding.viewBack.setOnClickListener {
            backCommon()

        }

        binding.ivMap.setOnClickListener {
            if (lockDetails.property_details.latitude.isNotEmpty()) {
                val strUri =
                    "http://maps.google.com/maps?q=loc:" + lockDetails.property_details.latitude + "," + lockDetails.property_details.longitude + " (" + "Location" + ")"
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(strUri))
                startActivity(intent)
            }
        }

    }


    private fun backCommon() {
        try {
            when (lockDetails.lock.provider) {
                CommonValues.keyless -> {
                    isScan = false
                    mBluetoothLeScan!!.stopReceiver()
//                    mBleScanCallback.finishScan()
                    mBleLockSdk?.disconnect()
                    disconnectByMe = true
                }

                CommonValues.messerSchimtt -> {
                    disconnectByMe = true
                    LocalBroadcastManager.getInstance(this)
                        .unregisterReceiver(MSTBleStatusChangeReceiver)
                }

                CommonValues.iseo -> {
                    scanManagerService.stopScan()
                    disconnectIseoLock()
                }

                CommonValues.lockWise -> {
                    lifecycleScope.launch { handler.async { airbnk.disconnect() } }
                }

            }
            finish()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

    }

    private fun openISEOLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                var response: ILockResponse? = null

                withContext(Dispatchers.IO) {
                    iseoLock = mobileCredentialConnLockService.connect(lock)
//                    mobileCredentialHermesCmdService.enterInMaintenanceMode(iseoLock)
//                    mobileCredentialHermesCmdService.readDeviceInfo(iseoLock)
//                    mobileCredentialHermesCmdService.writeClock(iseoLock)
                    response = mobileCredentialUserCmdService.openLock(iseoLock)
                    if (response?.errorCode?.code == ErrorCode.OUTOFDATEVALIDITY) {
                        Log.e("infoLock: " + response?.errorCode?.code)
                        CoroutineScope(Dispatchers.Main).launch {
                            defaultDialog(
                                this@LockDetailsActivity,
                                "Time Expired, Please Contact to Admin.",
                                object : OnActionOK {
                                    override fun onClickData() {
                                        binding.tvUnlock.text = getString(
                                            keyless.data.utils.android.R.string.tap_to_unlock_door
                                        )
                                        binding.ripplView.stopRippleAnimation()
                                        binding.txtConnecting.isVisible = false
                                        binding.animatedDots.isVisible = false
                                        binding.clRipple.isClickable = true
                                    }
                                })
                            lockDetails.lock.log(
                                action = LockLogActionType.Unlock,
                                message = "Opened Error " + response?.errorCode?.code,
                                data = response.toString()
                            )
                        }

                    } else if (response?.errorCode?.code == ErrorCode.OUTOFTIMERANGE) {
                        CoroutineScope(Dispatchers.Main).launch {
                            Log.e("infoLock: " + response?.errorCode?.code)
                            defaultDialog(
                                this@LockDetailsActivity,
                                "Out Of Time Range, Please Contact To Admin",
                                object : OnActionOK {
                                    override fun onClickData() {
                                        binding.tvUnlock.text = getString(
                                            keyless.data.utils.android.R.string.tap_to_unlock_door
                                        )
                                        binding.ripplView.stopRippleAnimation()
                                        binding.txtConnecting.isVisible = false
                                        binding.animatedDots.isVisible = false
                                        binding.clRipple.isClickable = true
                                    }
                                })
                            lockDetails.lock.log(
                                action = LockLogActionType.Unlock,
                                message = "Opened Error " + response?.errorCode?.code,
                                data = response.toString()
                            )
                        }
                    } else {
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Opened Successfully",
                            data = response.toString()
                        )

                        val battery = getBattery(response!!.batteryStatus)
                        mViewModel.postBatteryPercentage(
                            sharePrefs.token,
                            battery, lockDetails.lock.internal_id
                        )
                        runOnUiThread {
                            binding.ripplView.stopRippleAnimation()
                            binding.txtConnecting.isVisible = false
                            binding.animatedDots.isVisible = false
                            binding.ivLock.isVisible = true
                            binding.tvUnlock.isVisible = true
                            binding.ivLock.background = ContextCompat.getDrawable(
                                this@LockDetailsActivity,
                                R.drawable.ic_open_lock
                            )
                            binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.door_unlocked)
                            ObjectAnimator.ofInt(binding.clRipple, "progress", 100)
                                .setDuration(1000)
                                .start()
                            binding.clRipple.progressDrawable =
                                ContextCompat.getDrawable(
                                    this@LockDetailsActivity,
                                    R.drawable.bg_green_rounded
                                )
                            setLockTimer(5000L)
                        }

                        apiLockHistory()

                    }

                }

            } catch (e: V364SdkException) {
                e.printStackTrace()
            }
        }
    }

    private fun apiLockHistory() {
        val backup = arrayListOf(
            LockHistoryModel(
                "1",
                deviceName,
                lockDetails.lock._id,
                sharePrefs.uuid,
                CommonValues.dateForLockHistory(),
                lockDetails.owner_id
            )
        )
        mViewModel.updateLogs(
            this@LockDetailsActivity,
            sharePrefs.token,
            backup
        ) {

        }

    }

    private fun getBattery(batteryStatus: ILockBatteryStatus): Int {
        var batteryCount = 1000

        when (batteryStatus.batteryStatus.name) {
            "BATTERY_OK" -> {
                batteryCount = 3
            }

            "BATTERY_LOW" -> {
                batteryCount = 2
            }

            "BATTERY_VERYLOW" -> {
                batteryCount = 1
            }

            "BATTERY_EXTRALOW" -> {
                batteryCount = 0
            }

        }

        return batteryCount
    }

    @Deprecated("Deprecated in Java")
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        backCommon()
    }

    private fun disconnectIseoLock() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                withContext(Dispatchers.IO) {
                    mobileCredentialHermesCmdService.disconnect(iseoLock)
                }
            } catch (e: V364SdkException) {
            }
        }
        lockDetails.lock.log(
            action = LockLogActionType.Unlock,
            message = "Disconnected",
            data = ""
        )


    }

    private fun openLockChecks() {
        val isPrimaryUser = lockDetails.lock.primary
        val isPrivacy = lockDetails.lock.privacy_mode
        val isPrivacyOwner = lockDetails.lock.privacy_owner
        var isAccessToDND = false
        var showDialogAccess = 0

        if (Preferences.role.get() != OWNER && Preferences.role.get() != ADMIN) {
            val now = Clock.System.now()
            val last = runCatching { Instant.parse(Preferences.lastOnlineLocksFetchDateTime.get()) }.getOrNull()

            if (last != null && now - last > 48.hours) {
                defaultDialog(
                    this,
                    getString(keyless.data.utils.android.R.string.offline_48_hr_notification),
                    object : OnActionOK {
                        override fun onClickData() {
                            lifecycleScope.launch {
                                network.enableNetwork()
                                CommonValues.refreshApi = true
                                backCommon()
                            }
                        }
                    }
                )
                return
            }
        }

        if (Preferences.userRole.get() == GUEST) {
            if (!isPrimaryUser && isPrivacy) {
                showDialogAccess = 1
                defaultDialog(
                    this,
                    getString(keyless.data.utils.android.R.string.lock_not_accessible),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
                binding.clRipple.isClickable = true
                Log.e("opening", "1")
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Secondary user has no access to unlock the door in privacy mode",
                    data = ""
                )
            } else {
                if (isPrimaryUser && isPrivacy) {
                    isAccessToDND = true
                    lockDetails.lock.log(
                        action = LockLogActionType.Unlock,
                        message = "DND mode enable",
                        data = ""
                    )
                }
            }

        } else if (Preferences.role.get() == Roles.VIEWER_ACCESS || Preferences.role.get() == Roles.CUSTOMER_SERVICES) {
            if (isPrivacy) {
                showDialogAccess = 1
                defaultDialog(
                    this,
                    getString(keyless.data.utils.android.R.string.lock_not_accessible),
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
                binding.clRipple.isClickable = true
                Log.e("opening", "2")
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Secondary user has no access to unlock the door in privacy mode",
                    data = ""
                )

            } else if (isPrivacy && !isPrivacyOwner) {
                isAccessToDND = true
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "DND mode enable",
                    data = ""
                )
            }
        }

        if (isAccessToDND) {
            showDndPopup()
        } else {
            if (showDialogAccess != 1) {
                if (lockDetails.assignment?.assignment_data?.time_profile_id?.name == "forever_routine") {
                    if (timeEnded) {
                        defaultDialog(
                            this,
                            getString(keyless.data.utils.android.R.string.access_denied),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                        binding.clRipple.isClickable = true
                        Log.e("opening", "3")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Secondary user has no access to unlock the door in privacy mode",
                            data = ""
                        )
                    } else {
                        openAllLocks()
                    }
                } else {
                    if (isValid) {
                        openAllLocks()
                    } else {
                        defaultDialog(
                            this,
                            getString(keyless.data.utils.android.R.string.access_denied),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                        binding.clRipple.isClickable = true
                        Log.e("opening", "3")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Secondary user has no access to unlock the door in privacy mode",
                            data = ""
                        )
                    }
                }

            }
        }

    }

    private fun openAllLocks() {
        binding.ripplView.startRippleAnimation()
        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.connecting_to_door_lock)
        binding.clRipple.isClickable = false
        when (lockDetails.lock.provider) {
            CommonValues.keyless -> {
                connectLock()
            }

            CommonValues.oji, CommonValues.linko, Constants.primeSource -> {
                openOjiLock()
            }

            CommonValues.messerSchimtt -> {
                if (mssFound == 1) {
                    Handler().postDelayed({
                        binding.ripplView.stopRippleAnimation()
                        binding.txtConnecting.isVisible = false
                        binding.animatedDots.isVisible = false
                        binding.ivLock.isVisible = true
                        binding.tvUnlock.isVisible = true
                        binding.ivLock.background = ContextCompat.getDrawable(
                            this@LockDetailsActivity,
                            R.drawable.ic_open_lock
                        )
                        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.door_unlocked)
                        ObjectAnimator.ofInt(binding.clRipple, "progress", 100)
                            .setDuration(1000)
                            .start()
                        binding.clRipple.progressDrawable =
                            ContextCompat.getDrawable(
                                this@LockDetailsActivity,
                                R.drawable.bg_green_rounded
                            )
                    }, 2500)

                    mstBleInterface!!.openDoor(repeatUnLock)
                }
            }

            CommonValues.iseo -> {
                openISEOLock()
            }

            CommonValues.lockWise -> {
                lifecycleScope.launch {
                    handler.async(onError = {
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Opened Error " + it.message,
                            data = lockDetails.lock.unique_key
                        )
                        handleAirBnkLockView(AirBnkStatus.LOCKED.isUnlocked)
                        handleAirBnkLockView(airbnkLastStatus.isUnlocked)
                        appToast(it.message ?: "Error Happened")
                    }) { openAirBnk() }
                }
            }

            CommonValues.tedee -> {
                lifecycleScope.launch {
                    handler.async(onError = {
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Opened Error " + it.message,
                            data = lockDetails.lock.unique_key
                        )
                        handleTedeeLockView(lastTedeeState)
                        appToast(it.message ?: "Error Happened")
                    }) { openTedee() }
                }
            }
        }

    }

    private fun openOjiLock() {
        TTLockClient.getDefault().getLockTime(decryptedAccessKey, object : GetLockTimeCallback {
            override fun onFail(p0: LockError?) {
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Get lock time error\n${p0!!.errorMsg.toString()}",
                    data = decryptedAccessKey
                )
            }

            override fun onGetLockTimeSuccess(p0: Long) {

//                val lockDate = p0
                val sdf = SimpleDateFormat(DATE_FORMAT, Locale("en"))
                sdf.timeZone = TimeZone.getTimeZone(Preferences.timeZoneName.get())
                val lockDateTimeZone = sdf.format(p0)
                val sdf1 = SimpleDateFormat(DATE_FORMAT, Locale("en"))
                val lockDateTimeZoneFinal: Date = sdf1.parse(lockDateTimeZone)!!


                if (Preferences.role.get() != OWNER && Preferences.role.get() != ADMIN) {
                    if (((lockDateTimeZoneFinal.after(checkWithLockDate) || lockDateTimeZoneFinal.equals(
                            checkWithLockDate
                        )) || ((lockDateTimeZoneFinal.before(
                            checkWithLockDateStart
                        )) || lockDateTimeZoneFinal.equals(checkWithLockDateStart))) && Preferences.userRole.get() == GUEST
                    ) {
                        defaultDialog(
                            this@LockDetailsActivity,
                            getString(keyless.data.utils.android.R.string.access_denied),
                            object : OnActionOK {
                                override fun onClickData() {
                                }
                            })
                        binding.ripplView.stopRippleAnimation()
                        binding.clRipple.isClickable = true
                        Log.e("opening", "12")
                        lockDetails.lock.log(
                            action = LockLogActionType.Unlock,
                            message = "Shared time expire for today",
                            data = ""
                        )

                    } else {
                        openLockOji()
                    }
                } else {
                    openLockOji()
                }

            }

        })
    }

    private fun openLockOji() {
        TTLockClient.getDefault()
            .controlLock(ControlAction.UNLOCK, decryptedAccessKey, object : ControlLockCallback {
                override fun onControlLockSuccess(controlLockResult: ControlLockResult) {
                    apiLockHistory()
                    mViewModel.postBatteryPercentage(
                        sharePrefs.token,
                        controlLockResult.battery, lockDetails.lock.internal_id
                    )

                    runOnUiThread {
                        binding.ripplView.stopRippleAnimation()
                        binding.txtConnecting.isVisible = false
                        binding.animatedDots.isVisible = false
                        binding.ivLock.isVisible = true
                        binding.tvUnlock.isVisible = true
                        binding.ivLock.background = ContextCompat.getDrawable(
                            this@LockDetailsActivity,
                            R.drawable.ic_open_lock
                        )
                        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.door_unlocked)
                        ObjectAnimator.ofInt(binding.clRipple, "progress", 100)
                            .setDuration(1000)
                            .start()
                        binding.clRipple.progressDrawable =
                            ContextCompat.getDrawable(
                                this@LockDetailsActivity,
                                R.drawable.bg_green_rounded
                            )
                        setLockTimer(5000L)
                    }

                    lockDetails.lock.log(
                        action = LockLogActionType.Unlock,
                        message = "Unlock Successfully!",
                        data = ""
                    )
                }

                override fun onFail(error: LockError) {
                    LogUtil.d("lockAction:$error")
                    lockDetails.lock.log(
                        action = LockLogActionType.Unlock,
                        message = error.toString(),
                        data = ""
                    )
                }
            })

    }


    private fun showDndPopup() {
        val builder = AlertDialog.Builder(this)
        builder.setMessage(getString(keyless.data.utils.android.R.string.lock_is_in_dnd))
            .setPositiveButton(
                getString(keyless.data.utils.android.R.string.ok)
            ) { dialog, id ->
                mViewModel.hitPrivacyLockApi(
                    sharePrefs.token,
                    lockDetails.lock._id,
                    "off", 1
                )
                dialog.dismiss()
            }.setNegativeButton(
                getString(keyless.feature.common.R.string.text_cancel)
            ) { dialog, id ->
                dialog.dismiss()
            }
        val dialog = builder.create()
        dialog.show()
        binding.clRipple.isClickable = true
        Log.e("opening", "4")

    }

    @SuppressLint("MissingPermission")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            initializationCommon()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        } else if (resultCode == 90) {
            CommonValues.refreshApi = true
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        lockDetails = intent.getParcelableExtra("lockDetails")!!
        isPasscodeSupported = lockDetails.lock.provider == CommonValues.oji ||
                lockDetails.lock.provider == CommonValues.linko ||
                lockDetails.lock.provider == CommonValues.tedee
        Log.e("//" + lockDetails)

    }


    private fun openChineseLock() {
        binding.clRipple.isClickable = false
        binding.ivLock.background = ContextCompat.getDrawable(this, R.drawable.ic_open_lock)
        binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.door_unlocked)
        val lockBleOpenData = LockBleOpenData()
        val nowTime = Calendar.getInstance()
        nowTime.add(Calendar.MINUTE, -1)
        lockBleOpenData.beginTime = TimeUtils.dateFromNotYMDHMS(
            TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })
        nowTime.add(Calendar.MINUTE, 1)
        lockBleOpenData.endTime = TimeUtils.dateFromNotYMDHMS(
            TimeUtils.dateToNotYMDHMS(nowTime.time).toString().trim { it <= ' ' })
        mBleLockSdk?.bleOpenLock(lockBleOpenData)
        apiLockHistory()
        setLockTimer(8000L)
    }

    private fun setLockTimer(time: Long) {
        try {
            val handler = Handler()
            val runnable = Runnable {
                run {
                    binding.clRipple.progressDrawable =
                        ContextCompat.getDrawable(this, R.drawable.bg_grey_rounded)
                    binding.ivLock.background =
                        ContextCompat.getDrawable(this, R.drawable.ic_close_lock)
                    binding.clRipple.progress = 0
                    binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
                    binding.ripplView.stopRippleAnimation()
                    binding.txtConnecting.isVisible = false
                    binding.animatedDots.isVisible = false
                    binding.clRipple.isClickable = true
                    binding.clRipple.isFocusable = true
                    Log.e("opening", "5")
                    lockFind = true
                    binding.ripplView.stopRippleAnimation()
//                    mBluetoothLeScan!!.stopReceiver()
                    isScan = false
                    mBleLockSdk?.disconnect()
                }
            }
            handler.postAtTime(runnable, System.currentTimeMillis() + time)
            handler.postDelayed(runnable, time)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setUpMoreInfoRecycler() {
        val list = ArrayList<MoreInfoModel>()
        var hideServiceOption = false
        val laundryNumber = lockDetails.property_details.laundary_number
        val groceryNumber = lockDetails.property_details.grocery_number

        if (laundryNumber.isEmpty() && groceryNumber.isEmpty()) {
            hideServiceOption = true
        }

        //check for whatsapp and support
        var addSupportButton = true
        if (lockDetails.property_details.support_call_number.isEmpty()
            && lockDetails.property_details.support_whatsapp_number.isEmpty()
        ) {
            addSupportButton = false
        }

        if (Preferences.userRole.get() == INTEGRATOR) {
            if (Preferences.role.get() == OWNER || Preferences.role.get() == ADMIN || Preferences.role.get() == SYSTEM_MANAGER) {
                if (!hideServiceOption) {
                    list.add(
                        MoreInfoModel(
                            getString(keyless.data.utils.android.R.string.property_services),
                            0
                        )
                    )
                }
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_history), 1))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_info), 2))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_privacy), 3))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_share_access), 4))
                if (addSupportButton) {
                    list.add(
                        MoreInfoModel(getString(keyless.data.utils.android.R.string.text_support), 5)
                    )
                }
            } else if (Preferences.role.get() == Roles.VIEWER_ACCESS || Preferences.role.get() == Roles.CUSTOMER_SERVICES) {
                if (!hideServiceOption) list.add(
                    MoreInfoModel(
                        getString(keyless.data.utils.android.R.string.property_services),
                        0
                    )
                )
//                list.add(MoreInfoModel(getString(R.string.text_history), 1))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_info), 2))
//                list.add(MoreInfoModel(getString(R.string.text_lock_privacy), 3))
//                list.add(MoreInfoModel(getString(R.string.text_share_access), 4))
                if (addSupportButton) {
                    list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_support), 5))
                }
            } else {
                if (!hideServiceOption) {
                    list.add(
                        MoreInfoModel(
                            getString(keyless.data.utils.android.R.string.property_services),
                            0
                        )
                    )
                }
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_history), 1))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_info), 2))
                list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_share_access), 4))
                if (addSupportButton) {
                    list.add(
                        MoreInfoModel(getString(keyless.data.utils.android.R.string.text_support), 5)
                    )
                }
            }
        } else {
            if (!hideServiceOption) {
                list.add(
                    MoreInfoModel(
                        getString(keyless.data.utils.android.R.string.property_services),
                        0
                    )
                )
            }
            list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_history), 1))
            list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_info), 2))
            list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_lock_privacy), 3))
            list.add(MoreInfoModel(getString(keyless.data.utils.android.R.string.text_share_access), 4))
            if (addSupportButton) {
                list.add(
                    MoreInfoModel(getString(keyless.data.utils.android.R.string.text_support), 5)
                )
            }
        }

        if (isPasscodeSupported && lockDetails.passcode.isNotEmpty()) {
            list.add(MoreInfoModel(getString(R.string.view_passcode), 500))
        }

        if (lockDetails.lock.provider == CommonValues.lockWise) {
            list.add(MoreInfoModel(getString(R.string.lock_settings), 501))
        }

        binding.rvMoreInfo.layoutManager = LinearLayoutManager(this)
        adapterMoreInfo = MoreInfoAdapter(list, this, lockDetails)
        binding.rvMoreInfo.adapter = adapterMoreInfo
    }

    override fun apiLockPrivacy(status: String) {
        mViewModel.hitPrivacyLockApi(
            sharePrefs.token,
            lockDetails.lock._id,
            status, 2
        )
    }

    override fun openServicesSheet(lockDetails: LocksListResponse.LocksModel, isSupport: Boolean) {
        val data: ArrayList<String> = ArrayList()
        val title: String
        val supportCallNumber = lockDetails.property_details.support_call_number
        val whatsapp = lockDetails.property_details.support_whatsapp_number

        if (isSupport) {
            title = getString(keyless.data.utils.android.R.string.text_support)
            if (supportCallNumber.isNotEmpty()) {
                data.add(getString(keyless.data.utils.android.R.string.call))
            }
            if (whatsapp.isNotEmpty()) {
                data.add(getString(keyless.data.utils.android.R.string.whatsapp))
            }

        } else {
            val laundryNumber = lockDetails.property_details.laundary_number
            val groceryNumber = lockDetails.property_details.grocery_number

            if (laundryNumber.isNotEmpty()) {
                data.add(getString(keyless.data.utils.android.R.string.call_laundry))
            }
            if (groceryNumber.isNotEmpty()) {
                data.add(getString(keyless.data.utils.android.R.string.call_maintenance))
            }

            title = getString(keyless.data.utils.android.R.string.property_services)
        }


        ActionSheet(this, data)
            .setTitle(title)
            .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
            .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .create(object : ActionSheetCallBack {
                override fun data(data: String, position: Int) {
                    when (position) {
                        0 -> {
                            if (isSupport) {
                                if (supportCallNumber.isEmpty()) {
                                    callWhatsapp()
                                } else {
                                    callSupport()
                                }
                            } else {
                                if (lockDetails.property_details.laundary_number.isEmpty()) {
                                    callGrocery()
                                } else {
                                    callLaundry()
                                }
                            }


                        }

                        1 -> {
                            if (isSupport) {
                                callWhatsapp()
                            } else {
                                callGrocery()
                            }
                        }
                    }

                }

                private fun callSupport() {
                    CommonValues.openPhone(
                        this@LockDetailsActivity,
                        lockDetails.property_details.laundary_number
                    )
                }

                private fun callWhatsapp() {
                    CommonValues.onWhatsAppIntent(
                        this@LockDetailsActivity,
                        lockDetails.property_details.support_whatsapp_number
                    )
                }

                private fun callLaundry() {
                    CommonValues.openPhone(
                        this@LockDetailsActivity,
                        lockDetails.property_details.laundary_number
                    )
                }

                private fun callGrocery() {
                    CommonValues.openPhone(
                        this@LockDetailsActivity,
                        lockDetails.property_details.grocery_number
                    )
                }

            })

    }

    override fun onScanStarted() {


    }

    override fun onScanStopped() {

    }

    override fun onF9000Found(p0: IKeyScanInfo?) {
    }

    override fun onLockFound(p0: ILockScanInfo?, p1: IMobileCredentialScanInfo?) {
        runOnUiThread {
            if (p0!!.lockName == lockDetails.lock.lock_uid) {
                scanManagerService.stopScan()
                binding.clRipple.progressDrawable =
                    ContextCompat.getDrawable(this, R.drawable.bg_grey_rounded)
                binding.ivLock.background =
                    ContextCompat.getDrawable(this, R.drawable.ic_close_lock)
                binding.tvUnlock.text = getString(keyless.data.utils.android.R.string.tap_to_unlock_door)
                binding.clRipple.isClickable = true
                Log.e("opening", "6")
                binding.clRipple.progress = 0
                binding.txtConnecting.isVisible = false
                binding.animatedDots.isVisible = false
                binding.ivLock.isVisible = true
                binding.tvUnlock.isVisible = true
                binding.ripplView.stopRippleAnimation()
                lock = p0
                info = p1
                lockDetails.lock.log(
                    action = LockLogActionType.Unlock,
                    message = "Lock Found",
                    data = JSON.toJSONString(
                        p0,
                        SerializerFeature.WriteDateUseDateFormat
                    ) + " && " + JSON.toJSONString(p1, SerializerFeature.WriteDateUseDateFormat)
                )
            }

        }
    }

    override fun onLegacyDeviceFound(p0: ILegacyScanInfo?) {
    }

    private suspend fun initAirBnk() {
        val lock = airbnk.connect(decryptedAccessKey, 0)
        listenAirBnkStatus(lock)

        lockDetails.lock.log(
            action = LockLogActionType.ScanLock,
            message = "Scan Lock Successfully!",
            data = decryptedAccessKey
        )

        findLockDone()

        val batteryLevel = airbnk.getByKey(decryptedAccessKey).batteryLevel()
        updateAirBnkLockBattery(batteryLevel)
        mViewModel.postBatteryPercentage(
            decryptedAccessKey,
            batteryLevel,
            lockDetails.lock.internal_id
        )
    }

    private suspend fun initTedee() {
        if (!network.enableNetwork()) return
        val lock =
            tedee.sync(lockDetails.lock._id, Preferences.authenticationToken.get(), Preferences.userRole.get())
        handleTedeeLockView(lock.state)
        findLockDone()

        updateAirBnkLockBattery(lock.batteryLevel)
        mViewModel.postBatteryPercentage(
            decryptedAccessKey,
            lock.batteryLevel,
            lockDetails.lock.internal_id
        )
    }

    private suspend fun openAirBnk() {
        val lock = airbnk.connect(accessKey = decryptedAccessKey)
        if (lock.status().isUnlocked) {
            lockAirBnk()
            return
        } else {
            lock.unlock()
        }

        apiLockHistory()

        lockDetails.lock.log(
            action = LockLogActionType.Unlock,
            message = "Unlock Successfully!",
            data = ""
        )
    }

    private suspend fun openTedee() {
        if (lastTedeeState.isUnlocked) {
            lockTedee()
        } else {
            unlockTedee()
        }
    }

    private suspend fun unlockTedee() {
        val location = location.getCurrentLocation()
        val lock = tedee.unlock(
            lockId = lockDetails.lock._id,
            authentication = Preferences.authenticationToken.get(),
            role = Preferences.userRole.get(),
            latitude = location.lat,
            longitude = location.long
        )

        handleTedeeLockView(lock.state)

        apiLockHistory()

        lockDetails.lock.log(
            action = LockLogActionType.Unlock,
            message = "Unlock Successfully!",
            data = ""
        )
    }

    private suspend fun lockTedee() {
        val location = location.getCurrentLocation()
        val lock = tedee.lock(
            lockId = lockDetails.lock._id,
            authentication = Preferences.authenticationToken.get(),
            role = Preferences.userRole.get(),
            latitude = location.lat,
            longitude = location.long
        )
        handleTedeeLockView(lock.state)
    }

    private suspend fun lockAirBnk() {
        airbnk.getByKey(decryptedAccessKey).lock()
    }

    private fun doorLockedView(text: String, timer: Long, enabled: Boolean = true) {
        val handler = Handler()
        val runnable = Runnable {
            run {
                runOnUiThread {
                    try {
                        binding.clRipple.progressDrawable =
                            ContextCompat.getDrawable(this, R.drawable.bg_grey_rounded)
                        binding.ivLock.background =
                            ContextCompat.getDrawable(this, R.drawable.ic_close_lock)
                        binding.clRipple.progress = 0
                        binding.tvUnlock.text = text
                        binding.ripplView.stopRippleAnimation()
                        binding.txtConnecting.isVisible = false
                        binding.animatedDots.isVisible = false
                        binding.clRipple.isClickable = enabled
                        binding.clRipple.isFocusable = enabled
                        Log.e("opening", "5")
                        lockFind = true
                        binding.ripplView.stopRippleAnimation()
//                    mBluetoothLeScan!!.stopReceiver()
                        isScan = false
                        mBleLockSdk?.disconnect()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
        handler.postAtTime(runnable, System.currentTimeMillis() + timer)
        handler.postDelayed(runnable, timer)
    }

    private fun doorUnlockedView(text: String, timer: Long) {
        val handler = Handler()
        val runnable = Runnable {
            run {
                runOnUiThread {
                    binding.ripplView.stopRippleAnimation()
                    binding.txtConnecting.isVisible = false
                    binding.animatedDots.isVisible = false
                    binding.ivLock.isVisible = true
                    binding.tvUnlock.isVisible = true
                    binding.clRipple.progress = 0
                    binding.ivLock.background = ContextCompat.getDrawable(
                        this@LockDetailsActivity,
                        R.drawable.ic_open_lock
                    )
                    binding.tvUnlock.text = text
                    ObjectAnimator.ofInt(binding.clRipple, "progress", 100)
                        .setDuration(1000)
                        .start()
                    binding.clRipple.progressDrawable =
                        ContextCompat.getDrawable(
                            this@LockDetailsActivity,
                            R.drawable.bg_green_rounded
                        )
                    binding.clRipple.isClickable = true
                    binding.clRipple.isFocusable = true
                }
            }
        }
        handler.postAtTime(runnable, System.currentTimeMillis() + timer)
        handler.postDelayed(runnable, timer)
    }

    private fun updateAirBnkLockBattery(level: Int) {
        when (level) {
            0 -> {
                binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery0)
            }

            1 -> {
                binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery1)
            }

            2 -> {
                binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery2)
            }

            3 -> {
                binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery3)
            }

            else -> {
                binding.ivBatteryCicular.setImageResource(R.drawable.iv_battery0)
            }
        }
    }

    private fun listenAirBnkStatus(lock: AirBnkLock) {
        if (airbnkListenStarted) return

        airbnkListenStarted = true

        (lock as? AndroidAirBnkLock)
            ?.listenStatus()
            ?.distinctUntilChanged()
            ?.onEach {
                updatedStatus(lock, it)
            }?.launchIn(lifecycleScope)
    }

    data class LockInfo(val lock: ILockScanInfo?, val info: IMobileCredentialScanInfo?) {
        override fun equals(other: Any?): Boolean {
            return when (other) {
                is LockInfo -> {
                    return lock?.macAddress == other.lock?.macAddress
                }

                else -> super.equals(other)
            }
        }

        override fun hashCode(): Int {
            var result = lock?.hashCode() ?: 0
            result = 31 * result + (info?.hashCode() ?: 0)
            return result
        }
    }

    private suspend fun updatedStatus(lock: AirBnkLock, initStatus: AirBnkStatus) {
        try {
            if (!lifecycleScope.isActive) return
            val status = withTimeoutOrNull(2500) { lock.status() } ?: initStatus
            airbnkLastStatus = status
            handleAirBnkLockView(status.isUnlocked)
        } catch (ex: Exception) {
            airbnkLastStatus = initStatus
            handleAirBnkLockView(initStatus.isUnlocked)
        }
    }

    private fun handleAirBnkLockView(isUnlocked: Boolean) {
        if (isUnlocked) {
            doorUnlockedView(getString(keyless.data.utils.android.R.string.tap_to_lock_door), 0)
        } else {
            doorLockedView(getString(keyless.data.utils.android.R.string.tap_to_unlock_door), 0)
        }
    }

    private fun handleTedeeLockView(state: LockState) {
        lastTedeeState = state
        when (state) {
            LockState.Uncalibrated,
            LockState.Calibrating,
            LockState.Updating -> {
                doorLockedView(getString(keyless.data.utils.android.R.string.lock_status_is, lastTedeeState.toString()), 0, enabled = false)
            }

            LockState.Locking,
            LockState.Locked -> {
                doorLockedView(getString(keyless.data.utils.android.R.string.tap_to_unlock_door), 0)
            }

            LockState.SemiLocked,
            LockState.Unknown -> {
                doorLockedView(
                    getString(
                        keyless.data.utils.android.R.string.lock_status_is_tap_unlock,
                        lastTedeeState.toString()
                    ), 0
                )
            }

            LockState.Unlocking,
            LockState.Pulling,
            LockState.Unlocked,
            LockState.Pulled -> {
                doorUnlockedView(getString(keyless.data.utils.android.R.string.tap_to_lock_door), 0)
            }
        }
    }


}