plugins {
    id("keyless.android.feature")
    id("org.jetbrains.kotlin.kapt")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":rayonicsSdk"))

    implementation(project(":data-network-android"))
    implementation(project(":data-utils-android"))
    implementation(project(":data-common"))
    implementation(project(":feature-common"))
    implementation(project(":feature-dashboard"))
    implementation(project(":presentation-authentication"))

    implementation(keyless.androidx.lifecycle.livedata)
    implementation(keyless.retrofit)

    implementation("com.google.android.gms:play-services-maps:18.1.0")
    implementation("com.google.android.gms:play-services-location:21.0.1")
    implementation("com.alibaba:fastjson:1.1.60.android")
    implementation(platform("com.google.firebase:firebase-bom:28.4.2"))
    implementation ("com.google.firebase:firebase-crashlytics-ktx")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")

    implementation(keyless.glide)
    kapt(keyless.glide.compiler)
}