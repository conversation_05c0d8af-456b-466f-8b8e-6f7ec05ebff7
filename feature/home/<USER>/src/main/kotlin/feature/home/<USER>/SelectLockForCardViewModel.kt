package feature.home.admin

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import androidx.lifecycle.viewModelScope
import com.google.gson.JsonObject
import data.network.android.AdminLocksResponse
import data.network.android.ApiUtils
import data.network.android.ModelCardRayonics
import data.network.android.models.ModelConfigureCard
import presentation.common.domain.repositories.ErrorMessageHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SelectLockForCardViewModel : ViewModel() {

    private val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress
    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    private var _getResponseAdminLocks = MutableLiveData<AdminLocksResponse>()
    val getResponseAdminLocks: LiveData<AdminLocksResponse> = _getResponseAdminLocks

    fun hitAdminLocksApi(token: String, progress: Boolean, page: Int, search: String) {
        if (!progress) {
            _progress.value = false
            _progress.value = true
        }
        viewModelScope.launch(Dispatchers.IO) {
            ApiUtils.adminLocksChinese(token, page, search, {
                (it as AdminLocksResponse)
                _progress.value = false
                _getResponseAdminLocks.value = it
            }, {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            })
        }
    }

    fun getCardSeries(token: String) = liveData {
        ApiUtils.getCardSeries(
            token,
            {
                emit(it as ModelCardRayonics)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }

    fun createCard(token: String, jsonObject: JsonObject) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.createCard(
            token,
            jsonObject,
            {
                (it as ModelConfigureCard)
                _progress.value = false
                emit(it)
            },
            {
                _progress.value = false
                _error.value = ErrorMessageHandler.handleException(it).toString()
            }
        )
    }
}