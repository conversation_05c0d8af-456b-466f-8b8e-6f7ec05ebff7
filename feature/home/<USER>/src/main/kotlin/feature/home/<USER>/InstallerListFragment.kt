package feature.home.installer

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.JsonObject
import com.khoiron.actionsheets.ActionSheet
import com.khoiron.actionsheets.callback.ActionSheetCallBack
import data.common.preferences.Preferences
import data.network.android.models.ModelAdminInstaller
import data.network.android.models.ModelData
import data.utils.android.CommonValues
import data.utils.android.hideKeyboard
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import feature.common.dialogs.ProgressDialogUtils
import feature.common.models.ModelForData
import feature.dfu.view.nearbyDfuActivity.NearByDfuDeviceActivity
import feature.settings.changelock.ChangeLockActivity
import keyless.feature.home.installer.databinding.ActivityInstallerListBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Locale

class InstallerListFragment :
    Fragment(),
    AdapterInstallerMainList.ClickToSetUp,
    AdapterMaintenanceMain.ClickToMaintain {

    private var whichScreen: Int = 0
    private lateinit var adapterMainList: AdapterInstallerMainList
    private lateinit var adapterMaintenance: AdapterMaintenanceMain
    lateinit var viewModel: InstallerListViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    var page = 1
    var pageMaintenance = 1
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private var isLastPageMaintenance: Boolean = false

    var isLoadingMoreMaintenance = false
    private val layoutManager = LinearLayoutManager(context)
    private val layoutManagerMaintenance = LinearLayoutManager(context)
    private var listTotal: ArrayList<ModelAdminInstaller.DataModelInstaller> = ArrayList()
    private var listTotalMaintenance: ArrayList<ModelData> = ArrayList()
    var maintenanceClicked = 0
    lateinit var binding: ActivityInstallerListBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = ActivityInstallerListBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initz()
        setAdapter()
        clickListeners()
        apiForData()
        apiImplementationInstallation()
//        apiImplementationMaintenance(false, 1)
        pagination()
        observerInit()
    }

    @SuppressLint("SetTextI18n")
    private fun apiForData() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        viewModel.getCheckUser(sharePrefs.token, jsonObject, false).observe(requireActivity()) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (it.force_password_require) {
                if (!it.due_date.isNullOrEmpty()) {
                    var dateApi = CommonValues.formatOnlyDate(
                        it.due_date.split("T")[0]
                    )

                    val dateServer = CommonValues.formatOnlyDate(
                        CommonValues.serverDateTime.split("T")[0]
                    )

                    try {
                        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                        val date1 = formatter.parse(dateServer)
                        val str2 = dateApi
                        val date2 = formatter.parse(str2)
                        if (date1 < date2) {
                            binding.forcePassUpdateView.isVisible = false
                        } else {
                            clickForceUpdate()
                        }
                    } catch (e1: ParseException) {
                        e1.printStackTrace()
                    }
                    binding.forcePassUpdateView.isVisible = true
                    binding.forcePassUpdateView.text =
                        getString(keyless.data.utils.android.R.string.tap_to_update_your_password_before) + dateApi +
                        getString(keyless.data.utils.android.R.string.to_avoid_being_locked_out_of_your_account)
                }
            } else {
                binding.forcePassUpdateView.isVisible = false
            }
        }
    }

    private fun clickForceUpdate() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        viewModel.getCheckUser(sharePrefs.token, jsonObject, true).observe(requireActivity()) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (it.force_password_require) {
                val intent = Intent(requireContext(), AuthenticationActivity::class.java)
                intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.UPDATE_PASSWORD)
                startActivityForResult(intent, 12)
            } else {
                requireActivity().toast(
                    getString(keyless.data.utils.android.R.string.your_password_has_been_already_updated)
                )
                binding.forcePassUpdateView.isVisible = false
            }
        }
    }

    private fun apiImplementationMaintenance(progressed: Boolean, paging: Int, toString: String) {
        viewModel.getMaintenanceList(
            sharePrefs.token,
            paging,
            binding.svInstaller.text.toString(),
            progressed,
            sharePrefs.uuid
        )
            .observe(requireActivity()) {
                if (paging == 1) {
                    listTotalMaintenance.clear()
                }
                listTotalMaintenance.addAll(it.data)
                if (listTotalMaintenance.size > 0) {
                    if (maintenanceClicked == 1) {
                        binding.layNoData.isVisible = false
                        binding.rvMaintenance.isVisible = true
                        binding.svInstaller.isVisible = true
                    }
                    if (listTotalMaintenance.size == it.total) {
                        isLastPageMaintenance = true
                        isLoadingMoreMaintenance = true
                    }
                    adapterMaintenance.updateOptions(listTotalMaintenance)
                } else {
                    binding.layNoData.isVisible = true
                    binding.rvMaintenance.isVisible = false
                    binding.svInstaller.isVisible = !binding.svInstaller.text.toString().isEmpty()
                }
            }
    }

    private fun apiImplementationInstallation() {
        viewModel.getInstallerList(
            sharePrefs.token,
            page,
            binding.svInstaller.text.toString(),
            false,
            ""
        )
    }

    private fun clickListeners() {
        binding.forcePassUpdateView.setOnClickListener {
            clickForceUpdate()
        }

//        container.setOnRefreshListener {
//            container.isRefreshing = true
//            viewModel.getInstallerList(sharePrefs.token, 1, "", false, "")
//        }

        binding.svInstaller.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    return@OnEditorActionListener true
                }
                false
            }
        )

        binding.svInstaller.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.stopInstaller.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopInstaller.setOnClickListener {
            hideKeyboard()
            binding.svInstaller.setText("")
            if (whichScreen == 0) {
                viewModel.getInstallerList(
                    sharePrefs.token,
                    page,
                    binding.svInstaller.text.toString(),
                    false,
                    ""
                )
            } else {
                apiImplementationMaintenance(false, 1, binding.svInstaller.text.toString())
            }
        }
    }

    private fun performSearch() {
        hideKeyboard()
        if (whichScreen == 0) {
            viewModel.getInstallerList(
                sharePrefs.token,
                page,
                binding.svInstaller.text.toString(),
                false,
                ""
            )
        } else {
            apiImplementationMaintenance(false, 1, binding.svInstaller.text.toString())
        }
    }

    private fun pagination() {
        binding.rvInstaller.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore) {
                    page++
                    viewModel.getInstallerList(
                        sharePrefs.token,
                        page,
                        binding.svInstaller.text.toString(),
                        true,
                        ""
                    )
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })

        binding.rvMaintenance.addOnScrollListener(object :
                PaginationScrollListener(layoutManagerMaintenance) {
                override fun isLastPage(): Boolean {
                    return isLastPageMaintenance
                }

                override fun loadMoreItems() {
                    if (!isLoadingMoreMaintenance) {
                        pageMaintenance++
                        apiImplementationMaintenance(
                            true,
                            pageMaintenance,
                            binding.svInstaller.text.toString()
                        )
                        binding.progressPagination.visibility = View.VISIBLE
                    }
                }

                override fun isLoading(): Boolean {
                    return isLoadingMoreMaintenance
                }
            })
    }

    private fun observerInit() {
        viewModel.getResponseInstaller.observe(requireActivity()) {
            if (page == 1) {
                listTotal.clear()
            }
            listTotal.addAll(it.data)
            if (listTotal.size > 0) {
                binding.layNoData.isVisible = false
                binding.rvInstaller.isVisible = true
                binding.svInstaller.isVisible = true
                if (listTotal.size == it.total) {
                    isLastPage = true
                    isLoadingMore = true
                }
                adapterMainList.updateList(listTotal)
            } else {
                binding.layNoData.isVisible = true
                binding.rvInstaller.isVisible = false
                binding.svInstaller.isVisible = !binding.svInstaller.text.toString().isEmpty()
            }

//            container.isRefreshing = false
        }

        viewModel.progress.observe(requireActivity()) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireActivity(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
//            container.isRefreshing = false
        }

        viewModel.error.observe(requireActivity()) {
            requireActivity().toast(it)
//            container.isRefreshing = false
        }
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[InstallerListViewModel::class.java]
    }

    override fun clickToUpdateStatus(model: ModelData, position: Int) {
        val data: java.util.ArrayList<String> = java.util.ArrayList()
        data.add(getString(keyless.feature.common.R.string.update_battery))
        data.add(getString(keyless.feature.common.R.string.firmware_update))

        ActionSheet(requireActivity(), data)
            .setTitle(getString(keyless.feature.common.R.string.options))
            .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
            .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .create(object : ActionSheetCallBack {
                override fun data(data: String, position: Int) {
                    when (position) {
                        0 -> {
                            clickChangeBattery(model)
                        }

                        1 -> {
                            startActivity(
                                Intent(
                                    requireActivity(),
                                    NearByDfuDeviceActivity::class.java
                                ).putExtra("lockInfo", model.lock_info).putExtra(
                                    "property_details",
                                    model.maplockproperty
                                ).putExtra("installer", "1")
                            )
                        }
                    }
                }
            })
    }

    private fun clickChangeBattery(model: ModelData) {
        if (model.lock_info[0].provider == CommonValues.iseo) {
            val jsonObject = JsonObject()
            jsonObject.addProperty("lock_id", model.lock_info[0].unique_key)
            jsonObject.addProperty("uid", sharePrefs.uuid)
            jsonObject.addProperty("actionType", 1)
            viewModel.assign_lock_iseo(
                sharePrefs.token,
                model.company[0]._id,
                jsonObject
            ).observe(requireActivity()) {
                val modelData = ModelForData()
                modelData.unique_key = model.lock_info[0].unique_key
                modelData.access_key = model.lock_info[0].access_key
                modelData.encrypted_key = model.lock_info[0].encrypted_key
                modelData.provider = model.lock_info[0].provider
                modelData.lock_id = model.lock_info[0]._id
                modelData.internal_id = model.lock_info[0].internal_id
                modelData.lock_uid = model.lock_info[0].lock_uid
                modelData.owner_id = model.company[0]._id
                startActivityForResult(
                    Intent(
                        requireContext(),
                        ChangeLockActivity::class.java
                    ).putExtra("selectedItem", modelData),
                    51
                )
            }
        } else {
            val modelData = ModelForData()
            modelData.unique_key = model.lock_info[0].unique_key
            modelData.access_key = model.lock_info[0].access_key
            modelData.encrypted_key = model.lock_info[0].encrypted_key
            modelData.lock_id = model.lock_info[0]._id
            modelData.provider = model.lock_info[0].provider
            modelData.internal_id = model.lock_info[0].internal_id
            modelData.lock_uid = model.lock_info[0].lock_uid
            modelData.owner_id = model.company[0]._id
            startActivityForResult(
                Intent(
                    requireContext(),
                    ChangeLockActivity::class.java
                ).putExtra("selectedItem", modelData),
                51
            )
        }
    }

    private fun setAdapter() {
        binding.rvInstaller.layoutManager = layoutManager
        adapterMainList = AdapterInstallerMainList(this)
        binding.rvInstaller.adapter = adapterMainList

        binding.rvMaintenance.layoutManager = LinearLayoutManager(requireContext())
        adapterMaintenance = AdapterMaintenanceMain(this)
        binding.rvMaintenance.adapter = adapterMaintenance
    }

    override fun clickToNextScreen(model: ModelAdminInstaller.DataModelInstaller) {
        startActivityForResult(
            Intent(
                requireActivity(),
                InstallerLockActivity::class.java
            ).putExtra("model", model),
            30
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 30 && resultCode == 150) {
            viewModel.getInstallerList(sharePrefs.token, 1, "", false, "")
        }
        if (requestCode == 51 && resultCode == 52) {
            apiImplementationMaintenance(false, 1, binding.svInstaller.text.toString())
        }
        if (requestCode == 12) {
            lifecycleScope.launch {
                delay(1000)
                apiForData()
            }
        }
    }

    fun update(whichScreenInstaller: Int) {
        maintenanceClicked = 0
        binding.rvInstaller.isVisible = true
        binding.rvMaintenance.isVisible = false
        binding.svInstaller.setText("")
        viewModel.getInstallerList(sharePrefs.token, 1, "", false, "")
        whichScreen = whichScreenInstaller
    }

    fun apiMaintenance(whichScreenInstaller: Int) {
        maintenanceClicked = 1
        binding.rvInstaller.isVisible = false
        binding.rvMaintenance.isVisible = true
        binding.svInstaller.setText("")
        apiImplementationMaintenance(false, 1, binding.svInstaller.text.toString())
        whichScreen = whichScreenInstaller
    }
}