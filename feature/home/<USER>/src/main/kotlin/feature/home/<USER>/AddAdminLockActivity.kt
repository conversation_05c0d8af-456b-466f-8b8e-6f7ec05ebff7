package feature.home.admin

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.view.Gravity
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.PopupMenu
import android.widget.Spinner
import android.widget.TextView
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADD_ADAPTER
import data.utils.android.common.BleLockScanData
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import feature.qrcode.scan.QRCodeScanActivity
import keyless.feature.home.admin.R
import keyless.feature.home.admin.databinding.ActivityAddAdminLockBinding
import rayo.logicsdk.bean.DSTClass
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.bean.LockTimeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import java.sql.Timestamp
import java.util.Date
import java.util.Locale
import java.util.Random

class AddAdminLockActivity : AppCompatActivity() {

    private var popUpShow: Boolean = false
    private var internalId: String = ""
    private lateinit var bluetoothDeviceNew: BleLockScanData
    private var time2: String = ""
    private var time1: String = ""
    private var accessKeyForRegister: String = ""
    lateinit var mViewModel: AddLockAdminViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private val ALLOWED_CHARACTERS = "0123456789qwertyuiopasdfghjklzxcvbnm"
    private lateinit var popupSize: PopupMenu
    private lateinit var popupColor: PopupMenu
    private var mBleName: MutableList<String?> = ArrayList<String?>()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    var mBleLockSdk: BleLockSdk? = null
    var message = ""
    private var running: Boolean = false

//    lateinit var start: CountDownTimer
    private var backed: Boolean = false
    private var REQUEST_CHECK_SETTINGS = 3
    lateinit var dialogLock: Dialog
    private lateinit var binding: ActivityAddAdminLockBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddAdminLockBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        askPermission()
        clickListeners()
        observerInit()
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        }
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
//            try {
            when (msg.what) {
                ADD_ADAPTER -> {
                    if (msg.obj != null) {
//                        try {
                        addAdapterItemRange(msg.obj as BleLockScanData)
//                        } catch (e: Exception) {
//                            e.printStackTrace()
//                        }
                    }
//                        if (!running) {
//                            running = true
//                            start = object : CountDownTimer(15000, 10) {
//                                override fun onTick(millisUntilFinished: Long) {
//                                    if (msg.obj != null) {
// //                                        try {
//                                        addAdapterItemRange(msg.obj as BleLockScanData)
// //                                        }catch (e: Exception){
// //                                            e.printStackTrace()
// //                                        }
//                                    }
//                                }
//
//                                override fun onFinish() {
//                                    if (!backed) {
//                                        runOnUiThread {
//                                            if (!isFinishing) {
//                                                try {
//                                                    progressLay.isVisible = false
//                                                    defaultDialog(
//                                                        this@AddAdminLockActivity,
//                                                        getString(keyless.data.utils.android.R.string.scanned_lock_could_not_found),
//                                                        object : OnActionOK {
//                                                            override fun onClickData() {
//                                                                mBluetoothLeScan!!.stopReceiver()
//                                                                mBleLockSdk?.disconnect()
//                                                            }
//                                                        })
//                                                } catch (e: WindowManager.BadTokenException) {
//                                                    Log.e("WindowManagerBad ", e.toString())
//                                                }
//                                            }
//                                        }
//
//                                    }
//                                }
//
//                            }.start()
//                        }
                }

                else -> {
                }
            }
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }

        bluetoothDeviceNew = bluetoothDevice
        mBluetoothDevice = bluetoothDevice as BleLockScanData
        if (mBluetoothDevice!!.bleName == binding.txtLockModel.text.toString().trim()) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
//            start.cancel()
            registerLockk()
        } else {
            isScan = true
        }
    }

    private fun checkName(bleName: String): Boolean {
        if (null == bleName) return false
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }
        mViewModel.error.observe(this) {
            toast(it)
        }
    }

    @SuppressLint("MissingPermission")
    private fun clickListeners() {
        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        if (binding.progressLay?.isVisible == true) {
                            dialogLock.dismiss()
                        } else {
//                            mBleScanCallback.finishScan()
                            mBleLockSdk?.disconnect()
                            finish()
                        }
                        backed = true
                        running = false
                    }
                })
            )
        }

        binding.viewScanning.setOnClickListener {
            val intent = Intent(this, QRCodeScanActivity::class.java)
            intent.putExtra(QRCodeScanActivity.QR_CODE_SCAN_RESULT_CODE, QR_CODE_SCAN_LOCK_MODEL_CODE)
            startActivityForResult(intent, 25)
        }

        binding.viewInternalIdScanning.setOnClickListener {
            val intent = Intent(this, QRCodeScanActivity::class.java)
            intent.putExtra(QRCodeScanActivity.QR_CODE_SCAN_RESULT_CODE, QR_CODE_SCAN_INTERNAL_ID_CODE)
            startActivityForResult(intent, 25)
        }

        binding.selectColor.setOnClickListener {
            popupColor.show()
        }

        binding.selectSize.setOnClickListener {
            popupSize.show()
        }

        binding.selectTimeZone.setOnClickListener {
            showDialog()
        }

        binding.btnSave.setOnClickListener {
            if (CommonValues.isBluetoothEnabled()) {
                commonClick()
            } else {
                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
            }
        }

        binding.viewBack.setOnClickListener {
            mBleLockSdk?.disconnect()
            backed = true
            running = false
            finish()
        }
    }

    private fun commonClick() {
        if (binding.txtLockModel.text.toString().isEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_lock_model_to_continue))
            // TODO restore internal_id
//        } else if (binding.txtLockInternalId.text.toString().isEmpty()) {
//            toast(getString(keyless.data.utils.android.R.string.please_enter_internal_id))
        } else {
            val manager = getSystemService(Context.LOCATION_SERVICE) as LocationManager
            val statusOfGPS = manager.isProviderEnabled(LocationManager.GPS_PROVIDER)
//                val bluetooth = CommonValues.isBluetoothEnabled()

            if (statusOfGPS) {
                showDialogForScanning()
//                progressLay.isVisible = true
                initDataRange()
            } else {
                toast(getString(keyless.data.utils.android.R.string.turn_on_gps))
                startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
            }
        }
    }

    private fun showDialogForScanning() {
        dialogLock = Dialog(this)
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        var animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }
        cancelBtn.setOnClickListener {
            isScan = false
            mBleLockSdk?.disconnect()
            dialogLock.dismiss()
//            finish()
        }
        dialogLock.show()
    }

    private fun initDataRange() {
        mBleName = ArrayList<String?>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        // 初始化蓝牙搜索服务
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()

//        lifecycleScope.launch {
//            while (isScan){
// //                mBluetoothLeScan!!.stopReceiver()
//                delay(5000)
// //                isScan = false
// //                mBluetoothDeviceHashMap = HashMap()
//                mBluetoothLeScan!!.startReceiver()
// //                isScan = true
//            }
//        }

        Handler(Looper.getMainLooper()).postDelayed({
            mBluetoothLeScan!!.stopReceiver()

            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()

            isScan = true
        }, 1000)
    }

    private fun registerLockk() {
        mBleLockSdk = BleLockSdk()
        var mBluetoothManager: BluetoothManager? = null
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk!!.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            var lockCodeClass = LockCodeClass()
            mBleLockSdk!!.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                bluetoothDeviceNew.bleMac,
                bluetoothDeviceNew.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        if (binding.progressLay?.isVisible == true) {
            dialogLock.dismiss()
//            progressLay.isVisible = false
        } else {
            mBleLockSdk?.disconnect()
            finish()
        }
        backed = true
        running = false
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
        }

        override fun connect(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
            } else {
            }
        }

        override fun authentication(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                val mLockBasicInfo = resultBean.obj as (LockBasicInfo)
                var batteryCount = 1000
                if (mLockBasicInfo.battery in 0..25) {
                    batteryCount = 0
                } else if (mLockBasicInfo.battery in 26..50) {
                    batteryCount = 1
                } else if (mLockBasicInfo.battery in 51..75) {
                    batteryCount = 2
                } else if (mLockBasicInfo.battery > 75) {
                    batteryCount = 3
                }
                if (validation()) {
                    val requestBody = JsonObject()
                    requestBody.addProperty("unique_key", binding.txtLockModel.text.toString())
                    // TODO restore internal_id
                    // requestBody.addProperty("internal_id", binding.txtLockInternalId.text.toString())
                    requestBody.addProperty("access_key", getRandomAccessKeyString(4))
                    requestBody.addProperty("provider", binding.etBrand.text.toString())
                    requestBody.addProperty("colour", binding.selectColor.text.toString())
                    requestBody.addProperty("size", binding.selectSize.text.toString())
                    requestBody.addProperty("time_zone", binding.selectTimeZone.text.toString())
                    requestBody.addProperty("battery_level", batteryCount)
//                    progressLay.isVisible = true
                    mViewModel.hitAdminEditLocksChinese(
                        sharePrefs.token,
                        requestBody
                    ).observe(this@AddAdminLockActivity) {
                        if (it.isSuccess) {
                            internalId = it.data
                            val lockCodeClass = LockCodeClass()
                            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
                            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
                            lockCodeClass.sysCode = accessKeyForRegister.toByteArray()
                            mBleLockSdk?.registerLock(lockCodeClass)
                        }
//                        else {
//                            defaultDialog(
//                                this@AddAdminLockActivity,
//                                getString(keyless.data.utils.android.R.string.lock_is_not_in_range),
//                                object : OnActionOK {
//                                    override fun onClickData() {
//                                        progressLay.isVisible = false
//                                        isScan = false
//                                        mBluetoothLeScan!!.stopReceiver()
//                                        mBleLockSdk?.disconnect()
//                                        setResult(Activity.RESULT_OK)
//                                        finish()
//                                    }
//                                })
//                        }
                    }
                }
            } else {
//                var msg = ""
//                msg = if (JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    ).toLowerCase().contains("timeout")
//                ) {
//                    getString(keyless.data.utils.android.R.string.the_lock_could_not_be_connected)
//                } else {
//                    JSON.toJSONString(
//                        resultBean,
//                        SerializerFeature.WriteDateUseDateFormat
//                    )
//                }
//                defaultDialog(
//                    this@AddAdminLockActivity,
//                    msg,
//                    object : OnActionOK {
//                        override fun onClickData() {
//                            mBleLockSdk?.disconnect()
//                            progressLay.isVisible = false
//                            finish()
//                        }
//                    })
            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                val lockTimeClass = LockTimeClass()
                val timestamp = Timestamp(System.currentTimeMillis())
                println(timestamp)
                lockTimeClass.lockTime = timestamp
                var dstClass = DSTClass()
//                dstClass.beginTime = Calendar.getInstance().time
//                dstClass.endTime = Calendar.getInstance().time
                lockTimeClass.dstClass = dstClass
                mBleLockSdk?.setLockTime(lockTimeClass)
            }
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                popUpShow = true
                dialogLock.dismiss()
                defaultDialog(
                    this@AddAdminLockActivity,
                    internalId + " " + getString(keyless.data.utils.android.R.string.id_has_been_created),
                    object : OnActionOK {
                        override fun onClickData() {
                            mBleLockSdk?.disconnect()
                            binding.progressLay.isVisible = false
                            setResult(Activity.RESULT_OK)
                            finish()
                        }
                    }
                )
//                start.cancel()
            }
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
            mBleLockSdk?.getLockInfo()
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
            } else {
            }
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
        }

        override fun onReport(resultBean: ResultBean<*>?) {
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
        }

        override fun disPlay(resultBean: ResultBean<*>) {
        }

        override fun setTempCard(p0: ResultBean<*>?) {
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(keyless.data.utils.android.R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = if (ActivityCompat.checkSelfPermission(
                    this@AddAdminLockActivity,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this@AddAdminLockActivity,
                    arrayOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.BLUETOOTH_SCAN,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ),
                    202
                )
                return
            } else {
                BleLockScanData(
                    bluetoothDevice.name ?: "",
                    bluetoothDevice.address ?: "",
                    scanRecord
                )
            }
            mBleHandler!!.obtainMessage(ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {
        }
    }

    private fun showDialog() {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(true)
        dialog.setContentView(R.layout.time_zone_lay)
        var timeZone = dialog.findViewById<Spinner>(R.id.timeZone)
        var timeZone1 = dialog.findViewById<Spinner>(R.id.timeZone1)
        var okbtn = dialog.findViewById<TextView>(R.id.ok)
        var cancelBtn = dialog.findViewById<TextView>(R.id.cancel)
        setRecyclerView(timeZone, timeZone1)

        okbtn.setOnClickListener {
            if (time1.isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.select_timezone_to_continue))
            } else if (time2.isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.select_timezone_to_continue))
            } else {
                binding.selectTimeZone.setText("$time1:$time2")
                dialog.dismiss()
            }
        }
        cancelBtn.setOnClickListener {
            dialog.dismiss()
        }

        val lp = WindowManager.LayoutParams()
        lp.copyFrom(dialog.window?.attributes)
        lp.width = WindowManager.LayoutParams.MATCH_PARENT
        dialog.window?.attributes = lp

        dialog.show()
    }

    private fun setRecyclerView(timeZone: Spinner, timeZone1: Spinner) {
        val array1 = arrayOf(
            "UTC+04",
            "UTC-12",
            "UTC-11",
            "UTC-10",
            "UTC-09",
            "UTC-08",
            "UTC-07",
            "UTC-06",
            "UTC-05",
            "UTC-04",
            "UTC-03",
            "UTC-02",
            "UTC-01",
            "UTC+0",
            "UTC+01",
            "UTC+02",
            "UTC+03",
            "UTC+05",
            "UTC+06",
            "UTC+07",
            "UTC+08",
            "UTC+09",
            "UTC+10",
            "UTC+11",
            "UTC+12"
        )
        val array2 = arrayOf("0", "15", "30", "45")
        val adapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            array1
        )
        timeZone.adapter = adapter

        val adapter1 = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            array2
        )
        timeZone1.adapter = adapter1

        timeZone.onItemSelectedListener = object :
            AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>,
                view: View,
                position: Int,
                id: Long
            ) {
                time1 = array1[position]
            }

            override fun onNothingSelected(parent: AdapterView<*>) {
                // write code to perform some action
            }
        }

        timeZone1.onItemSelectedListener = object :
            AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>,
                view: View,
                position: Int,
                id: Long
            ) {
                time2 = array2[position]
            }

            override fun onNothingSelected(parent: AdapterView<*>) {
                // write code to perform some action
            }
        }
    }

    private fun getRandomAccessKeyString(sizeOfRandomString: Int): String {
        val random = Random()
        val sb = StringBuilder(sizeOfRandomString)
        for (i in 0 until sizeOfRandomString) sb.append(
            ALLOWED_CHARACTERS[
                random.nextInt(
                    ALLOWED_CHARACTERS.length
                )
            ]
        )
        accessKeyForRegister = sb.toString()
        return sb.toString()
    }

    private fun validation(): Boolean =
        if (binding.etBrand.text.toString().isNullOrEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_brand))
            false
        } else if (binding.txtLockModel.text.toString().isNullOrEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_lock_model))
            false
            // TODO restore internal_id
//        } else if (binding.txtLockInternalId.text.toString().isNullOrEmpty()) {
//            toast(getString(keyless.data.utils.android.R.string.please_enter_internal_id))
//            false
        } else if (binding.selectTimeZone.text.toString().isNullOrEmpty()) {
            toast(getString(keyless.data.utils.android.R.string.please_enter_timezone))
            false
        } else {
            true
        }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[AddLockAdminViewModel::class.java]
        mViewModel.getSizeColorData(sharePrefs.token).observe(this) {
            getSizeList(it.lock_attributes.Keyless.size)
            getColorList(it.lock_attributes.Keyless.colour)
        }
    }

    private fun getColorList(colourList: ArrayList<String>) {
        popupColor = PopupMenu(this, binding.selectColor, keyless.feature.common.R.style.PopupMenu)
        popupColor.gravity = Gravity.START
        for (i in 0 until (colourList.size)) {
            popupColor.menu.add(
                colourList[i].substring(0, 1).uppercase() + colourList[i].substring(1)
                    .lowercase(Locale.getDefault())
            )
        }
        popupColor.setOnMenuItemClickListener { item ->
            binding.selectColor.setText(
                item.title.toString().substring(0, 1).uppercase() + item.title.toString()
                                .substring(1).lowercase(Locale.getDefault())
            )
            true
        }
        binding.selectColor.setText(
            colourList[0].substring(0, 1).uppercase() + colourList[0]
                        .substring(1).lowercase(Locale.getDefault())
        )
    }

    private fun getSizeList(sizeList: ArrayList<String>) {
        popupSize = PopupMenu(this, binding.selectSize, keyless.feature.common.R.style.PopupMenu)
        popupSize.gravity = Gravity.START
        for (i in 0 until (sizeList.size)) {
            popupSize.menu.add(sizeList[i])
        }
        popupSize.setOnMenuItemClickListener { item ->
            binding.selectSize.setText(item.title)
            true
        }
        binding.selectSize.setText(sizeList[0])
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == QR_CODE_SCAN_LOCK_MODEL_CODE) {
            val dataMain = data?.getStringExtra(QRCodeScanActivity.QR_CODE_SCAN_RESULT_DATA)
            binding.txtLockModel.setText(dataMain.toString())
        }

        if (resultCode == QR_CODE_SCAN_INTERNAL_ID_CODE) {
            val dataMain = data?.getStringExtra(QRCodeScanActivity.QR_CODE_SCAN_RESULT_DATA)
            binding.txtLockInternalId.setText(dataMain.toString())
        }

        if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT) {
            if (!CommonValues.isBluetoothEnabled()) {
                val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (!mBluetoothAdapter.isEnabled) {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                } else {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                }
            }
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    companion object {
        private const val QR_CODE_SCAN_LOCK_MODEL_CODE = 30
        private const val QR_CODE_SCAN_INTERNAL_ID_CODE = 31
    }
}