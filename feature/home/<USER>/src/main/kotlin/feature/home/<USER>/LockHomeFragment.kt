package feature.home.locks

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Context.BLUETOOTH_SERVICE
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.Filter
import android.widget.Filterable
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.JsonObject
import com.khoiron.actionsheets.ActionSheet
import com.khoiron.actionsheets.callback.ActionSheetCallBack
import data.common.preferences.Preferences
import data.common.preferences.Roles.CUSTOMER_SERVICES
import data.common.preferences.Roles.SYSTEM_MANAGER
import data.common.preferences.Roles.VIEWER_ACCESS
import data.network.android.LockModelAdmin
import data.network.android.LocksListResponse
import data.utils.android.CommonValues
import data.utils.android.CommonValues.Companion.ADMIN
import data.utils.android.CommonValues.Companion.GUEST
import data.utils.android.common.BleLockScanData
import data.utils.android.hideKeyboard
import data.utils.android.interfaces.ClickFragments
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.OnActionYesNo
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import feature.common.dialogs.dialogYesNo
import feature.dashboard.unlock.LockDetailsActivity
import keyless.feature.home.locks.R
import keyless.feature.home.locks.databinding.FragmentLockHomeBinding
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class LockHomeFragment :
    Fragment(),
    LockAdminAdapter.ClickToDelete,
    LockAdapter.ClickToEnter,
    Filterable,
    ServicesAdapter.ClickForApi {

    private var listTotalAdminLocks: ArrayList<LockModelAdmin> = ArrayList()
    private var listTotalSearchAdminLocks: ArrayList<LockModelAdmin> = ArrayList()
    private var page = 1
    private val layoutManager = LinearLayoutManager(context)
    private var uniqueKeyDelete: String = ""
    private var accessKeyDelete: String = ""
    private var idToDelete: String = ""
    private var adapterLock: LockAdapter? = null
    private lateinit var adapterAdminLock: LockAdminAdapter
    lateinit var viewModel: HomeLocksViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            requireContext()
        )
    }
    private var mBleName: MutableList<String?> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    var mBleLockSdk: BleLockSdk? = null
    private lateinit var bluetoothDeviceNew: BleLockScanData
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    lateinit var updateValue: ClickFragments
    var arrayLocks: ArrayList<LocksListResponse.LocksModel> = ArrayList()
    private var REQUEST_CHECK_SETTINGS = 3
    lateinit var dialogLock: Dialog
    private lateinit var binding: FragmentLockHomeBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentLockHomeBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        updateValue = context as ClickFragments
    }

    override fun onAttach(activity: Activity) {
        super.onAttach(activity)
        updateValue = activity as ClickFragments
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel = ViewModelProvider(this)[HomeLocksViewModel::class.java]
        initz()
        apiForData()
        clickEvents()
        observerInit()
    }

    @SuppressLint("SetTextI18n")
    private fun apiForData() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        viewModel.getCheckUser(sharePrefs.token, jsonObject, false).observe(requireActivity()) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (it.force_password_require) {
                if (!it.due_date.isNullOrEmpty()) {
                    val dateApi = CommonValues.formatOnlyDate(
                        it.due_date.split("T")[0]
                    )

                    val dateServer = CommonValues.formatOnlyDate(
                        CommonValues.serverDateTime.split("T")[0]
                    )

                    try {
                        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("en"))
                        val date1 = formatter.parse(dateServer)
                        val date2 = formatter.parse(dateApi)
                        if (date1 != null) {
                            if (date1 < date2) {
                                binding.forcePassUpdateView.isVisible = false
                            } else {
                                clickForceUpdate()
                            }
                        }
                    } catch (e1: ParseException) {
                        e1.printStackTrace()
                    }

                    runCatching {
                        binding.forcePassUpdateView.text =
                            getString(keyless.data.utils.android.R.string.tap_to_update_your_password_before) + dateApi +
                                    getString(keyless.data.utils.android.R.string.to_avoid_being_locked_out_of_your_account)
                    }
                }

                when (Preferences.userRole.get()) {
                    ADMIN -> {
                        binding.forcePassUpdateView.isVisible = true
                    }

                    GUEST -> {
                        binding.forcePassUpdateView.isVisible = false
                    }

                    else -> {
                        binding.forcePassUpdateView.isVisible = true
                    }
                }
            } else {
                binding.forcePassUpdateView.isVisible = false
            }
        }
    }

    private fun observerInit() {
        viewModel.getResponseAdminLocks.observe(viewLifecycleOwner) {
            if (page == 1) {
                listTotalAdminLocks.clear()
                listTotalSearchAdminLocks.clear()
            }
            if (binding.svLockAdmin.text.toString().isEmpty()) {
                listTotalAdminLocks.addAll(it.locks)
                if (listTotalAdminLocks.size > 0) {
                    binding.layNoDataFullPage.isVisible = false
                    binding.rvAdminLocks.isVisible = true
                    binding.svLockAdmin.isVisible = true
                    adapterAdminLock.updateList(listTotalAdminLocks)
                } else {
                    binding.rvAdminLocks.isVisible = false
                    binding.svLockAdmin.isVisible = false
                    binding.stopSearchAdmin.isVisible = false
                    binding.layNoDataFullPage.isVisible = true
                    binding.btnAddLock.isVisible =
                        !(
                                Preferences.role.get() == VIEWER_ACCESS ||
                                        Preferences.role.get() == CUSTOMER_SERVICES ||
                                        Preferences.role.get() == SYSTEM_MANAGER &&
                                        Preferences.userRole.get() == ADMIN
                                )
                }

                if (listTotalAdminLocks.size == it.total_locks) {
                    isLastPage = true
                    isLoadingMore = true
                }
            } else {
                listTotalSearchAdminLocks.addAll(it.locks)
                if (listTotalSearchAdminLocks.size > 0) {
                    binding.layNoDataFullPage.isVisible = false
                    binding.rvAdminLocks.isVisible = true
                    binding.svLockAdmin.isVisible = true
                    adapterAdminLock.updateList(listTotalSearchAdminLocks)
                } else {
                    binding.rvAdminLocks.isVisible = false
                    binding.svLockAdmin.isVisible = true
                    binding.stopSearchAdmin.isVisible = true
                    binding.layNoDataFullPage.isVisible = true
                    binding.btnAddLock.isVisible =
                        Preferences.role.get() != CUSTOMER_SERVICES &&
                                Preferences.role.get() != VIEWER_ACCESS &&
                                Preferences.role.get() != SYSTEM_MANAGER &&
                                Preferences.userRole.get() != ADMIN
                }
                if (listTotalSearchAdminLocks.size == it.total_locks) {
                    isLastPage = true
                    isLoadingMore = true
                }
            }
            binding.progressBar.isVisible = false
            binding.progressPagination.isVisible = false
        }

        viewModel.progress.observe(viewLifecycleOwner) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(requireContext(), true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        viewModel.error.observe(viewLifecycleOwner) {
            if (it.lowercase(Locale.getDefault()).contains("authentication failed")) {
                lifecycleScope.launch(CoroutineExceptionHandler { coroutineContext, throwable -> }) {
                    delay(2000)
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.your_session_expired),
                        object : OnActionOK {
                            override fun onClickData() {
                                updateValue.clicksIntents(1)
                            }
                        }
                    )
                }
            } else {
                requireContext().toast(it)
            }
            binding.progressBar.isVisible = false
        }

        viewModel.deleteLock.observe(viewLifecycleOwner) {
            dialogLock.dismiss()
            val msg: String = if (it.isSuccess) {
                getString(keyless.data.utils.android.R.string.lock_deleted_successfully)
            } else {
                it.message
            }
            defaultDialog(
                requireActivity(),
                msg,
                object : OnActionOK {
                    override fun onClickData() {
                        mBleLockSdk?.disconnect()
                        isScan = false
                        apiImplementation(true, page, binding.svLockAdmin.text.toString())
                    }
                }
            )

            binding.progressBar.isVisible = false
        }
    }

    private fun apiImplementation(progress: Boolean, page: Int, search: String) {
        viewModel.hitAdminLocksApi(sharePrefs.token, progress, page, search)
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[HomeLocksViewModel::class.java]
        adapterAdminLock = LockAdminAdapter(this)

        when (Preferences.userRole.get()) {
            ADMIN -> {
                binding.rvAdminLocks.isVisible = true
                binding.svLockAdmin.isVisible = true
                binding.stopSearchAdmin.isVisible = false
                binding.svLock.isVisible = false
                binding.stopSearch.isVisible = false
                setAdapter()
                apiImplementation(false, page, binding.svLockAdmin.text.toString())
            }

            GUEST -> {

            }

            else -> {
                binding.rvAdminLocks.isVisible = false
                binding.svLockAdmin.isVisible = false
                binding.stopSearchAdmin.isVisible = false
                if (sharePrefs.getLockData().size > 0) {
                    binding.rvLock.isVisible = true
                    binding.layNoDataFullPage.isVisible = false
                    binding.svLock.isVisible = true
                } else {
                    binding.rvLock.isVisible = false
                    binding.layNoDataFullPage.isVisible = true
                    binding.svLock.isVisible = false
                    binding.btnAddLock.isVisible =
                        Preferences.role.get() != CUSTOMER_SERVICES &&
                                Preferences.role.get() != VIEWER_ACCESS &&
                                Preferences.role.get() != SYSTEM_MANAGER &&
                                Preferences.userRole.get() != ADMIN
                }
                setUpRecyclerView()
            }
        }
    }

    private fun pagination() {
        binding.rvAdminLocks.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore) {
                    page++
                    apiImplementation(true, page, binding.svLockAdmin.text.toString())
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })
    }

    private fun setAdapter() {
        binding.rvAdminLocks.layoutManager = layoutManager
        binding.rvAdminLocks.adapter = adapterAdminLock
        pagination()
    }

    private fun setUpRecyclerView() {
        binding.rvLock.layoutManager = GridLayoutManager(context, 2)
        adapterLock = LockAdapter(this@LockHomeFragment)
        binding.rvLock.adapter = context?.let { adapterLock }
        arrayLocks = sharePrefs.getLockData()
        adapterLock?.update(sharePrefs.getLockData())

        val dataList = ArrayList<ServicesModel>()
        val model =
            ServicesModel(
                keyless.data.utils.android.R.mipmap.taxi_image,
                getString(keyless.data.utils.android.R.string.taxi),
                "https://xxride.com/"
            )
        dataList.add(model)

        val model2 = ServicesModel(
            keyless.data.utils.android.R.mipmap.delivery_image,
            getString(keyless.data.utils.android.R.string.food_delivery),
            "https://deliveroo.onelink.me/9Aoc/keyless"
        )
        dataList.add(model2)

        val model3 = ServicesModel(
            keyless.data.utils.android.R.mipmap.house_keeping_image,
            getString(keyless.data.utils.android.R.string.house_keeping),
            "https://m.urbancompany.com/ImbW/2mv89x75"
        )
        dataList.add(model3)

        val model4 = ServicesModel(
            keyless.data.utils.android.R.mipmap.ambulance_image,
            getString(keyless.data.utils.android.R.string.emergency_services),
            "https://m.urbancompany.com/ImbW/2mv89x75"
        )
        dataList.add(model4)

        val model5 = ServicesModel(
            keyless.data.utils.android.R.mipmap.local_experinces,
            getString(keyless.data.utils.android.R.string.local_experiences),
            "https://lokalee.app/locations/explore-dubai"
        )
        dataList.add(model5)

        val model6 = ServicesModel(
            keyless.data.utils.android.R.mipmap.groceries_image,
            getString(keyless.data.utils.android.R.string.groceries),
            ""
        )
        dataList.add(model6)

        val model7 = ServicesModel(
            keyless.data.utils.android.R.mipmap.health_care,
            getString(keyless.data.utils.android.R.string.home_health_care),
            ""
        )
        dataList.add(model7)
    }

    private fun clickEvents() {
        binding.forcePassUpdateView.setOnClickListener {
            clickForceUpdate()
        }

        binding.claimKey.setOnClickListener {
            val customDialog = Dialog(requireActivity())
            customDialog.setContentView(R.layout.custom_claim_dialog)
            customDialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
//            customDialog.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            customDialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
                val displayRectangle = Rect()
                val window = customDialog.window
                v.getWindowVisibleDisplayFrame(displayRectangle)
                val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
                val maxWidth = displayRectangle.width() * 0.8f // 60%
                window?.setLayout(maxWidth.toInt(), maxHeight)
            }
            val yesBtn = customDialog.findViewById(R.id.btn_proceed) as TextView
            val noBtn = customDialog.findViewById(R.id.btn_cancel) as TextView
            val txtCodeNumber = customDialog.findViewById(R.id.txtCodeNumber) as EditText
            yesBtn.setOnClickListener {
                val jsonObject = JsonObject()
                jsonObject.addProperty("booking_number", txtCodeNumber.text.toString().trim())
                kotlin.runCatching {
                    viewModel.claimKey(sharePrefs.token, jsonObject).observe(requireActivity()) {
                        if (it.success) {
                            kotlin.runCatching { requireActivity().toast(it.message) }
                            updateValue.clicksIntents(4)
                        } else {
                            kotlin.runCatching { requireActivity().toast(it.message) }
                        }
                    }
                }
                // Do something here
                customDialog.dismiss()
            }
            noBtn.setOnClickListener {
                customDialog.dismiss()
            }
            customDialog.show()
        }

        binding.btnAddLock.setOnClickListener {
            if (Preferences.isAdminLogin()) {
                kotlin.runCatching {
                    defaultDialog(
                        requireActivity(),
                        getString(keyless.data.utils.android.R.string.disabled_in_admin_mode),
                        object : OnActionOK {
                            override fun onClickData() {
                            }
                        }
                    )
                }
            } else {
                updateValue.clicksIntents(3)
            }
        }

        binding.svLock.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                adapterLock?.let {
                    filter.filter(p0.toString())
                    binding.stopSearch.isVisible = p0.toString().isNotEmpty()
                }
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearch.setOnClickListener {
            hideKeyboard()
            binding.svLock.setText("")
            filter.filter("")
        }

        binding.svLockAdmin.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    return@OnEditorActionListener true
                }
                false
            }
        )

        binding.svLockAdmin.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.stopSearchAdmin.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearchAdmin.setOnClickListener {
            hideKeyboard()
            binding.svLockAdmin.setText("")
            apiImplementation(false, 1, binding.svLockAdmin.text.toString())
//            adapterAdminLock.filter.filter("")
        }
    }

    private fun clickForceUpdate() {
        val jsonObject = JsonObject()
        jsonObject.addProperty("uid", sharePrefs.uuid)
        jsonObject.addProperty("is_admin", Preferences.isAdminLogin())
        viewModel.getCheckUser(sharePrefs.token, jsonObject, true).observe(requireActivity()) {
            Preferences.timeZoneName.set(it.timezone_name)
            Preferences.timeZoneOffset.set(it.timezone)
            if (it.force_password_require) {
                val intent = Intent(requireContext(), AuthenticationActivity::class.java)
                intent.putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.UPDATE_PASSWORD)
                startActivityForResult(intent, 12)
            } else {
                requireActivity().toast(
                    getString(keyless.data.utils.android.R.string.your_password_has_been_already_updated)
                )
                binding.forcePassUpdateView.isVisible = false
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 12) {
            lifecycleScope.launch {
                delay(1000)
                apiForData()
            }
        }

        if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            initCommon()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT) {
            if (!CommonValues.isBluetoothEnabled()) {
                val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (!mBluetoothAdapter.isEnabled) {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                } else {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                }
            } else {
                initCommon()
            }
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    private fun performSearch() {
        hideKeyboard()
        apiImplementation(false, 1, binding.svLockAdmin.text.toString())
    }

    override fun clickDelete(
        _id: String,
        uniqueKey: String,
        accessKey: String,
        encryptedKey: String
    ) {
        dialogYesNo(
            requireActivity(),
            getString(keyless.data.utils.android.R.string.alert),
            getString(keyless.data.utils.android.R.string.are_you_sure_you_want_to_delete),
            object : OnActionYesNo {
                override fun onYes(view: View) {
                    idToDelete = _id
                    uniqueKeyDelete = uniqueKey
                    if (accessKey.isNotEmpty()) {
                        accessKeyDelete = accessKey
                        Firebase.crashlytics.log(accessKeyDelete)
                    }

                    methodRequiresTwoPermission()
                }

                override fun onNo(view: View) {
                }

                override fun onClickData(view: View, data: String) {
                }
            }
        )
    }

    private fun showDialogForScanning() {
        dialogLock = Dialog(requireActivity())
        dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
        dialogLock.setCancelable(false)
        dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
        var animatedDots = dialogLock.findViewById<TextAndAnimationView>(
            keyless.feature.common.R.id.animatedDotsDialog
        )
        var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
        animatedDots.animate()
        dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialogLock.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
            val maxWidth = displayRectangle.width() * 0.8f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight)
        }
        cancelBtn.setOnClickListener {
            isScan = false
            mBleLockSdk?.disconnect()
            dialogLock.dismiss()
//            finish()
        }
        dialogLock.show()
    }

    private fun methodRequiresTwoPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[2] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[2] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                        initCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    val lm =
                        requireActivity().getSystemService(AppCompatActivity.LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                            initCommon()
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            }
        }
    }

    private fun initCommon() {
//        progressBar.isVisible = true
        showDialogForScanning()
        initLock()
    }

    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(requireActivity())
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        initCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        startIntentSenderForResult(
                            status.resolution!!.intentSender,
                            REQUEST_CHECK_SETTINGS,
                            null,
                            0,
                            0,
                            0,
                            null
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            requireActivity(),
            "Please allow permissions to continue",
            object : OnActionOK {
                override fun onClickData() {
                    if (activity != null && isAdded) {
                        startActivityForResult(
                            Intent(
                                Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                                Uri.fromParts("package", requireActivity().packageName, null)
                            ),
                            10
                        )
                    }
                }
            }
        )
    }

    override fun getFilter(): Filter {
        var listFiltered = arrayLocks
        return object : Filter() {
            override fun performFiltering(constraint: CharSequence?): FilterResults {
                val charString = constraint?.toString() ?: ""
                listFiltered = if (charString.isEmpty()) {
                    arrayLocks
                } else {
                    val mFilteredList = ArrayList<LocksListResponse.LocksModel>()
                    arrayLocks.filter {
                        (it.lock.name.contains(constraint!!, true)) or
                                (it.lock.name.startsWith(constraint, true))

//                           (it.lock.name.contains(constraint!!,true) || it.property_details.appartment_number.contains(constraint!!,true) ) or
//                                (it.lock.name.startsWith(constraint,true) || it.property_details.appartment_number.startsWith(constraint,true))
                    }
                        .forEach { mFilteredList.add(it) }
                    mFilteredList
                }
                return FilterResults().apply { values = listFiltered }
            }

            override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                listFiltered = if (results?.values == null) {
                    ArrayList()
                } else {
                    results.values as java.util.ArrayList<LocksListResponse.LocksModel>
                }
                if (binding.layNoDataFullPage != null) {
                    binding.layNoDataFullPage.isVisible = listFiltered.size == 0
                }
                adapterLock?.update(listFiltered)
            }
        }
    }

    private fun initLock() {
        mBleName = ArrayList()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan!!.startReceiver()
            isScan = true
        }, 1000)
    }

    override fun onDetach() {
        super.onDetach()
//        running = false
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CommonValues.ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            addAdapterItemRange(msg.obj as BleLockScanData)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (mBleName.size == 0 || checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }

        bluetoothDeviceNew = bluetoothDevice
        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(uniqueKeyDelete)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
            registerLockk()
        } else {
            isScan = true
        }
    }

    private fun registerLockk() {
        mBleLockSdk = BleLockSdk()
        val mBluetoothManager: BluetoothManager? =
            requireActivity().getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk!!.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            val lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = accessKeyDelete.toByteArray()
            mBleLockSdk!!.connect(
                lockCodeClass,
                mBluetoothManager,
                requireActivity(),
                bluetoothDeviceNew.bleMac,
                bluetoothDeviceNew.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    private fun checkName(bleName: String): Boolean {
        if (null == bleName) return false
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            if (resultBean!!.isRet) {
            }
        }

        override fun connect(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
            } else {
            }
        }

        override fun authentication(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                val lockCodeClass = LockCodeClass()
                mBleLockSdk?.registerLock(lockCodeClass)
            } else {
//                defaultDialog(
//                    requireActivity(),
//                    getString(keyless.data.utils.android.R.string.the_lock_could_not_be_connected),
//                    object : OnActionOK {
//                        override fun onClickData() {
//                            mBleLockSdk?.disconnect()
//                        }
//                    })
            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
                mBleLockSdk?.disconnect()
                Firebase.crashlytics.log(idToDelete)
                viewModel.hitAdminDeleteLock(
                    sharePrefs.token,
                    idToDelete
                )
            }
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            if (resultBean.isRet) {
            } else {
            }
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
        }

        override fun onReport(resultBean: ResultBean<*>?) {
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
        }

        override fun disPlay(resultBean: ResultBean<*>) {
        }

        override fun setTempCard(p0: ResultBean<*>?) {
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }
    }

    @SuppressLint("SimpleDateFormat")
    override fun clicking(locksModel: LocksListResponse.LocksModel) {
        if (
            Preferences.userRole.get() == GUEST ||
            Preferences.role.get() == SYSTEM_MANAGER ||
            Preferences.role.get() == VIEWER_ACCESS ||
            Preferences.role.get() == CUSTOMER_SERVICES
        ) {
            val startDate =
                CommonValues.formattedDateOnlyEn(
                    locksModel.assignment?.assignment_data?.valid_from!!.split("+")[0]
                )

            val dateToDisplay = CommonValues.formatDdMmYyyyy(
                locksModel.assignment?.assignment_data?.valid_from!!.split(
                    "+"
                )[0], requireActivity()
            )

            var currentDate = ""

            currentDate = if (CommonValues.isNetworkAvailable(requireActivity())) {
                if (!CommonValues.serverDateTime.isNullOrEmpty()) {
                    CommonValues.formattedDateOnlyEn(
                        CommonValues.serverDateTime.split(
                            "."
                        )[0]
                    )
                } else {
                    val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                    sdf.format(Date())
                }
            } else {
                val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                sdf.format(Date())
            }

            val sdf1 = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
            val startDateTimeMain: Date = sdf1.parse(startDate)!!
            val currentDateTimeMain: Date = sdf1.parse(currentDate)!!
            if (currentDateTimeMain.after(startDateTimeMain) || currentDateTimeMain == startDateTimeMain) {
                val intent = Intent(
                    context,
                    LockDetailsActivity::class.java
                ).putExtra("lockDetails", locksModel)
                startActivity(intent)
            } else {
                defaultDialog(
                    requireActivity(),
                    getString(keyless.feature.common.R.string.this_lock_will_be_accessible_on) + " \n" + dateToDisplay,
                    object : OnActionOK {
                        override fun onClickData() {
                        }
                    }
                )
            }
        } else {
            val intent = Intent(
                context,
                LockDetailsActivity::class.java
            ).putExtra("lockDetails", locksModel)
            startActivity(intent)
        }
    }

    fun updateAdminData() {
        apiImplementation(false, 1, binding.svLockAdmin.text.toString())
    }

    override fun clicking(dataList: ArrayList<ServicesModel>, position: Int) {
        when (position) {
            0 -> {
                showGroceriesDialog("taxi")
            }

            1 -> {
                apiServices("food_delivery")
                launchLink(requireActivity(), dataList[position].link)
            }

            2 -> {
                apiServices("house_keeping")
                launchLink(requireActivity(), dataList[position].link)
            }

            3 -> {
                showEmergencyServices()
            }

            4 -> {
                apiServices("concierge")
                launchLink(requireActivity(), dataList[position].link)
            }

            5 -> {
                showGroceriesDialog("groceries")
            }

            6 -> {
                openActionSheet(1, dataList[position].text)
            }
        }
    }

    private fun openActionSheet(value: Int, text: String) {
        val data: java.util.ArrayList<String> = java.util.ArrayList()
        data.add(getString(keyless.data.utils.android.R.string.call))
        data.add(getString(keyless.data.utils.android.R.string.whatsapp))

        ActionSheet(requireActivity(), data)
            .setTitle(text)
            .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
            .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .create(object : ActionSheetCallBack {
                override fun data(data: String, position: Int) {
                    when (position) {
                        0 -> {
                            if (value == 1) {
                                apiServices("home_health_care/Call")
                                CommonValues.openPhone(
                                    requireActivity(),
                                    "800 337292273"
                                )
                            } else {
                                apiServices("concierge/Call")
                                CommonValues.openPhone(
                                    requireActivity(),
                                    "+971 47047150"
                                )
                            }
                        }

                        1 -> {
                            if (value == 1) {
                                apiServices("home_health_care/Whatsapp")
                                CommonValues.onWhatsAppIntent(
                                    requireActivity(),
                                    "+971 565047773"
                                )
                            } else {
                                apiServices("concierge/Whatsapp")
                                CommonValues.onWhatsAppIntent(
                                    requireActivity(),
                                    "+971 585533729"
                                )
                            }
                        }
                    }
                }
            })
    }

    fun apiServices(serviceName: String) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("service", serviceName)
        viewModel.getServicesApi(sharePrefs.token, jsonObject).observe(this) {
        }
    }

    private fun showEmergencyServices() {
        val data: java.util.ArrayList<String> = java.util.ArrayList()
        data.add(getString(keyless.data.utils.android.R.string.police_999))
        data.add(requireActivity().getString(keyless.data.utils.android.R.string.ambulance_998))
        data.add(requireActivity().getString(keyless.data.utils.android.R.string.fire_department_997))

        ActionSheet(requireActivity(), data)
            .setTitle(getString(keyless.data.utils.android.R.string.emergency_services))
            .setCancelTitle(getString(keyless.feature.common.R.string.text_cancel))
            .setColorTitle(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorTitleCancel(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorSelected(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .setColorData(resources.getColor(keyless.feature.common.R.color.colorAccent))
            .create(object : ActionSheetCallBack {
                override fun data(data: String, position: Int) {
                    when (position) {
                        0 -> {
                            apiServices("emergency_police")
                            CommonValues.openPhone(
                                requireActivity(),
                                "999"
                            )
                        }

                        1 -> {
                            apiServices("emergency_ambulance")
                            CommonValues.openPhone(
                                requireActivity(),
                                "998"
                            )
                        }

                        2 -> {
                            apiServices("emergency_file_department")
                            CommonValues.openPhone(
                                requireActivity(),
                                "997"
                            )
                        }
                    }
                }
            })
    }

    private fun launchLink(context: Context, link: String) {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(link))
        context.startActivity(browserIntent)
    }

    private fun showGroceriesDialog(from: String) {
        val dialog = Dialog(requireActivity())
        dialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog.setContentView(R.layout.groceries_dialog)

        dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
            val displayRectangle = Rect()
            val window = dialog.window
            v.getWindowVisibleDisplayFrame(displayRectangle)
            val maxHeight = displayRectangle.height() * 0.8f // 60%
            val maxWidth = displayRectangle.width() * 0.9f // 60%
            window?.setLayout(maxWidth.toInt(), maxHeight.toInt())
        }

        var ivInstashop = dialog.findViewById<ImageView>(R.id.iv_instashop)
        var ivNoonDaily = dialog.findViewById<ImageView>(R.id.iv_noon_daily)
        var closeDialog = dialog.findViewById<ImageView>(R.id.closeDialog)
        var tvInstashop = dialog.findViewById<TextView>(R.id.tv_instashop)
        var tvNoonDaily = dialog.findViewById<TextView>(R.id.tv_noon_daily)
        var tvGroceries = dialog.findViewById<TextView>(R.id.tv_groceries)
        var cvInstaShop = dialog.findViewById<CardView>(R.id.cv_instashop)
        var cvNoonDaily = dialog.findViewById<CardView>(R.id.cv_noon_daily)

        dialog.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
        if (from == "groceries") {
            ivInstashop.setImageResource(R.drawable.ic_insta_shop_image)
            tvInstashop.setText(keyless.data.utils.android.R.string.instashop)
            tvNoonDaily.setText(keyless.data.utils.android.R.string.noon_daily)
            tvGroceries.setText(keyless.data.utils.android.R.string.groceries)
            ivNoonDaily.setImageResource(R.drawable.ic_noon_daily_image)
        } else {
            ivInstashop.setImageResource(R.drawable.ic_xxride)
            ivNoonDaily.setImageResource(R.drawable.ic_careem)
            tvInstashop.setText(keyless.data.utils.android.R.string.xxride)
            tvGroceries.setText(keyless.data.utils.android.R.string.taxi)
            tvNoonDaily.setText(keyless.data.utils.android.R.string.hala_taxi)
        }

        cvInstaShop.setOnClickListener {
            if (from == "groceries") {
                apiServices("groceries/instashop")
                launchLink(requireActivity(), "https://instashop.com/")
            } else {
                apiServices("taxi/xxride")
                launchLink(
                    requireActivity(),
                    "https://play.google.com/store/apps/details?id=com.app.xxrideuser&hl=en_US"
                )
            }
            dialog.dismiss()
        }

        cvNoonDaily.setOnClickListener {
            if (from == "groceries") {
                apiServices("groceries/noon_daily")
                launchLink(requireActivity(), "https://daily.noon.com/uae-en/")
            } else {
                apiServices("taxi/hala_taxi")
                launchLink(
                    requireActivity(),
                    "https://play.google.com/store/apps/details?id=com.careem.acma&referrer=" +
                            "adjust_reftag%3Dc064DQqtCdC4i%26utm_source%3DIVR%2BSMS%26utm_campaign%3DSira"
                )
            }

            dialog.dismiss()
        }

        closeDialog.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
}