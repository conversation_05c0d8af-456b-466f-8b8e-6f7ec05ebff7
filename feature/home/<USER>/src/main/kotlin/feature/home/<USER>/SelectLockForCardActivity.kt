package feature.home.admin

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Intent
import android.content.IntentSender
import android.content.pm.PackageManager
import android.graphics.Rect
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.android.gms.common.api.GoogleApiClient
import com.google.android.gms.common.api.PendingResult
import com.google.android.gms.common.api.Status
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.LocationSettingsResult
import com.google.android.gms.location.LocationSettingsStatusCodes
import com.google.gson.JsonObject
import core.locks.logs.models.LockLogActionType
import data.network.android.LockModelAdmin
import data.network.android.log
import data.utils.android.CommonValues
import data.utils.android.common.BleLockScanData
import data.utils.android.common.FileUtils
import data.utils.android.hideKeyboard
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import dots.animation.textview.TextAndAnimationView
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.ProgressDialogUtils
import feature.common.dialogs.defaultDialog
import keyless.feature.common.R
import keyless.feature.home.admin.databinding.ActivitySelectLockForCardBinding
import rayo.logicsdk.bean.LockCodeClass
import rayo.logicsdk.ble.BleScanCallback
import rayo.logicsdk.ble.BluetoothLeScan
import rayo.logicsdk.data.LockBasicInfo
import rayo.logicsdk.data.LockReportData
import rayo.logicsdk.sdk.BleLockSdk
import rayo.logicsdk.sdk.BleLockSdkCallback
import rayo.logicsdk.sdk.ResultBean
import java.util.Date
import java.util.Locale

class SelectLockForCardActivity : AppCompatActivity(), LockAdminCardAdapter.ClickToConnect {

    private var decryptedAccessKey: String = ""
    private var cardId: String = ""
    private var isClick: Boolean = true
    private var screen: Int = 1
    private lateinit var selectedItemData: LockModelAdmin
    private val layoutManager = LinearLayoutManager(this)
    lateinit var viewModel: SelectLockForCardViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    private var page = 1
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private lateinit var adapterAdminLock: LockAdminCardAdapter
    private var listTotalAdminLocks: ArrayList<LockModelAdmin> = ArrayList()
    private var listTotalSearchAdminLocks: ArrayList<LockModelAdmin> = ArrayList()
    private var mBleLockSdk: BleLockSdk? = null
    private var mBluetoothManager: BluetoothManager? = null
    private var mBleName: MutableList<String> = ArrayList()
    private var mBluetoothLeScan: BluetoothLeScan? = null
    private var mBleHandler: BleHandler? = null
    private var isScan = false
    private var mBluetoothDeviceHashMap: HashMap<String, BleLockScanData>? = null
    private var mBluetoothDevice: BleLockScanData? = null
    private var mMac: String? = null
    private var mLockBasicInfo: LockBasicInfo? = null
    private var backed: Boolean = false
    private var REQUEST_CHECK_SETTINGS = 3
    lateinit var dialogLock: Dialog
    private var isPopupShow = 0
    private var alreadyPressed = 0
    private lateinit var binding: ActivitySelectLockForCardBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySelectLockForCardBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setAdapter()
        clickListeners()
        observerInit()
    }

    @SuppressLint("HandlerLeak")
    private inner class BleHandler : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                CommonValues.ADD_ADAPTER -> {
                    if (msg.obj != null) {
                        try {
                            addAdapterItemRange(msg.obj as BleLockScanData)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
//                    if (!running) {
//                        running = true
//                        start = object : CountDownTimer(15000, 10) {
//                            override fun onTick(millisUntilFinished: Long) {
//                                if (msg.obj != null) {
//                                    try {
//                                        addAdapterItemRange(msg.obj as BleLockScanData)
//                                    } catch (e: Exception) {
//                                        e.printStackTrace()
//                                    }
//                                }
//                            }
//
//                            override fun onFinish() {
//                                if (!backed) {
//                                    runOnUiThread {
//                                        if (!isFinishing) {
//                                            try {
//                                                progressBar.isVisible = false
//                                                defaultDialog(
//                                                    this@SelectLockForCardActivity,
//                                                    getString(keyless.data.utils.android.R.string.scanned_lock_could_not_found),
//                                                    object : OnActionOK {
//                                                        override fun onClickData() {
//                                                            mBluetoothLeScan!!.stopReceiver()
//                                                            mBleLockSdk?.disconnect()
//                                                            running = false
//                                                        }
//                                                    })
//                                            } catch (e: WindowManager.BadTokenException) {
//                                                Log.e("WindowManagerBad ", e.toString())
//                                            }
//                                        }
//                                    }
//
//                                }
//                            }
//
//                        }.start()
//                    }
                }
            }
        }
    }

    private fun addAdapterItemRange(bluetoothDevice: BleLockScanData) {
        if (checkName(bluetoothDevice.bleName)) {
            if (!mBluetoothDeviceHashMap!!.containsKey(bluetoothDevice.bleMac)) {
                addItemIn(bluetoothDevice)
                mBluetoothDeviceHashMap!![bluetoothDevice.bleMac] = bluetoothDevice
            }
        }
    }

    private fun checkName(bleName: String): Boolean {
        for (ble in bleName) {
            if (bleName.contains(ble)) {
                return true
            }
        }
        return false
    }

    private fun addItemIn(bluetoothDevice: BleLockScanData) {
        if (bluetoothDevice == null) {
            return
        }
        mBluetoothDevice = bluetoothDevice
        if (mBluetoothDevice!!.bleName.contains(selectedItemData.lock.unique_key)) {
            mBluetoothLeScan!!.stopReceiver()
            isScan = false
//            start.cancel()
            connectLock()
        } else {
            isScan = true
        }
    }

    private fun setAdapter() {
        binding.rvAdminLocks.layoutManager = layoutManager
        adapterAdminLock = LockAdminCardAdapter(this)
        binding.rvAdminLocks.adapter = adapterAdminLock
        pagination()
    }

    private fun observerInit() {
        viewModel.getResponseAdminLocks.observe(this) {
            if (page == 1) {
                listTotalAdminLocks.clear()
                listTotalSearchAdminLocks.clear()
            }
            if (binding.svLockAdmin.text.toString().isEmpty()) {
                listTotalAdminLocks.addAll(it.locks)
                if (listTotalAdminLocks.size > 0) {
                    binding.layNoDataFullPage.isVisible = false
                    binding.rvAdminLocks.isVisible = true
                    binding.svLockAdmin.isVisible = true
                    adapterAdminLock.updateList(listTotalAdminLocks)
                } else {
                    binding.rvAdminLocks.isVisible = false
                    binding.svLockAdmin.isVisible = false
                    binding.stopSearchAdmin.isVisible = false
                    binding.textView32.isVisible = false
                    binding.layNoDataFullPage.isVisible = true
                }

                if (listTotalAdminLocks.size == it.total_locks) {
                    isLastPage = true
                    isLoadingMore = true
                }
            } else {
                listTotalSearchAdminLocks.addAll(it.locks)
                if (listTotalSearchAdminLocks.size > 0) {
                    binding.layNoDataFullPage.isVisible = false
                    binding.rvAdminLocks.isVisible = true
                    binding.svLockAdmin.isVisible = true
                    adapterAdminLock.updateList(listTotalSearchAdminLocks)
                } else {
                    binding.rvAdminLocks.isVisible = false
                    binding.svLockAdmin.isVisible = true
                    binding.stopSearchAdmin.isVisible = true
                    binding.layNoDataFullPage.isVisible = true
                }

                if (listTotalSearchAdminLocks.size == it.total_locks) {
                    isLastPage = true
                    isLoadingMore = true
                }
            }
            binding.progressPagination.isVisible = false
        }

        viewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }

        viewModel.error.observe(this) {
            toast(it)
        }
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        if (screen == 1) {
            finish()
        } else if (screen == 2) {
            binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
            binding.screenAddRayonicsCard.isVisible = false
            binding.screenSelectLock.isVisible = true
            screen = 1
            isClick = true
            binding.etCardId.setText("")
            cardId = ""
            mBleLockSdk?.disconnect()
//            running = false
            backed = true
        }
    }

    private fun clickListeners() {
        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (object : OnBackPressedCallback(true),
                    OnBackInvokedCallback {
                    override fun handleOnBackPressed() {

                    }

                    override fun onBackInvoked() {
                        if (screen == 1) {
                            finish()
                        } else if (screen == 2) {
                            binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
                            binding.screenAddRayonicsCard.isVisible = false
                            binding.screenSelectLock.isVisible = true
                            screen = 1
                            isClick = true
                            binding.etCardId.setText("")
                            cardId = ""
                            mBleLockSdk?.disconnect()
                            mBleScanCallback.finishScan()
                            backed = true
                        }
                    }
                })
            )
        }
        binding.btnAddCard.setOnClickListener {
            if (cardId.isEmpty()) {
                toast(getString(keyless.data.utils.android.R.string.please_tap_card_to_generate_card_id))
            } else {
                val jsonObject = JsonObject()
                jsonObject.addProperty("uid", cardId)
                jsonObject.addProperty("internal_id", binding.etInternalId.text.toString().trim())

                viewModel.createCard(sharePrefs.token, jsonObject).observe(this) {
                    if (it.success) {
                        defaultDialog(
                            this@SelectLockForCardActivity,
                            getString(R.string.card_has_been_added),
                            object : OnActionOK {
                                override fun onClickData() {
                                    viewModel.getCardSeries(sharePrefs.token)
                                        .observe(this@SelectLockForCardActivity) { it1 ->
                                            if (it.success) {
                                                binding.etInternalId.setText(it1.internal_id)
                                                binding.etCardId.setText("")
                                                cardId = ""
                                                isClick = true
                                            }
                                        }
                                }
                            }
                        )
                    } else {
                        toast(it.message)
                    }
                }
            }
        }

        binding.etCardId.setOnClickListener {
            if (isClick) {
                mBleLockSdk?.readCardByCylinder(15)
                isClick = false
            }
        }

        binding.backBtn.setOnClickListener {
            if (screen == 1) {
                finish()
            } else if (screen == 2) {
                screen = 1
                isClick = true
                binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.select_lock)
                binding.screenAddRayonicsCard.isVisible = false
                binding.screenSelectLock.isVisible = true
                binding.etCardId.setText("")
                cardId = ""
                mBleLockSdk?.disconnect()
//                running = false
                backed = true
            }
        }

        binding.svLockAdmin.setOnEditorActionListener(
            TextView.OnEditorActionListener { v, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    return@OnEditorActionListener true
                }
                false
            }
        )

        binding.svLockAdmin.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                binding.stopSearchAdmin.isVisible = p0.toString().isNotEmpty()
            }

            override fun afterTextChanged(p0: Editable?) {
            }
        })

        binding.stopSearchAdmin.setOnClickListener {
            hideKeyboard()
            binding.svLockAdmin.setText("")
            apiImplementation(false, 1, binding.svLockAdmin.text.toString())
//            adapterAdminLock.filter.filter("")
        }
    }

    private fun performSearch() {
        hideKeyboard()
        apiImplementation(false, 1, binding.svLockAdmin.text.toString())
    }

    private fun initz() {
        viewModel = ViewModelProvider(this)[SelectLockForCardViewModel::class.java]
        apiImplementation(false, page, binding.svLockAdmin.text.toString())
    }

    private fun apiImplementation(progress: Boolean, page: Int, search: String) {
        viewModel.hitAdminLocksApi(sharePrefs.token, progress, page, search)
    }

    private fun pagination() {
        binding.rvAdminLocks.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore) {
                    page++
                    apiImplementation(true, page, binding.svLockAdmin.text.toString())
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })
    }

    override fun clickForCard(model: LockModelAdmin) {
        if (alreadyPressed == 0) {
            alreadyPressed = 1
            selectedItemData = model
            askPermission()
        }
    }

    @SuppressLint("MissingPermission")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 50) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (
                    grantResults.isNotEmpty() &&
                    grantResults[0] == PackageManager.PERMISSION_GRANTED &&
                    grantResults[1] == PackageManager.PERMISSION_GRANTED
                ) {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                } else {
                    showDialogForPermissions()
                }
            } else {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    val lm = getSystemService(LOCATION_SERVICE) as LocationManager
                    var gps_enabled = false
                    var network_enabled = false

                    try {
                        gps_enabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    try {
                        network_enabled = lm.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                    } catch (ex: java.lang.Exception) {
                    }

                    if (!gps_enabled && !network_enabled) {
                        displayLocationSettingsRequest()
                    } else {
                        if (CommonValues.isBluetoothEnabled()) {
                            initializationCommon()
                        } else {
                            val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                            if (!mBluetoothAdapter.isEnabled) {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            } else {
                                val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                startActivityForResult(
                                    intentBtEnabled,
                                    CommonValues.REQUEST_ENABLE_BT
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun displayLocationSettingsRequest() {
        val googleApiClient = GoogleApiClient.Builder(this)
            .addApi(LocationServices.API).build()
        googleApiClient.connect()
        val locationRequest: LocationRequest = LocationRequest.create()
        locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        locationRequest.interval = 10000
        locationRequest.fastestInterval = 10000 / 2
        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val result: PendingResult<LocationSettingsResult> =
            LocationServices.SettingsApi.checkLocationSettings(googleApiClient, builder.build())
        result.setResultCallback { result ->
            val status: Status = result.status
            when (status.statusCode) {
                LocationSettingsStatusCodes.SUCCESS -> {
                    if (CommonValues.isBluetoothEnabled()) {
                        initializationCommon()
                    } else {
                        val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                        if (!mBluetoothAdapter.isEnabled) {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        } else {
                            val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                            startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                        }
                    }
                }

                LocationSettingsStatusCodes.RESOLUTION_REQUIRED -> {
                    try {
                        status.startResolutionForResult(
                            this@SelectLockForCardActivity,
                            REQUEST_CHECK_SETTINGS
                        )
                    } catch (e: IntentSender.SendIntentException) {
                    }
                }

                LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE -> {}
            }
        }
    }

    private fun showDialogForPermissions() {
        defaultDialog(
            this,
            getString(keyless.data.utils.android.R.string.please_allow_bluetooth),
            object : OnActionOK {
                override fun onClickData() {
                    startActivityForResult(
                        Intent(
                            Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                            Uri.fromParts("package", packageName, null)
                        ),
                        10
                    )
                }
            }
        )
    }

    private fun initializationCommon() {
        decryptedAccessKey = if (selectedItemData.lock.encrypted_key.isNotEmpty()) {
            selectedItemData.lock.access_key
        } else {
            selectedItemData.lock.access_key
        }
        initLock()
        showDialogForScanning()

//        progressBar.isVisible = true
    }

    private fun showDialogForScanning() {
        if (isPopupShow == 0) {
            isPopupShow = 1
            dialogLock = Dialog(this)
            dialogLock.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialogLock.window?.setBackgroundDrawableResource(keyless.feature.common.R.drawable.white_all_corners_10)
            dialogLock.setCancelable(false)
            dialogLock.setContentView(keyless.feature.common.R.layout.lock_scanning_dialog)
            var animatedDots =
                dialogLock.findViewById<TextAndAnimationView>(keyless.feature.common.R.id.animatedDotsDialog)
            var cancelBtn = dialogLock.findViewById<TextView>(keyless.feature.common.R.id.cancelBtn)
            animatedDots.animate()
            dialogLock.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
                val displayRectangle = Rect()
                val window = dialogLock.window
                v.getWindowVisibleDisplayFrame(displayRectangle)
                val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
                val maxWidth = displayRectangle.width() * 0.8f // 60%
                window?.setLayout(maxWidth.toInt(), maxHeight)
            }

            cancelBtn.setOnClickListener {
                isScan = false
                mBluetoothLeScan!!.stopReceiver()
                mBleLockSdk?.disconnect()
                mBluetoothLeScan = null
                mBleScanCallback.finishScan()
                isPopupShow = 0
                alreadyPressed = 0

                dialogLock.dismiss()
            }
            dialogLock.show()
        }
    }

    private fun askPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
                ),
                50
            )
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION
                ),
                50
            )
        }
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 10) {
            askPermission()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode != 0) {
            initializationCommon()
        } else if (requestCode == CommonValues.REQUEST_ENABLE_BT && resultCode == 0) {
            if (!CommonValues.isBluetoothEnabled()) {
                val mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
                if (!mBluetoothAdapter.isEnabled) {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                } else {
                    val intentBtEnabled = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intentBtEnabled, CommonValues.REQUEST_ENABLE_BT)
                }
            }
        } else if (requestCode == REQUEST_CHECK_SETTINGS) {
            displayLocationSettingsRequest()
        }
    }

    override fun onResume() {
        super.onResume()
//        running = false
    }

    private fun initLock() {
        mBleName = ArrayList<String>()
        mBleName.add(CommonValues.EXTRA_C_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_D_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_B_BLE_NAME)
        mBleName.add(CommonValues.EXTRA_E_BLE_NAME)
        mBluetoothLeScan =
            object : BluetoothLeScan(BluetoothAdapter.getDefaultAdapter(), 0, mBleScanCallback) {
                override fun enableBluetooth(): Boolean {
                    return true
                }
            }
        mBleHandler = BleHandler()
        isScan = false
        mBluetoothDeviceHashMap = HashMap()
        Handler().postDelayed({
            mBluetoothLeScan?.stopReceiver()
            isScan = false
            mBluetoothDeviceHashMap = HashMap()
            mBluetoothLeScan?.startReceiver()
            isScan = true
        }, 1000)
    }

    private val mBleScanCallback: BleScanCallback = object : BleScanCallback {
        @SuppressLint("MissingPermission")
        override fun findBle(bluetoothDevice: BluetoothDevice, rssi: Int, scanRecord: ByteArray) {
            val bleLockScanData = BleLockScanData(
                bluetoothDevice.name ?: "",
                bluetoothDevice.address ?: "",
                scanRecord
            )
            mBleHandler!!.obtainMessage(CommonValues.ADD_ADAPTER, bleLockScanData).sendToTarget()
        }

        override fun finishScan() {}
    }

    private fun connectLock() {
        mMac = mBluetoothDevice?.bleMac
        mBleLockSdk = BleLockSdk()
        mLockBasicInfo = LockBasicInfo()
        mBluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager?
        mBleLockSdk?.init(mBluetoothManager, mBleLockSdkCallback)
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            val config = FileUtils.readStringFromFile(applicationContext)
            var lockCodeClass = JSON.parseObject(
                config,
                LockCodeClass::class.java
            )
            if (null == lockCodeClass) {
                lockCodeClass = LockCodeClass()
            }

            lockCodeClass = LockCodeClass()
            lockCodeClass.ivCode = "8qli5ljbkBJSBip8".toByteArray()
            lockCodeClass.registerCode = "i4ZP1vbtGwBB33EL".toByteArray()
            lockCodeClass.sysCode = decryptedAccessKey.toByteArray()
            mBleLockSdk?.connect(
                lockCodeClass,
                mBluetoothManager,
                this,
                mBluetoothDevice?.bleMac,
                mBluetoothDevice?.scanRecord,
                "1".toByteArray(),
                Date(),
                false
            )
        }, 10)
    }

    private val mBleLockSdkCallback: BleLockSdkCallback = object : BleLockSdkCallback {
        override fun init(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                init sdk  
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
            rayo.logicsdk.utils.Log.d(
                "TAG",
                JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun connect(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
            connect 
            ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
            selectedItemData.lock.log(
                actionType = LockLogActionType.SelectCard,
                message = "Connected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun authentication(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """ authentication ${
                JSON.toJSONString(
                    resultBean,
                    SerializerFeature.WriteDateUseDateFormat
                )
                }""",
                this@SelectLockForCardActivity
            )

            if (resultBean.isRet) {
                isPopupShow = 0
                alreadyPressed = 0
                dialogLock.dismiss()
                binding.progressBar.isVisible = false
                viewModel.getCardSeries(sharePrefs.token).observe(this@SelectLockForCardActivity) {
                    if (it.success) {
                        binding.etInternalId.setText(it.internal_id)
                    }
                }
                screen = 2
                binding.titleToolbar.text = getString(keyless.data.utils.android.R.string.add_ryonics_card)
                binding.screenAddRayonicsCard.isVisible = true
                binding.screenSelectLock.isVisible = false
                selectedItemData.lock.log(
                    actionType = LockLogActionType.SelectCard,
                    message = "Authentication Successfully",
                    data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
                )
            } else {
                binding.progressBar.isVisible = false
                if (JSON.toJSONString(
                                    resultBean,
                                    SerializerFeature.WriteDateUseDateFormat
                                ).lowercase(Locale.getDefault()).contains("timeout")
                ) {
//                    defaultDialog(
//                        this@SelectLockForCardActivity,
//                        getString(keyless.data.utils.android.R.string.the_lock_could_not_be_connected),
//                        object : OnActionOK {
//                            override fun onClickData() {
//                            }
//                        })

                    selectedItemData.lock.log(
                        actionType = LockLogActionType.SelectCard,
                        message = "Authentication Failed",
                        data = JSON.toJSONString(
                            resultBean,
                            SerializerFeature.WriteDateUseDateFormat
                        )
                    )
                } else {
                    toast(
                        JSON.toJSONString(
                            resultBean,
                            SerializerFeature.WriteDateUseDateFormat
                        )
                    )
                }
            }
        }

        override fun disConnect(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                disConnect 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )

            selectedItemData.lock.log(
                actionType = LockLogActionType.SelectCard,
                message = "Disconnected",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
//            running = false
        }

        override fun registerLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                registerLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun getLockInfo(resultBean: ResultBean<*>) {
//            CommonValues.showMessage(
//                """
//                getLockInfo
//                ${resultBean.obj}
//                """.trimIndent(),
//                this@SelectLockForCardActivity
//            )
        }

        override fun setLockInfo(resultBean: ResultBean<*>?) {
//            CommonValues.showMessage(
//                """
//                setLockInfo
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@SelectLockForCardActivity
//            )
        }

        override fun setLockTime(resultBean: ResultBean<*>?) {
//            CommonValues.showMessage(
//                """
//                setLockTime
//                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
//                """.trimIndent(),
//                this@SelectLockForCardActivity
//            )
        }

        override fun getLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                getLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setLockOfficeMode(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                setLockOfficeMode 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun getLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun checkLockHotPoint(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                checkLockHotPoint 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun getLockStatus(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                getLockStatus 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun readLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun cleanLockEvent(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockEvent 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun updateLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                updateLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun findLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun cleanLockPermission(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanLockPermission 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun bleOpenLock(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                bleOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun pincodeOpenLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                pincodeOpenLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun findLockBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                findLockBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun cleanBlacklist(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                cleanBlacklist 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setCalendar(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setCalendar 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun resetLock(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLock 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun resetLockFactory(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                resetLockFactory 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setLockSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                setLockSerialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun readCardByCylinder(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                readCardByCylinder
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun onReport(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                onReport 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )

            if (resultBean!!.isRet) {
                val mCardInfo = resultBean.obj as (LockReportData)
                cardId = mCardInfo.cardId.toString().uppercase()
                binding.etCardId.setText(cardId)
            }
            selectedItemData.lock.log(
                actionType = LockLogActionType.SelectCard,
                message = "Report",
                data = JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)
            )
        }

        override fun getReaderInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                get reader info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setReaderSerialId(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set reader serialId 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun getKeyboardInfo(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                get keyboard info 
                ${resultBean.obj}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setKeyboardInfo(resultBean: ResultBean<*>?) {
            CommonValues.showMessage(
                """
                set keyboard info 
                ${JSON.toJSONString(resultBean, SerializerFeature.WriteDateUseDateFormat)}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun disPlay(resultBean: ResultBean<*>) {
            CommonValues.showMessage(
                """
                Bluetooth Data 
                ${resultBean.obj as String}
                """.trimIndent(),
                this@SelectLockForCardActivity
            )
        }

        override fun setTempCard(p0: ResultBean<*>?) {
        }

        override fun deleteTempCard(p0: ResultBean<*>?) {
        }

        override fun findTempCard(p0: ResultBean<*>?) {
        }
    }
}