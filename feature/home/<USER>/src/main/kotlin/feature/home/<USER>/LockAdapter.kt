package feature.home.locks

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import data.common.preferences.Preferences
import data.common.preferences.Roles
import data.network.android.ApiUtils.IMAGE_BASE_URL
import data.network.android.LocksListResponse
import data.utils.android.CommonValues
import keyless.feature.home.locks.databinding.LockAdapterLayoutBinding
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class LockAdapter(
    contextMain: Fragment
) :
    RecyclerView.Adapter<LockAdapter.ViewHolder>() {

    lateinit var context: Context
    var listFiltered: ArrayList<LocksListResponse.LocksModel> = ArrayList()
    private var isValid: Boolean = false
    var listener = contextMain as ClickToEnter

    override fun onCreateViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        context = viewGroup.context

        val binding = LockAdapterLayoutBinding.inflate(
            LayoutInflater.from(context),
            viewGroup,
            false
        )
        return ViewHolder(binding)
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(viewHolder: ViewHolder, position: Int) {
        viewHolder.bind(listFiltered[position], position)
    }

    override fun getItemCount() = listFiltered.size
    fun update(arrayLocks: java.util.ArrayList<LocksListResponse.LocksModel>) {
        listFiltered = arrayLocks
        notifyDataSetChanged()
    }

    interface ClickToEnter {
        fun clicking(locksModel: LocksListResponse.LocksModel)
    }

    inner class ViewHolder(val binding: LockAdapterLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(model: LocksListResponse.LocksModel, position: Int) {
            if (listFiltered[position].lock.icon.size > 0) {
                if (listFiltered[position].lock.icon[0].icon.isNotEmpty()) {
                    Glide.with(context)
                        .load(IMAGE_BASE_URL + listFiltered[position].lock.icon[0].icon)
                        .placeholder(keyless.feature.common.R.drawable.iv_other_icon)
                        .into(binding.ivIconHome)
                } else {
                    binding.ivIconHome.setImageResource(keyless.feature.common.R.drawable.iv_other_icon)
                }
            } else {
                binding.ivIconHome.setImageResource(keyless.feature.common.R.drawable.iv_other_icon)
            }

            binding.txtLockName.text = listFiltered[position].lock.name
            if (listFiltered[position].property_details.appartment_number.isEmpty()) {
                binding.txtLockPlace.text =
                    listFiltered[position].property_details.floor + " " + context.getString(
                    keyless.data.utils.android.R.string.txt_floor
                )
            } else {
                binding.txtLockPlace.text =
                    listFiltered[position].property_details.appartment_number +
                    ", " + listFiltered[position].property_details.floor + " " +
                    context.getString(keyless.data.utils.android.R.string.txt_floor)
            }

            binding.txtPropertyName.text = listFiltered[position].property_details.name

            binding.lay.setOnClickListener {
                listener.clicking(listFiltered[position])
            }

            if (
                Preferences.userRole.get() == CommonValues.GUEST ||
                Preferences.role.get() == Roles.SYSTEM_MANAGER ||
                Preferences.role.get() == Roles.VIEWER_ACCESS ||
                Preferences.role.get() == Roles.CUSTOMER_SERVICES
            ) {
                val startDate =
                    CommonValues.formattedDateOnlyEn(
                        listFiltered[position].assignment?.assignment_data?.valid_from!!.split("+")[0]
                    )
                var currentDate = ""

                currentDate = if (CommonValues.isNetworkAvailable(context)) {
                    if (!CommonValues.serverDateTime.isNullOrEmpty()) {
                        CommonValues.formattedDateOnlyEn(
                            CommonValues.serverDateTime.split(
                                "."
                            )[0]
                        )
                    } else {
                        val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                        sdf.format(Date())
                    }
                } else {
                    val sdf = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                    sdf.format(Date())
                }

                Log.e("currentDate", currentDate)
                val sdf1 = SimpleDateFormat(CommonValues.DATE_FORMAT, Locale("en"))
                val startDateTimeMain: Date = sdf1.parse(startDate)!!
                val currentDateTimeMain: Date = sdf1.parse(currentDate)!!
                if (currentDateTimeMain.after(startDateTimeMain) || currentDateTimeMain == startDateTimeMain) {
                    binding.inactive.isVisible = false
                    binding.lay.setCardBackgroundColor(0)
                } else {
                    binding.inactive.isVisible = true
                    binding.lay.setCardBackgroundColor(
                        context.getColor(keyless.feature.common.R.color.border_color)
                    )
                }
            } else {
                binding.inactive.isVisible = false
            }
        }
    }
}