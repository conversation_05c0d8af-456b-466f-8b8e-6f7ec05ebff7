<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:layout_marginTop="20dp"
    android:orientation="horizontal"
    android:visibility="visible"
    app:cardBackgroundColor="@color/border_color"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp"
    app:cardMaxElevation="0dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="30dp"
        android:paddingBottom="30dp">


        <ImageView
            android:id="@+id/imageView7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center|start"
            android:layout_marginStart="24dp"
            android:src="@drawable/keyless_logo_2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_marginTop="-3dp"
            android:id="@+id/txtHeading"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_bold_700"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="@color/black"
            android:textSize="17dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageView7"
            app:layout_constraintTop_toTopOf="@+id/imageView7" />


        <TextView
            android:id="@+id/txtDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/poppins_regular_400"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/txtHeading"
            app:layout_constraintTop_toBottomOf="@+id/txtHeading" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:drawableStart="@drawable/iv_how_works"
            android:drawablePadding="6dp"
            android:fontFamily="@font/poppins_bold_700"
            android:gravity="center"
            android:text="How it works"
            android:textColor="@color/black"
            android:textSize="13dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/txtDescription"
            app:layout_constraintStart_toStartOf="@+id/txtDescription"
            app:layout_constraintTop_toBottomOf="@+id/txtDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
