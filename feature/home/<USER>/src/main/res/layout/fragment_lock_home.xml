<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="feature.home.locks.LockHomeFragment">



    <EditText
        android:id="@+id/sv_lock"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/bg_btn_round"
        android:backgroundTint="@color/bg_edit_grey"
        android:drawableStart="@drawable/ic_baseline_search_24"
        android:drawablePadding="10dp"
        android:ellipsize="end"
        style="@style/mirrorText"
        android:imeOptions="actionDone"
        android:fontFamily="@font/poppins_regular_400"
        android:hint="@string/search_by_lock_name"
        android:includeFontPadding="false"
        android:padding="8dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forcePassUpdateView" />

    <ImageView
        android:id="@+id/stopSearch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:src="@drawable/iv_cross_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/sv_lock"
        app:layout_constraintEnd_toEndOf="@+id/sv_lock"
        app:layout_constraintTop_toTopOf="@+id/sv_lock" />


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sv_lock"
        app:layout_constraintVertical_bias="0.0">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/guestViewPager"
                android:layout_width="match_parent"
                android:layout_height="160dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabIndicatorGuest"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_marginBottom="16dp"
                android:backgroundTint="@color/transparent"
                app:layout_constraintBottom_toBottomOf="@+id/guestViewPager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintWidth_percent="0.25"
                app:tabBackground="@drawable/indicator_selector_guest"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabMaxWidth="30dp"
                app:tabMode="fixed"
                app:tabPaddingEnd="10dp"
                app:tabPaddingStart="10dp"
                app:tabRippleColor="@null" />

            <TextView
                android:id="@+id/forcePassUpdateViewGuest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="20dp"
                android:includeFontPadding="false"
                android:background="@drawable/force_update_bg"
                android:fontFamily="@font/poppins_regular_400"
                android:gravity="center"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:visibility="gone"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:textColor="@color/black"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/guestViewPager" />


            <TextView
                android:id="@+id/txtYourLocks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/your_keys"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/forcePassUpdateViewGuest" />


            <TextView
                android:id="@+id/claimKey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_blue_outline"
                android:fontFamily="@font/poppins_semibold_600"
                android:includeFontPadding="false"
                android:paddingLeft="14dp"
                android:paddingTop="4dp"
                android:paddingRight="14dp"
                android:paddingBottom="4dp"
                android:text="@string/claim_your_key"
                android:textColor="@color/colorAccent"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/forcePassUpdateViewGuest" />




            <LinearLayout
                android:layout_width="match_parent"
                android:id="@+id/linearYourLocks"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txtYourLocks"
                app:layout_constraintVertical_bias="0.0"
                android:layout_height="wrap_content">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:layout_marginTop="36dp"
                    android:id="@+id/layNoDataHalfPage"
                    android:visibility="gone"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:src="@drawable/iv_no_locks" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:fontFamily="@font/poppins_semibold_600"
                        android:text="@string/no_locks_assigned"
                        android:textColor="@color/black"
                        android:textSize="14dp"
                        android:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/rv_lock" />


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_lock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="4dp"
                    android:gravity="center"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>





            <TextView
                android:id="@+id/txtQuickServices"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="36dp"
                android:fontFamily="@font/poppins_semibold_600"
                android:text="@string/quick_services"
                android:textColor="@color/black"
                android:textSize="14dp"
                android:visibility="visible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/linearYourLocks" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvServices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="10dp"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txtQuickServices" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>


<!--    NO LOCKS INDICATION WITH ADD BUTTON FUNCTIONALITY-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:id="@+id/layNoDataFullPage"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/iv_no_locks" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/poppins_semibold_600"
            android:text="@string/no_locks_are_found"
            android:textColor="@color/black"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_lock" />


        <TextView
            android:id="@+id/btnAddLock"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginStart="60dp"
            android:layout_marginTop="25dp"
            android:layout_marginEnd="60dp"
            android:layout_marginBottom="41dp"
            android:includeFontPadding="false"
            android:background="@drawable/bg_btn_round"
            android:fontFamily="@font/poppins_medium_500"
            android:gravity="center"
            android:text="@string/add_lock"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </LinearLayout>

<!--    FORCE UPDATE PASSWORD ALL USERS NOT GUEST-->
    <TextView
        android:id="@+id/forcePassUpdateView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        android:background="@drawable/force_update_bg"
        android:fontFamily="@font/poppins_regular_400"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:visibility="gone"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp"
        android:includeFontPadding="false"
        android:textColor="@color/black"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />



    <!--    ADMIN-->
    <EditText
        android:id="@+id/svLockAdmin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="20dp"
        style="@style/mirrorText"
        android:background="@drawable/bg_btn_round"
        android:backgroundTint="@color/bg_edit_grey"
        android:drawableStart="@drawable/ic_baseline_search_24"
        android:drawablePadding="10dp"
        android:ellipsize="end"
        android:fontFamily="@font/poppins_regular_400"
        android:hint="@string/search_by_internal_id_or_lock_id"
        android:includeFontPadding="false"
        android:padding="8dp"
        android:focusableInTouchMode="true"
        android:imeOptions="actionSearch"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingRight="30dp"
        android:singleLine="true"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forcePassUpdateView" />

    <!--    ADMIN-->
    <ImageView
        android:id="@+id/stopSearchAdmin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:src="@drawable/iv_cross_black"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/svLockAdmin"
        app:layout_constraintEnd_toEndOf="@+id/svLockAdmin"
        app:layout_constraintTop_toTopOf="@+id/svLockAdmin" />

<!--    ADMIN-->
    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:id="@+id/rvAdminLocks"
        android:layout_height="0dp"
        android:visibility="gone"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/svLockAdmin"
        app:layout_constraintVertical_bias="0.0" />


<!--    TODO REMOVE NOT USED-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="#33000000"
        android:id="@+id/progressBar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:layout_height="match_parent">


        <LinearLayout
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:gravity="center"
            android:background="@drawable/white_all_corners_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:theme="@style/progressBarBlue"
                android:progressTint="@color/colorAccent"
                android:progressBackgroundTint="@color/colorAccent"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>



    <ProgressBar
        android:id="@+id/progress_pagination"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:theme="@style/progressBarBlue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>