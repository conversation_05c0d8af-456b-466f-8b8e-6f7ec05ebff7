plugins {
    id("keyless.android.feature")
}

android {
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

dependencies {
    implementation(project(":data-common"))
    implementation(project(":data-utils-android"))
    implementation(project(":data-network-android"))
    implementation(project(":feature-common"))

    implementation(project(":rayonicsSdk"))

    implementation(keyless.androidx.lifecycle.livedata)

    implementation("com.alibaba:fastjson:1.1.60.android")
    implementation("com.google.android.gms:play-services-location:21.0.1")
}