package feature.common.navigation

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import data.common.preferences.Preferences
import feature.common.application.App
import feature.common.dialogs.OnActionOK
import feature.common.dialogs.defaultDialog

fun logoutAndNavToDashboard(
    message: String,
    context: Activity
) {
    defaultDialog(
        context,
        message,
        object : OnActionOK {
            override fun onClickData() {
                context.startActivity(Intent(context, App.DASHBOARD_ACTIVITY_CLASS).putExtra("runSplash", true))
                Preferences.isLoggedIn.set(false)
                context.finishAffinity()
            }
        }
    )
}

fun NavController.safeNavigate(
    @IdRes currentDestinationId: Int,
    @IdRes id: Int,
    navOptions: NavOptions? = null,
    args: Bundle? = null
) {
    if (currentDestinationId == currentDestination?.id) {
        navigate(id, args, navOptions)
    }
}