package feature.common.compose.button

import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp

@Composable
fun BackButton(
    modifier: Modifier = Modifier,
    isEnabled: Boolean,
    onClick: () -> Unit
) {
    IconButton(
        modifier = modifier,
        enabled = isEnabled,
        onClick = onClick
    ) {
        val direction = LocalLayoutDirection.current
        val icon = remember(LocalLayoutDirection.current) {
            if (direction == LayoutDirection.Ltr) {
                Icons.Default.KeyboardArrowLeft
            } else {
                Icons.Default.KeyboardArrowRight
            }
        }

        Icon(
            modifier = Modifier.size(36.dp),
            imageVector = icon,
            contentDescription = "",
            tint = MaterialTheme.colorScheme.onPrimary
        )
    }
}