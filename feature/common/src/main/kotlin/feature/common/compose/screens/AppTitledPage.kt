package feature.common.compose.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import feature.common.compose.button.BackButton
import feature.common.compose.text.AppPageTitleText
import feature.common.compose.theme.AppTheme
import feature.common.compose.theme.Dimensions
import keyless.feature.common.R

@Composable
fun AppLogoPage(
    modifier: Modifier = Modifier,
    isBackEnabled: Boolean,
    onBackPress: () -> Unit,
    content: @Composable () -> Unit
) {
    AppBasePage(
        modifier = modifier,
        title = { Image(painterResource(R.drawable.keyless_logo), contentDescription = null) },
        isBackEnabled = isBackEnabled,
        onBackPress = onBackPress,
        content = content
    )
}

@Composable
fun AppTitledPage(
    modifier: Modifier = Modifier,
    title: String,
    isBackEnabled: Boolean,
    onBackPress: () -> Unit,
    content: @Composable () -> Unit
) {
    AppBasePage(
        modifier = modifier,
        title = { AppPageTitleText(modifier = Modifier.align(Alignment.Center), text = title) },
        isBackEnabled = isBackEnabled,
        onBackPress = onBackPress,
        content = content
    )
}

@Composable
private fun AppBasePage(
    modifier: Modifier = Modifier,
    title: @Composable BoxScope.() -> Unit,
    isBackEnabled: Boolean,
    onBackPress: () -> Unit,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp)
        ) {

            title()

            BackButton(modifier = Modifier.align(Alignment.CenterStart), isBackEnabled, onBackPress)
        }

        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxSize()
                .clip(
                    shape = RoundedCornerShape(
                        topStart = 32.dp,
                        topEnd = 32.dp
                    )
                )
                .background(MaterialTheme.colorScheme.onPrimary)
                .padding(top = Dimensions.Paddings.screen)
                .padding(horizontal = Dimensions.Paddings.screen)
        ) {
            content()
        }
    }
}

@Composable
private fun Sample() {
    AppTitledPage(
        title = "Upload Documents",
        isBackEnabled = true,
        onBackPress = {}
    ) {

    }
}

@Preview(showBackground = true)
@Composable
private fun Preview() {
    AppTheme {
        Sample()
    }
}

