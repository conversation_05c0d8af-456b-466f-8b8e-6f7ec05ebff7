package feature.common.countries

import android.content.Context
import com.hbb20.CCPCountry
import data.utils.android.CommonValues
import keyless.data.utils.android.R
import java.util.Locale

object Countries {

    fun getFlagResource(countryCode: String, context: Context): Int {
        val countryCode = getCountryDialCode(countryCode.replace("+", ""), context, "")
        val c = CCPCountry()
        c.nameCode = countryCode!!.lowercase(Locale.getDefault())
        return getFlag(c)
    }

    fun getFlag(country: CCPCountry): Int {
        return when (country.nameCode.lowercase(Locale.getDefault())) {
            "ad" -> com.hbb20.R.drawable.flag_andorra
            "ae" -> com.hbb20.R.drawable.flag_uae
            "af" -> com.hbb20.R.drawable.flag_afghanistan
            "ag" -> com.hbb20.R.drawable.flag_antigua_and_barbuda
            "ai" -> com.hbb20.R.drawable.flag_anguilla
            "al" -> com.hbb20.R.drawable.flag_albania
            "am" -> com.hbb20.R.drawable.flag_armenia
            "ao" -> com.hbb20.R.drawable.flag_angola
            "aq" -> com.hbb20.R.drawable.flag_antarctica
            "ar" -> com.hbb20.R.drawable.flag_argentina
            "as" -> com.hbb20.R.drawable.flag_american_samoa
            "at" -> com.hbb20.R.drawable.flag_austria
            "au" -> com.hbb20.R.drawable.flag_australia
            "aw" -> com.hbb20.R.drawable.flag_aruba
            "ax" -> com.hbb20.R.drawable.flag_aland
            "az" -> com.hbb20.R.drawable.flag_azerbaijan
            "ba" -> com.hbb20.R.drawable.flag_bosnia
            "bb" -> com.hbb20.R.drawable.flag_barbados
            "bd" -> com.hbb20.R.drawable.flag_bangladesh
            "be" -> com.hbb20.R.drawable.flag_belgium
            "bf" -> com.hbb20.R.drawable.flag_burkina_faso
            "bg" -> com.hbb20.R.drawable.flag_bulgaria
            "bh" -> com.hbb20.R.drawable.flag_bahrain
            "bi" -> com.hbb20.R.drawable.flag_burundi
            "bj" -> com.hbb20.R.drawable.flag_benin
            "bl" -> com.hbb20.R.drawable.flag_saint_barthelemy // custom
            "bm" -> com.hbb20.R.drawable.flag_bermuda
            "bn" -> com.hbb20.R.drawable.flag_brunei
            "bo" -> com.hbb20.R.drawable.flag_bolivia
            "br" -> com.hbb20.R.drawable.flag_brazil
            "bs" -> com.hbb20.R.drawable.flag_bahamas
            "bt" -> com.hbb20.R.drawable.flag_bhutan
            "bw" -> com.hbb20.R.drawable.flag_botswana
            "by" -> com.hbb20.R.drawable.flag_belarus
            "bz" -> com.hbb20.R.drawable.flag_belize
            "ca" -> com.hbb20.R.drawable.flag_canada
            "cc" -> com.hbb20.R.drawable.flag_cocos // custom
            "cd" -> com.hbb20.R.drawable.flag_democratic_republic_of_the_congo
            "cf" -> com.hbb20.R.drawable.flag_central_african_republic
            "cg" -> com.hbb20.R.drawable.flag_republic_of_the_congo
            "ch" -> com.hbb20.R.drawable.flag_switzerland
            "ci" -> com.hbb20.R.drawable.flag_cote_divoire
            "ck" -> com.hbb20.R.drawable.flag_cook_islands
            "cl" -> com.hbb20.R.drawable.flag_chile
            "cm" -> com.hbb20.R.drawable.flag_cameroon
            "cn" -> com.hbb20.R.drawable.flag_china
            "co" -> com.hbb20.R.drawable.flag_colombia
            "cr" -> com.hbb20.R.drawable.flag_costa_rica
            "cu" -> com.hbb20.R.drawable.flag_cuba
            "cv" -> com.hbb20.R.drawable.flag_cape_verde
            "cw" -> com.hbb20.R.drawable.flag_curacao
            "cx" -> com.hbb20.R.drawable.flag_christmas_island
            "cy" -> com.hbb20.R.drawable.flag_cyprus
            "cz" -> com.hbb20.R.drawable.flag_czech_republic
            "de" -> com.hbb20.R.drawable.flag_germany
            "dj" -> com.hbb20.R.drawable.flag_djibouti
            "dk" -> com.hbb20.R.drawable.flag_denmark
            "dm" -> com.hbb20.R.drawable.flag_dominica
            "do" -> com.hbb20.R.drawable.flag_dominican_republic
            "dz" -> com.hbb20.R.drawable.flag_algeria
            "ec" -> com.hbb20.R.drawable.flag_ecuador
            "ee" -> com.hbb20.R.drawable.flag_estonia
            "eg" -> com.hbb20.R.drawable.flag_egypt
            "er" -> com.hbb20.R.drawable.flag_eritrea
            "es" -> com.hbb20.R.drawable.flag_spain
            "et" -> com.hbb20.R.drawable.flag_ethiopia
            "fi" -> com.hbb20.R.drawable.flag_finland
            "fj" -> com.hbb20.R.drawable.flag_fiji
            "fk" -> com.hbb20.R.drawable.flag_falkland_islands
            "fm" -> com.hbb20.R.drawable.flag_micronesia
            "fo" -> com.hbb20.R.drawable.flag_faroe_islands
            "fr" -> com.hbb20.R.drawable.flag_france
            "ga" -> com.hbb20.R.drawable.flag_gabon
            "gb" -> com.hbb20.R.drawable.flag_united_kingdom
            "gd" -> com.hbb20.R.drawable.flag_grenada
            "ge" -> com.hbb20.R.drawable.flag_georgia
            "gf" -> com.hbb20.R.drawable.flag_guyane
            "gg" -> com.hbb20.R.drawable.flag_guernsey
            "gh" -> com.hbb20.R.drawable.flag_ghana
            "gi" -> com.hbb20.R.drawable.flag_gibraltar
            "gl" -> com.hbb20.R.drawable.flag_greenland
            "gm" -> com.hbb20.R.drawable.flag_gambia
            "gn" -> com.hbb20.R.drawable.flag_guinea
            "gp" -> com.hbb20.R.drawable.flag_guadeloupe
            "gq" -> com.hbb20.R.drawable.flag_equatorial_guinea
            "gr" -> com.hbb20.R.drawable.flag_greece
            "gt" -> com.hbb20.R.drawable.flag_guatemala
            "gu" -> com.hbb20.R.drawable.flag_guam
            "gw" -> com.hbb20.R.drawable.flag_guinea_bissau
            "gy" -> com.hbb20.R.drawable.flag_guyana
            "hk" -> com.hbb20.R.drawable.flag_hong_kong
            "hn" -> com.hbb20.R.drawable.flag_honduras
            "hr" -> com.hbb20.R.drawable.flag_croatia
            "ht" -> com.hbb20.R.drawable.flag_haiti
            "hu" -> com.hbb20.R.drawable.flag_hungary
            "id" -> com.hbb20.R.drawable.flag_indonesia
            "ie" -> com.hbb20.R.drawable.flag_ireland
            "il" -> com.hbb20.R.drawable.flag_israel
            "im" -> com.hbb20.R.drawable.flag_isleof_man // custom
            "is" -> com.hbb20.R.drawable.flag_iceland
            "in" -> com.hbb20.R.drawable.flag_india
            "io" -> com.hbb20.R.drawable.flag_british_indian_ocean_territory
            "iq" -> com.hbb20.R.drawable.flag_iraq_new
            "ir" -> com.hbb20.R.drawable.flag_iran
            "it" -> com.hbb20.R.drawable.flag_italy
            "je" -> com.hbb20.R.drawable.flag_jersey
            "jm" -> com.hbb20.R.drawable.flag_jamaica
            "jo" -> com.hbb20.R.drawable.flag_jordan
            "jp" -> com.hbb20.R.drawable.flag_japan
            "ke" -> com.hbb20.R.drawable.flag_kenya
            "kg" -> com.hbb20.R.drawable.flag_kyrgyzstan
            "kh" -> com.hbb20.R.drawable.flag_cambodia
            "ki" -> com.hbb20.R.drawable.flag_kiribati
            "km" -> com.hbb20.R.drawable.flag_comoros
            "kn" -> com.hbb20.R.drawable.flag_saint_kitts_and_nevis
            "kp" -> com.hbb20.R.drawable.flag_north_korea
            "kr" -> com.hbb20.R.drawable.flag_south_korea
            "kw" -> com.hbb20.R.drawable.flag_kuwait
            "ky" -> com.hbb20.R.drawable.flag_cayman_islands
            "kz" -> com.hbb20.R.drawable.flag_kazakhstan
            "la" -> com.hbb20.R.drawable.flag_laos
            "lb" -> com.hbb20.R.drawable.flag_lebanon
            "lc" -> com.hbb20.R.drawable.flag_saint_lucia
            "li" -> com.hbb20.R.drawable.flag_liechtenstein
            "lk" -> com.hbb20.R.drawable.flag_sri_lanka
            "lr" -> com.hbb20.R.drawable.flag_liberia
            "ls" -> com.hbb20.R.drawable.flag_lesotho
            "lt" -> com.hbb20.R.drawable.flag_lithuania
            "lu" -> com.hbb20.R.drawable.flag_luxembourg
            "lv" -> com.hbb20.R.drawable.flag_latvia
            "ly" -> com.hbb20.R.drawable.flag_libya
            "ma" -> com.hbb20.R.drawable.flag_morocco
            "mc" -> com.hbb20.R.drawable.flag_monaco
            "md" -> com.hbb20.R.drawable.flag_moldova
            "me" -> com.hbb20.R.drawable.flag_of_montenegro // custom
            "mf" -> com.hbb20.R.drawable.flag_saint_martin
            "mg" -> com.hbb20.R.drawable.flag_madagascar
            "mh" -> com.hbb20.R.drawable.flag_marshall_islands
            "mk" -> com.hbb20.R.drawable.flag_macedonia
            "ml" -> com.hbb20.R.drawable.flag_mali
            "mm" -> com.hbb20.R.drawable.flag_myanmar
            "mn" -> com.hbb20.R.drawable.flag_mongolia
            "mo" -> com.hbb20.R.drawable.flag_macao
            "mp" -> com.hbb20.R.drawable.flag_northern_mariana_islands
            "mq" -> com.hbb20.R.drawable.flag_martinique
            "mr" -> com.hbb20.R.drawable.flag_mauritania
            "ms" -> com.hbb20.R.drawable.flag_montserrat
            "mt" -> com.hbb20.R.drawable.flag_malta
            "mu" -> com.hbb20.R.drawable.flag_mauritius
            "mv" -> com.hbb20.R.drawable.flag_maldives
            "mw" -> com.hbb20.R.drawable.flag_malawi
            "mx" -> com.hbb20.R.drawable.flag_mexico
            "my" -> com.hbb20.R.drawable.flag_malaysia
            "mz" -> com.hbb20.R.drawable.flag_mozambique
            "na" -> com.hbb20.R.drawable.flag_namibia
            "nc" -> com.hbb20.R.drawable.flag_new_caledonia // custom
            "ne" -> com.hbb20.R.drawable.flag_niger
            "nf" -> com.hbb20.R.drawable.flag_norfolk_island
            "ng" -> com.hbb20.R.drawable.flag_nigeria
            "ni" -> com.hbb20.R.drawable.flag_nicaragua
            "nl" -> com.hbb20.R.drawable.flag_netherlands
            "no" -> com.hbb20.R.drawable.flag_norway
            "np" -> com.hbb20.R.drawable.flag_nepal
            "nr" -> com.hbb20.R.drawable.flag_nauru
            "nu" -> com.hbb20.R.drawable.flag_niue
            "nz" -> com.hbb20.R.drawable.flag_new_zealand
            "om" -> com.hbb20.R.drawable.flag_oman
            "pa" -> com.hbb20.R.drawable.flag_panama
            "pe" -> com.hbb20.R.drawable.flag_peru
            "pf" -> com.hbb20.R.drawable.flag_french_polynesia
            "pg" -> com.hbb20.R.drawable.flag_papua_new_guinea
            "ph" -> com.hbb20.R.drawable.flag_philippines
            "pk" -> com.hbb20.R.drawable.flag_pakistan
            "pl" -> com.hbb20.R.drawable.flag_poland
            "pm" -> com.hbb20.R.drawable.flag_saint_pierre
            "pn" -> com.hbb20.R.drawable.flag_pitcairn_islands
            "pr" -> com.hbb20.R.drawable.flag_puerto_rico
            "ps" -> com.hbb20.R.drawable.flag_palestine
            "pt" -> com.hbb20.R.drawable.flag_portugal
            "pw" -> com.hbb20.R.drawable.flag_palau
            "py" -> com.hbb20.R.drawable.flag_paraguay
            "qa" -> com.hbb20.R.drawable.flag_qatar
            "re" -> com.hbb20.R.drawable.flag_martinique // no exact flag found
            "ro" -> com.hbb20.R.drawable.flag_romania
            "rs" -> com.hbb20.R.drawable.flag_serbia // custom
            "ru" -> com.hbb20.R.drawable.flag_russian_federation
            "rw" -> com.hbb20.R.drawable.flag_rwanda
            "sa" -> com.hbb20.R.drawable.flag_saudi_arabia
            "sb" -> com.hbb20.R.drawable.flag_soloman_islands
            "sc" -> com.hbb20.R.drawable.flag_seychelles
            "sd" -> com.hbb20.R.drawable.flag_sudan
            "se" -> com.hbb20.R.drawable.flag_sweden
            "sg" -> com.hbb20.R.drawable.flag_singapore
            "sh" -> com.hbb20.R.drawable.flag_saint_helena // custom
            "si" -> com.hbb20.R.drawable.flag_slovenia
            "sk" -> com.hbb20.R.drawable.flag_slovakia
            "sl" -> com.hbb20.R.drawable.flag_sierra_leone
            "sm" -> com.hbb20.R.drawable.flag_san_marino
            "sn" -> com.hbb20.R.drawable.flag_senegal
            "so" -> com.hbb20.R.drawable.flag_somalia
            "sr" -> com.hbb20.R.drawable.flag_suriname
            "ss" -> com.hbb20.R.drawable.flag_south_sudan
            "st" -> com.hbb20.R.drawable.flag_sao_tome_and_principe
            "sv" -> com.hbb20.R.drawable.flag_el_salvador
            "sx" -> com.hbb20.R.drawable.flag_sint_maarten
            "sy" -> com.hbb20.R.drawable.flag_syria
            "sz" -> com.hbb20.R.drawable.flag_swaziland
            "tc" -> com.hbb20.R.drawable.flag_turks_and_caicos_islands
            "td" -> com.hbb20.R.drawable.flag_chad
            "tg" -> com.hbb20.R.drawable.flag_togo
            "th" -> com.hbb20.R.drawable.flag_thailand
            "tj" -> com.hbb20.R.drawable.flag_tajikistan
            "tk" -> com.hbb20.R.drawable.flag_tokelau // custom
            "tl" -> com.hbb20.R.drawable.flag_timor_leste
            "tm" -> com.hbb20.R.drawable.flag_turkmenistan
            "tn" -> com.hbb20.R.drawable.flag_tunisia
            "to" -> com.hbb20.R.drawable.flag_tonga
            "tr" -> com.hbb20.R.drawable.flag_turkey
            "tt" -> com.hbb20.R.drawable.flag_trinidad_and_tobago
            "tv" -> com.hbb20.R.drawable.flag_tuvalu
            "tw" -> com.hbb20.R.drawable.flag_taiwan
            "tz" -> com.hbb20.R.drawable.flag_tanzania
            "ua" -> com.hbb20.R.drawable.flag_ukraine
            "ug" -> com.hbb20.R.drawable.flag_uganda
            "us" -> com.hbb20.R.drawable.flag_united_states_of_america
            "uy" -> com.hbb20.R.drawable.flag_uruguay
            "uz" -> com.hbb20.R.drawable.flag_uzbekistan
            "va" -> com.hbb20.R.drawable.flag_vatican_city
            "vc" -> com.hbb20.R.drawable.flag_saint_vicent_and_the_grenadines
            "ve" -> com.hbb20.R.drawable.flag_venezuela
            "vg" -> com.hbb20.R.drawable.flag_british_virgin_islands
            "vi" -> com.hbb20.R.drawable.flag_us_virgin_islands
            "vn" -> com.hbb20.R.drawable.flag_vietnam
            "vu" -> com.hbb20.R.drawable.flag_vanuatu
            "wf" -> com.hbb20.R.drawable.flag_wallis_and_futuna
            "ws" -> com.hbb20.R.drawable.flag_samoa
            "xk" -> com.hbb20.R.drawable.flag_kosovo
            "ye" -> com.hbb20.R.drawable.flag_yemen
            "yt" -> com.hbb20.R.drawable.flag_martinique // no exact flag found
            "za" -> com.hbb20.R.drawable.flag_south_africa
            "zm" -> com.hbb20.R.drawable.flag_zambia
            "zw" -> com.hbb20.R.drawable.flag_zimbabwe
            else -> com.hbb20.R.drawable.flag_transparent
        }
    }

    fun getTimeZone(): Array<Map<String, String>> {
        return arrayOf(
            mapOf("Etc/GMT-12" to "-12:00"),
            mapOf("Etc/GMT-11" to "-11:00"),
            mapOf("Pacific/Midway" to "-11:00"),
            mapOf("America/Adak" to "-10:00"),
            mapOf("America/Anchorage" to "-09:00"),
            mapOf("Pacific/Gambier" to "-09:00"),
            mapOf("America/Dawson_Creek" to "-08:00"),
            mapOf("America/Ensenada" to "-08:00"),
            mapOf("America/Los_Angeles" to "-08:00"),
            mapOf("America/Chihuahua" to "-07:00"),
            mapOf("America/Denver" to "-07:00"),
            mapOf("America/Belize" to "-06:00"),
            mapOf("America/Cancun" to "-06:00"),
            mapOf("America/Chicago" to "-06:00"),
            mapOf("Chile/EasterIsland" to "-06:00"),
            mapOf("America/Bogota" to "-05:00"),
            mapOf("America/Havana" to "-05:00"),
            mapOf("America/New_York" to "-05:00"),
            mapOf("America/Caracas" to "-04:30"),
            mapOf("America/Campo_Grande" to "-04:00"),
            mapOf("America/Glace_Bay" to "-04:00"),
            mapOf("America/Goose_Bay" to "-04:00"),
            mapOf("America/Santiago" to "-04:00"),
            mapOf("America/La_Paz" to "-04:00"),
            mapOf("America/Argentina/Buenos_Aires" to "-03:00"),
            mapOf("America/Montevideo" to "-03:00"),
            mapOf("America/Araguaina" to "-03:00"),
            mapOf("America/Godthab" to "-03:00"),
            mapOf("America/Miquelon" to "-03:00"),
            mapOf("America/Sao_Paulo" to "-03:00"),
            mapOf("America/St_Johns" to "-03:30"),
            mapOf("America/Noronha" to "-02:00"),
            mapOf("Atlantic/Cape_Verde" to "-01:00"),

            mapOf("UTC" to "UTC"),

            mapOf("Africa/Algiers" to "+01:00"),
            mapOf("Africa/Windhoek" to "+01:00"),
            mapOf("Atlantic/Azores" to "+01:00"),
            mapOf("Atlantic/Stanley" to "+01:00"),
            mapOf("Europe/Amsterdam" to "+01:00"),
            mapOf("Europe/Belgrade" to "+01:00"),
            mapOf("Europe/Brussels" to "+01:00"),
            mapOf("Europe/Belfast" to "+01:00"),
            mapOf("Africa/Abidjan" to "+00:00"),
            mapOf("Europe/Dublin" to "+01:00"),
            mapOf("Europe/Lisbon" to "+01:00"),
            mapOf("Europe/London" to "+01:00"),
            mapOf("Africa/Cairo" to "+02:00"),
            mapOf("Africa/Blantyre" to "+02:00"),
            mapOf("Asia/Beirut" to "+02:00"),
            mapOf("Asia/Damascus" to "+02:00"),
            mapOf("Asia/Gaza" to "+02:00"),
            mapOf("Asia/Jerusalem" to "+02:00"),
            mapOf("Africa/Addis_Ababa" to "+03:00"),
            mapOf("Asia/Riyadh" to "+03:00"),
            mapOf("Europe/Minsk" to "+03:00"),
            mapOf("Asia/Tehran" to "+03:30"),
            mapOf("Asia/Dubai" to "+04:00"),
            mapOf("Asia/Yerevan" to "+04:00"),
            mapOf("Europe/Moscow" to "+04:00"),
            mapOf("Asia/Kabul" to "+04:30"),
            mapOf("Asia/Tashkent" to "+05:00"),
            mapOf("Asia/Kolkata" to "+05:30"),
            mapOf("Asia/Katmandu" to "+05:45"),
            mapOf("Asia/Dhaka" to "+06:00"),
            mapOf("Asia/Yekaterinburg" to "+06:00"),
            mapOf("Asia/Rangoon" to "+06:30"),
            mapOf("Asia/Bangkok" to "+07:00"),
            mapOf("Asia/Novosibirsk" to "+07:00"),
            mapOf("Etc/GMT+8" to "+08:00"),
            mapOf("Asia/Hong_Kong" to "+08:00"),
            mapOf("Asia/Krasnoyarsk" to "+08:00"),
            mapOf("Australia/Perth" to "+08:00"),
            mapOf("Australia/Eucla" to "+08:45"),
            mapOf("Asia/Irkutsk" to "+09:00"),
            mapOf("Asia/Seoul" to "+09:00"),
            mapOf("Asia/Tokyo" to "+09:00"),
            mapOf("Australia/Adelaide" to "+09:30"),
            mapOf("Australia/Darwin" to "+09:30"),
            mapOf("Pacific/Marquesas" to "+09:30"),
            mapOf("Etc/GMT+10" to "+10:00"),
            mapOf("Australia/Brisbane" to "+10:00"),
            mapOf("Australia/Hobart" to "+10:00"),
            mapOf("Asia/Yakutsk" to "+10:00"),
            mapOf("Australia/Lord_Howe" to "+10:30"),
            mapOf("Asia/Vladivostok" to "+11:00"),
            mapOf("Pacific/Norfolk" to "+11:30"),
            mapOf("Etc/GMT+12" to "+12:00"),
            mapOf("Asia/Anadyr" to "+12:00"),
            mapOf("Asia/Magadan" to "+12:00"),
            mapOf("Pacific/Auckland" to "+12:00"),
            mapOf("Pacific/Chatham" to "+12:45"),
            mapOf("Pacific/Tongatapu" to "+13:00"),
            mapOf("Pacific/Kiritimati" to "+14:00")
        )
    }

    fun getCountryDialCode(countryId: String, context: Context, countryName: String): String? {
        var countryDialCode: String? = null
        val arrCountryCode = context.resources.getStringArray(R.array.DialingCountryCode)
        for (i in arrCountryCode.indices) {
            val arrDial = arrCountryCode[i].split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (countryName.isNotEmpty()) {
                if (
                    arrDial[0].trim { it <= ' ' } == countryId.trim() &&
                    arrDial[1].trim { it <= ' ' } == countryName.trim()
                ) {
                    countryDialCode = arrDial[1]
                    break
                }
            } else {
                if (arrDial[0].trim { it <= ' ' } == countryId.trim()) {
                    countryDialCode = arrDial[1]
                    break
                }
            }
        }
        return countryDialCode
    }
}