package feature.common.dialogs

import android.app.Dialog
import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.ProgressBar
import android.widget.TextView
import keyless.feature.common.R

interface OnActionEdit {
    fun onEdit()
}

fun dialogYesNo(
    context: Context,
    title: String,
    message: String? = null,
    listeneer: OnActionYesNo
) {
    val dialog = Dialog(context)
    dialog.setContentView(R.layout.layout_alert_dialog)
    dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
    dialog.setCancelable(false)
    val ivHeader = dialog.findViewById<TextView>(R.id.ivHeader)
    val tvTextA = dialog.findViewById<TextView>(R.id.tvTextA)
    val tvCancel = dialog.findViewById<TextView>(R.id.tvCancel)
    val tvSubmitA = dialog.findViewById<TextView>(R.id.tvSubmitA)
    ivHeader.text = title ?: ""
    tvTextA.text = message ?: ""
    dialog.setCanceledOnTouchOutside(false)
    dialog.window?.setLayout(
        WindowManager.LayoutParams.MATCH_PARENT,
        WindowManager.LayoutParams.WRAP_CONTENT
    )
    tvCancel.setOnClickListener {
        listeneer.onYes(it)
        dialog.dismiss()
    }
    tvSubmitA.setOnClickListener {
        listeneer.onNo(it)
        dialog.dismiss()
    }
    dialog.show()
}

interface OnActionYesNo {
    fun onYes(view: View)
    fun onNo(view: View)
    fun onClickData(view: View, data: String)
}

fun successDialogWithOkButton(context: Context, message: String, listener: OnActionOK) {
    val dialog = Dialog(context)
    dialog.setContentView(R.layout.layout_sucess_dialog_with_ok)
    dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
    dialog.setCancelable(false)
    dialog.setCanceledOnTouchOutside(false)
    dialog.window?.setLayout(
        WindowManager.LayoutParams.MATCH_PARENT,
        WindowManager.LayoutParams.WRAP_CONTENT
    )
    val tvText = dialog.findViewById<TextView>(R.id.tvText)
    val tvOk = dialog.findViewById<TextView>(R.id.tvOk)
    tvText.text = "$message"
    tvOk.setOnClickListener {
        dialog.dismiss()
        listener.onClickData()
    }
    dialog.show()
}

fun successDialogWithOkButton(context: Context, message: String, callback: () -> Unit) {
    successDialogWithOkButton(context, message, object: OnActionOK { override fun onClickData() { callback() } })
}

fun defaultDialog(context: Context, title: String, callback: () -> Unit) {
    defaultDialog(context, title, object: OnActionOK { override fun onClickData() { callback() } })
}

fun defaultDialog(context: Context, title: String, listener: OnActionOK) {
    val dialog = Dialog(context)
    dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
    dialog.window?.setBackgroundDrawableResource(R.drawable.white_all_corners_10)
    dialog.setCancelable(false)
    dialog.setContentView(R.layout.default_dialog_layout)
    val txtMsg = dialog.findViewById<TextView>(R.id.txtMsg)
    val okBtn = dialog.findViewById<TextView>(R.id.okBtn)
    txtMsg.text = title
    dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
        val displayRectangle = Rect()
        val window = dialog.window
        v.getWindowVisibleDisplayFrame(displayRectangle)
        val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
        val maxWidth = displayRectangle.width() * 0.8f // 60%
        window?.setLayout(maxWidth.toInt(), maxHeight)
    }
    okBtn.setOnClickListener {
        dialog.dismiss()
        listener.onClickData()
    }
    runCatching { dialog.show() }
}

fun dialogWithProgress(
    context: Context,
    title: String,
    callBack: (TextView, TextView, ProgressBar) -> Unit
) {
    val dialog = Dialog(context)
    dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
    dialog.window?.setBackgroundDrawableResource(R.drawable.white_all_corners_10)
    dialog.setCancelable(false)
    dialog.setContentView(R.layout.dialog_progress_layout)
    val txtMsg = dialog.findViewById<TextView>(R.id.txtMsg)
    val okBtn = dialog.findViewById<TextView>(R.id.okBtn)
    val progress = dialog.findViewById<ProgressBar>(R.id.progressBar2)
    txtMsg.text = title
    dialog.window?.decorView?.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
        val displayRectangle = Rect()
        val window = dialog.window
        v.getWindowVisibleDisplayFrame(displayRectangle)
        val maxHeight = WindowManager.LayoutParams.WRAP_CONTENT
        val maxWidth = displayRectangle.width() * 0.8f // 60%
        window?.setLayout(maxWidth.toInt(), maxHeight)
    }
    okBtn.setOnClickListener {
        dialog.dismiss()
    }
    dialog.show()
    callBack.invoke(txtMsg, okBtn, progress)
}

interface OnActionOK {
    fun onClickData()
}