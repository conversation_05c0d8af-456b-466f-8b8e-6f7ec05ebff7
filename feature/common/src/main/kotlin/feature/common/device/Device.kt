package feature.common.device

import android.content.Context
import android.os.Build
import com.google.firebase.crashlytics.internal.common.CommonUtils
import java.io.File

object Device {
    fun isRooted(context: Context?): Boolean {
        val isEmulator = CommonUtils.isEmulator(context)
        val buildTags = Build.TAGS
        return if (!isEmulator && buildTags != null && buildTags.contains("test-keys")) {
            true
        } else {
            var file = File("/system/app/Superuser.apk")
            if (file.exists()) {
                true
            } else {
                file = File("/system/xbin/su")
                !isEmulator && file.exists()
            }
        }
    }
}