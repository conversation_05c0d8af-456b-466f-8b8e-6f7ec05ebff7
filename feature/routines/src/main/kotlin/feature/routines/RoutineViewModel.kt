package feature.routines

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.liveData
import data.network.android.AddRoutineResponse
import data.network.android.ApiUtils
import data.network.android.GetRoutineResponse
import data.network.android.models.CreateRoutineRequest
import data.network.android.models.GetRoutineDeleteModel
import data.network.android.models.GetSingleResponse
import presentation.common.domain.repositories.ErrorMessageHandler

class RoutineViewModel : ViewModel() {

    private var _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    val _progress = MutableLiveData<Boolean>()
    var progress: LiveData<Boolean> = _progress

    fun hitGetRoutineApi(token: String, page: Int, isPagination: String) = liveData {
        ApiUtils.getRoutine(token, page, {
            _progress.value = false
            emit(it as GetRoutineResponse)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun hitAddRoutineApi(request: CreateRoutineRequest, token: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.addRoutine(token, request, {
            _progress.value = false
            emit(it as AddRoutineResponse)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun hitEditRoutineApi(id: String, request: CreateRoutineRequest, token: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.editRoutine(token, id, request, {
            _progress.value = false
            emit(it as AddRoutineResponse)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun getEditRoutineData(id: String, token: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.getSingleRoutine(token, id, {
            _progress.value = false
            emit(it as GetSingleResponse)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun hitDeleteTimeRangeApi(id: String, token: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.deleteTimeRange(token, id, {
            _progress.value = false
            emit(it as GetRoutineDeleteModel)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }

    fun hitDeleteRoutine(token: String, routineId: String) = liveData {
        _progress.value = false
        _progress.value = true
        ApiUtils.deleteRoutine(token, routineId, {
            _progress.value = false
            emit(it as GetRoutineResponse)
        }, {
            _progress.value = false
            _error.value = ErrorMessageHandler.handleException(it).toString()
        })
    }
}