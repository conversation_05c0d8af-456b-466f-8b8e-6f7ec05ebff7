package domain.regula.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.user.account.repositories.UserAccountRepository
import domain.regula.models.CheckInEvent
import domain.regula.models.SideEffect
import domain.regula.repositories.ScreenStateRepository
import domain.regula.repositories.SideEffectsRepository

internal class CheckInUseCase(
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val user: UserAccountRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {
    suspend fun checkIn(event: CheckInEvent.ConfirmCheckIn) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: CheckInEvent.ConfirmCheckIn) {
        val user = user.checkIn(
            name = event.name,
            documentNumber = event.documentNumber,
            documentType = event.documentType,
            bookingNumber = event.bookingNumber,
            similarity = event.similarity,
            status = event.status,
            expiryDate = event.expiryDate,
            documentImage = event.documentImage,
            capturedImage = event.capturedImage,
            identity1 = event.frontSide,
            identity2 = event.backSide,
            extra = event.extra
        )

        sideEffects.emit(SideEffect.CheckInSuccess(user))
    }
}