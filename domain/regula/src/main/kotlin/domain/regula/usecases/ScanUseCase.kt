package domain.regula.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.regula.documents.RegulaDocuments
import domain.regula.models.ScreenEvent
import domain.regula.models.ScreenState
import domain.regula.repositories.ScreenStateRepository
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

internal class ScanUseCase(
    private val screen: ScreenStateRepository,
    private val logger: Logger,
    private val documents: RegulaDocuments,
    private val status: StatusRepository
) {

    suspend fun scanFrontSide(event: ScreenEvent.ScanFrontSide) = logger.async() {
        documents.stopScan()
        scanFrontSideJob(event)
    }

    private suspend fun scanFrontSideJob(event: ScreenEvent.ScanFrontSide) = logger.async() {
        when (screen.current.state) {
            ScreenState.DocumentTypeState.NONE -> throw IllegalStateException("Document type is not selected")
            ScreenState.DocumentTypeState.UAE_ID -> {
                val result = documents.scanId()
                if (
                    result.typeDescription.lowercase().contains("passport") ||
                    result.portrait == null ||
                    result.country != "ARE"
                ) {
                    throw IllegalStateException(
                        "Invalid document scanned"
                    )
                }
                val expiryDate = runCatching { LocalDate.parse(result.expiryDate!!) }.getOrNull()
                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                if (expiryDate != null && expiryDate < today) {
                    throw IllegalStateException("Document expired")
                }

                if (screen.current.backSide != null && result.identity != screen.current.backSide?.identity) {
                    screen.update { it.copy(frontSide = null, backSide = null) }
                    throw IllegalStateException("Identity mismatch")
                }

                screen.update {
                    it.copy(
                        frontSide = result,
                        backSide = null
                    )
                }
            }

            ScreenState.DocumentTypeState.PASSPORT -> {
                val result = documents.scanPassport()
                if (!result.typeDescription.lowercase().contains("passport")) {
                    throw IllegalStateException(
                        "Invalid document scanned"
                    )
                }
                screen.update {
                    it.copy(
                        frontSide = result,
                        backSide = null
                    )
                }
            }
        }
    }

    suspend fun scanBackSide(event: ScreenEvent.ScanBackSide) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { scanBackSideJob(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun scanBackSideJob(event: ScreenEvent.ScanBackSide) = logger.async() {
        val result = when (screen.current.state) {
            ScreenState.DocumentTypeState.NONE -> throw IllegalStateException("Document type is not selected")
            ScreenState.DocumentTypeState.UAE_ID -> documents.scanId()
            ScreenState.DocumentTypeState.PASSPORT -> throw IllegalStateException("Passport does not have back side")
        }
        if (
            result.typeDescription.lowercase().contains("passport") ||
            result.portrait != null ||
            result.country != "ARE"
        ) {
            throw IllegalStateException(
                "Invalid document scanned"
            )
        }

        if (result.identity != screen.current.frontSide?.identity) {
            screen.update { it.copy(frontSide = null, backSide = null) }
            throw IllegalStateException("Identity mismatch")
        }

        val expiryDate = runCatching { LocalDate.parse(result.expiryDate!!) }.getOrNull()
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        if (expiryDate != null && expiryDate < today) {
            screen.update { it.copy(frontSide = null, backSide = null) }
            throw IllegalStateException("Document expired")
        }

        screen.update {
            it.copy(
                backSide = result
            )
        }
    }
}