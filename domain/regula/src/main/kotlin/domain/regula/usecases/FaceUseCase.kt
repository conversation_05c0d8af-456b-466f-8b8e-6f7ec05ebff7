package domain.regula.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.regula.documents.MatchFacesResult
import core.regula.documents.RegulaFace
import domain.regula.models.ScreenEvent
import domain.regula.models.SideEffect
import domain.regula.repositories.ScreenStateRepository
import domain.regula.repositories.SideEffectsRepository

internal class FaceUseCase(
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val face: RegulaFace,
    private val status: StatusRepository
) {

    suspend fun scanFace(event: ScreenEvent.ScanFace) = logger.async() {
        scanFaceJob(event)

        kotlinx.coroutines.delay(1000)
    }

    private suspend fun scanFaceJob(event: ScreenEvent.ScanFace) = logger.async() {
        face.stopLiveNess()
        val image = face.liveNessCheck()
        screen.update { it.copy(faceVerificationImage = image) }
        var similarities: MatchFacesResult? = null
        if (image?.image != null) {
            similarities = face.matchFaces(
                firstImage = screen.current.frontSide?.portrait ?: screen.current.backSide?.portrait
                    ?: throw IllegalStateException("Portrait not found"),
                secondImage = image.image!!
            )
        }
        screen.update { it.copy(similarityResults = similarities) }
        if (image?.image != null) {
            sideEffects.emit(SideEffect.NavigateToResultsScreen)
        }
    }
}