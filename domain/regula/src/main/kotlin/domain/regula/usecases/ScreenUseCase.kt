package domain.regula.usecases

import core.common.message.Message
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.repository.log
import core.regula.documents.RegulaDocuments
import core.regula.documents.RegulaFace
import domain.regula.models.ScreenEvent
import domain.regula.models.SideEffect
import domain.regula.repositories.ScreenStateRepository
import domain.regula.repositories.SideEffectsRepository
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch

internal class ScreenUseCase(
    private val documents: RegulaDocuments,
    private val face: RegulaFace,
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun initDocumentScreen(event: ScreenEvent.InitDocumentScreen) = logger.async() {
        val loading = status.load(Message.fromString(""))

        launch {
            val job1 = launch { documents.initialize() }
            val job2 = launch { face.initialize() }
            joinAll(job1, job2)
            status.removeLoading(loading)
        }
        screen.update {
            it.copy(bookingNumber = event.bookingNumber)
        }
    }

    fun endDocumentScreen() = logger.log {
        documents.stopScan()
        documents.deInitialize()
        face.stopLiveNess()
        face.deInitialize()
    }

    fun selectDocumentType(event: ScreenEvent.SelectDocumentType) = logger.log {
        screen.update {
            val emptyResults = null
            it.copy(
                state = event.state,
                frontSide = emptyResults,
                backSide = emptyResults,
                faceVerificationImage = null
            )
        }
    }

    suspend fun initFaceScreen(event: ScreenEvent.InitFaceScreen) = logger.async() {
        screen.update {
            it.copy(
                faceVerificationImage = null
            )
        }
    }

    suspend fun endFaceScreen(event: ScreenEvent.EndFaceScreen) = logger.async() {
    }

    suspend fun documentsNextClick(event: ScreenEvent.DocumentsNextClick) = logger.async() {
        sideEffects.emit(SideEffect.NavigateToFaceVerification)
    }

    suspend fun faceNextClick(event: ScreenEvent.FaceNextClick) = logger.async() {
        sideEffects.emit(SideEffect.NavigateToResultsScreen)
    }
}