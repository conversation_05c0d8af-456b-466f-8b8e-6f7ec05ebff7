package domain.settings.support.usecases

import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.locks.logs.repostiories.LockLogsRepository
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import domain.settings.support.models.DiagnosticEvent
import domain.settings.support.models.SideEffect
import domain.settings.support.repositories.SideEffectsRepository

internal class SendLockLogsUseCase(
    private val lockLogs: LockLogsRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: DiagnosticEvent.SendLogs) {
        val result = status.execute(
            loading = Messages.sendLogsLoading,
            success = Messages.sendLogsSuccess,
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: DiagnosticEvent.SendLogs) = logger.async() {
        val logs = lockLogs.getLogs(event.internalId)

        if (logs.isEmpty()) {
            sideEffects.emit(SideEffect.NoLogsFound)
            return@async
        }

        val message = lockLogs.syncLogs(
            internalId = event.internalId,
            authentication = event.authentication
        )

        sideEffects.emit(SideEffect.SendLogsResponse(message = message))
    }
}