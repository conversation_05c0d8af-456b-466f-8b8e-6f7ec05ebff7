package domain.settings.support.usecases

import core.lock.common.models.LockBrand
import core.locks.manager.LocksManager
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.permissions.manager.BluetoothManager
import core.permissions.manager.PermissionsManager
import core.permissions.manager.models.Permission
import core.permissions.manager.models.Response
import data.common.preferences.Preferences
import data.lock.common.lock.models.lock.Lock
import data.user.home.repositories.locks
import domain.settings.support.models.NearbyLock
import domain.settings.support.models.ScreenEvent
import domain.settings.support.repositories.ScreenStateRepository
import domain.settings.support.repositories.nearby
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.datetime.Clock

internal class NearByUseCase(
    private val state: ScreenStateRepository,
    private val manager: LocksManager,
    private val bluetooth: BluetoothManager,
    private val permissions: PermissionsManager,
    private val logger: Logger
) {

    private var rayonicsScanJob: Job? = null
    private var iseoScanJob: Job? = null
    private var ttlockScanJob: Job? = null
    private var collectJob: Job? = null

    suspend fun resume(event: ScreenEvent.ResumeNearBy) = logger.async() {
        if (!checkPermissions() && !bluetooth.enableBluetooth()) return@async
        val locks = Preferences.locks()

        collectJob = launch {
            state.update {
                val state = it.nearby ?: return@update it

                it.copy(
                    state = state.copy(showRefresh = false, devices = listOf())
                )
            }

            withTimeoutOrNull(30_000) {
                manager
                    .scan()
                    .collect { bluetoothLock ->
                        val nearbyState = state.get().nearby ?: return@collect
                        val nearbyLocks = nearbyState.devices

                        if (nearbyLocks.find { it.name == bluetoothLock.name } != null) return@collect

                        state.update {
                            val state = it.nearby ?: return@update it
                            it.copy(
                                state = state.copy(
                                    devices = state.devices + toNearBy(
                                        name = bluetoothLock.name,
                                        brand = bluetoothLock.brand,
                                        locks = locks
                                    )
                                )
                            )
                        }
                    }
            }

            cancelScans()

            collectJob?.cancel()
        }
    }

    suspend fun pause(event: ScreenEvent.PauseNearBy) = logger.async() {
        cancelScans()
        collectJob?.cancel()
    }

    private suspend fun checkPermissions(): Boolean = logger.async() {
        if (
            !permissions.check(Permission.LocationPrecise) &&
            !permissions.request(listOf(Permission.LocationPrecise)).all { it.result == Response.Result.Granted }
        ) {
            return@async false
        }

        if (!bluetooth.isPermissionGranted() && !bluetooth.requestPermission()) return@async false

        return@async true
    }

    private fun toNearBy(name: String, brand: LockBrand, locks: List<Lock>): NearbyLock {
        val lock = locks.find { it.lockUid == name } ?: locks.find { it.uniqueKey == name }
        val providerBrand = LockBrand.fromString(lock?.provider ?: "")
        val address = if (lock == null) {
            ""
        } else {
            "Address: " + lock.property.apartmentNumber + ", " + lock.property.floor + " Floor"
        }
        return NearbyLock(
            brand = if (providerBrand != LockBrand.Lock) providerBrand else brand,
            id = lock?.id ?: Clock.System.now().toString(),
            name = lock?.name ?: name,
            model = name,
            isAvailable = lock != null,
            address = address
        )
    }

    private suspend fun cancelScans() = logger.async() {
        rayonicsScanJob?.cancel()
        iseoScanJob?.cancel()
        ttlockScanJob?.cancel()
        state.update {
            val state = it.nearby ?: return@update it

            it.copy(state = state.copy(showRefresh = true))
        }
    }
}