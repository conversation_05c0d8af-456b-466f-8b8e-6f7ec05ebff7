package domain.settings.profile.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.user.account.repositories.UserAccountRepository
import domain.settings.profile.models.ProfileEvent
import domain.settings.profile.models.SideEffect
import domain.settings.profile.repositories.SideEffectsRepository

internal class GetUserProfileUseCase(
    private val userAccount: UserAccountRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: ProfileEvent.GetUserProfile) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            sideEffects.emit(SideEffect.ErrorToast(result.error))
            throw result.error
        }
    }

    private suspend fun job(event: ProfileEvent.GetUserProfile) = logger.async() {
        val response = userAccount.getUserProfile()

        sideEffects.emit(SideEffect.UserProfile(response))
    }
}