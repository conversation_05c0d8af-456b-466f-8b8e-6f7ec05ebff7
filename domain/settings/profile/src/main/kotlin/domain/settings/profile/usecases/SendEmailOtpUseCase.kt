package domain.settings.profile.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.user.account.repositories.UserAccountRepository
import domain.settings.profile.models.EmailEvent
import domain.settings.profile.models.SideEffect
import domain.settings.profile.repositories.SideEffectsRepository

internal class SendEmailOtpUseCase(
    private val logger: Logger,
    private val userAccount: UserAccountRepository,
    private val sideEffects: SideEffectsRepository,
    private val status: StatusRepository
) {

    suspend fun execute(event: EmailEvent.SendOtp) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            sideEffects.emit(SideEffect.ErrorToast(result.error))
            throw result.error
        }
    }

    private suspend fun job(event: EmailEvent.SendOtp) {
        val response = userAccount.sendOtpMail(email = event.email)
        sideEffects.emit(SideEffect.OnSendOtp(response))
    }
}