package data.settings.company.usecases.company

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyRepository
import data.settings.company.models.CompanyEvent
import data.settings.company.models.SideEffect
import data.settings.company.repositories.SideEffectsRepository

internal class UpdateCompanyProfileUseCase(
    private val company: CompanyRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: CompanyEvent.UpdateCompany) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = {
                job(event)
            }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: CompanyEvent.UpdateCompany) = logger.async() {
        val response = company.updateCompanyProfile(
            companyName = event.companyName,
            address = event.address,
            country = event.country,
            city = event.city,
            trnNumber = event.trnNumber,
            zipCode = event.zipCode,
            checkIn = event.checkIn,
            businessType = event.businessType,
            businessLia = event.businessLia,
            tradeLicenseNumber = event.tradeLicenseNumber,
            timezoneOffset = event.timezoneOffset,
            timezoneName = event.timezoneName
        )

        sideEffects.emit(SideEffect.Toast(response))
    }
}