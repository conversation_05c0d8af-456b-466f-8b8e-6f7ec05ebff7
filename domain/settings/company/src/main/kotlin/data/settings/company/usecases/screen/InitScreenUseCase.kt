package data.settings.company.usecases.screen

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyRepository
import data.settings.company.models.ScreenEvent
import data.settings.company.repositories.ScreenStateRepository

internal class InitScreenUseCase(
    private val company: CompanyRepository,
    private val screen: ScreenStateRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = {
                job(event)
            }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async() {
        val profile = company.getCompanyProfile()
        val lias = company.getLias()

        screen.update {
            it.copy(
                companyProfile = profile,
                lias = lias
            )
        }
    }
}