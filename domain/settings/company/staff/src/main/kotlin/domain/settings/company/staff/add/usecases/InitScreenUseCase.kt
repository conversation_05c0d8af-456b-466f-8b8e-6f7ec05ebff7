package domain.settings.company.staff.add.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.add.models.ScreenEvent
import domain.settings.company.staff.add.models.ScreenState
import domain.settings.company.staff.add.repositories.ScreenStateRepository

internal class InitScreenUseCase(
    private val screen: ScreenStateRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val staff: CompanyStaffRepository
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async() {
        val member = event.memberId?.let { it1 -> staff.getById(it1) }
        screen.update {
            ScreenState(
                editMember = member,
                status = status.stream.value,
                isVerified = event.isVerified,
                selectedRole = member?.role
            )
        }
    }
}