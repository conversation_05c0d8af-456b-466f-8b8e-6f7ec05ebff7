package domain.settings.company.staff.manage.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.manage.models.ScreenEvent
import domain.settings.company.staff.manage.repositories.ScreenStateRepository

internal class InitScreenUseCase(
    private val screen: ScreenStateRepository,
    private val companyStaff: CompanyStaffRepository,
    private val status: StatusRepository,
    private val logger: Logger
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async() {
        val staff = companyStaff.getAllStaff()
        screen.update { it.copy(staff = staff) }
    }
}