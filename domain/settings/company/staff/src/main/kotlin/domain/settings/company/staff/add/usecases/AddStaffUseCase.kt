package domain.settings.company.staff.add.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.add.models.SideEffect
import domain.settings.company.staff.add.models.StaffEvent
import domain.settings.company.staff.add.repositories.SideEffectsRepository

internal class AddStaffUseCase(
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val staff: CompanyStaffRepository
) {

    suspend fun execute(event: StaffEvent.AddStaff) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: StaffEvent.AddStaff) = logger.async() {
        val response = staff.addStaff(
            countryCode = event.countryCode,
            email = event.email,
            firstName = event.firstName,
            lastName = event.lastName,
            mobileNumber = event.mobileNo,
            passportNumber = event.passport,
            role = event.roleId,
            username = event.userName
        )

        sideEffects.emit(SideEffect.Success(response))
    }
}