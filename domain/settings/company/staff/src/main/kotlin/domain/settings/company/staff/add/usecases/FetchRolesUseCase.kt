package domain.settings.company.staff.add.usecases

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.company.repositories.CompanyStaffRepository
import domain.settings.company.staff.add.models.ScreenEvent
import domain.settings.company.staff.add.models.SideEffect
import domain.settings.company.staff.add.repositories.ScreenStateRepository
import domain.settings.company.staff.add.repositories.SideEffectsRepository

internal class FetchRolesUseCase(
    private val screen: ScreenStateRepository,
    private val sideEffects: SideEffectsRepository,
    private val logger: Logger,
    private val status: StatusRepository,
    private val staff: CompanyStaffRepository
) {

    suspend fun execute(event: ScreenEvent.ClickToSelectRole) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }
    }

    private suspend fun job(event: ScreenEvent.ClickToSelectRole) = logger.async() {
        val roles = staff.getRoles()
        sideEffects.emit(SideEffect.RolesResponse(roles))
    }
}