package domain.settings.checkin.usecases.screen

import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.common.preferences.Preferences
import data.user.guest.repositories.GuestRepository
import domain.settings.checkin.models.ScreenEvent
import domain.settings.checkin.repositories.ScreenStateRepository

internal class InitScreenUseCase(
    private val screen: ScreenStateRepository,
    private val repository: GuestRepository,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async() {
        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { repository.get(authentication = Preferences.authenticationToken.get()) }
        )

        if (result is AsyncResult.Fail) {
            throw result.error
        }

        screen.update { it.copy(checkins = result.result) }
    }
}