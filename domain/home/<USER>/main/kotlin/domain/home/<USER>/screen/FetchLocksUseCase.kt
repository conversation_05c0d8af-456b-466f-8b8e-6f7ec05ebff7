package domain.home.usecases.screen

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.lock.common.lock.models.lock.UserHomeResponse
import data.user.home.repositories.UserHomeRepository

internal class FetchLocksUseCase(
    private val userHomeRepository: UserHomeRepository,
    private val logger: Logger
) {

    suspend fun execute(token: String, uid: String, isAdmin: Boolean): UserHomeResponse = logger.async() {
        userHomeRepository.fetchUserHomeData(token = token, uid = uid, isAdmin = isAdmin)
    }
}