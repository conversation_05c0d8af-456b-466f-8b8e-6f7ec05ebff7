package domain.common

import core.common.coroutines.CoroutineDispatchers
import core.common.error.KError
import core.common.error.causes
import core.common.status.CommonStatus
import core.common.status.StatusRepository
import core.http.client.HttpError
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import core.monitoring.common.test.CLILogger
import kotlinx.coroutines.cancel
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.Before
import kotlin.test.Test

class ErrorHandlerTest {

    private lateinit var errorHandler: ErrorHandler
    private lateinit var logger: Logger
    private lateinit var status: StatusRepository

    @Before
    fun setUp() {
        logger = CLILogger(Unit)
        status = CommonStatus(CoroutineDispatchers())
        errorHandler = ErrorHandler(status, logger)
    }

    @Test
    fun handleHttp() = runTest {
        errorHandler.async({ assert(it.causes().size == 3) }) { failHttp() }
        assert(true)
    }

    @Test
    fun handleCancellation() = runTest {
        errorHandler.async({ assert(false) }) { failCancellation() }
        assert(true)
    }

    @Test
    fun details() = runTest {
        errorHandler.async({
            println("Details: ${(it as KError.Fatal).details}")
            println("Call location: ${it.callLocation}")
        }) { step1() }
    }

    private suspend fun failHttp(): Unit = logger.async {
        throw HttpError(
            message = "You did something wrong",
            cause = Exception("You did something wrong"),
            callLocation = "Http error",
            responseCode = 400,
            responseCodeDescription = "Bad request",
            details = mapOf(),
            responseBody = ""
        )
    }

    private suspend fun failCancellation(): Unit = logger.async {
        currentCoroutineContext().cancel()
        delay(4)
    }

    private suspend fun step1(): Unit = logger.async {
        logger.context("step", "1")
        step2()
    }

    private suspend fun step2(): Unit = logger.async { step3() }
    private suspend fun step3(): Unit = logger.async {
        logger.context("step", "3")
        step4()
    }

    private suspend fun step4(): Unit = logger.async { throw Exception("Step 4 Errorred!!!") }
}