package domain.common

import core.common.error.isAny
import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.error.HttpAuthenticationError
import data.error.handle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

// TODO Move to data common
class ErrorHandler(
    private val status: StatusRepository,
    private val logger: Logger
    // private val user: UserRepository
) {
    suspend fun <T> async(onError: suspend (Exception) -> Unit = {}, work: suspend () -> T): T? {
        return try {
            work()
        } catch (ex: HttpAuthenticationError) {
            ex.printStackTrace()
            ex.handle(logger, status)
            null
        } catch (ex: CancellationException) {
            null
        } catch (ex: Exception) {
            if (ex.isAny<CancellationException>()) return null
            ex.printStackTrace()
            ex.handle(logger, status)
            onError(ex)
            null
        }
    }

    fun <T> execute(work: () -> T): T? {
        return try {
            work()
        } catch (ex: HttpAuthenticationError) {
            ex.printStackTrace()
            ex.handle(logger) {
                CoroutineScope(Dispatchers.Default).launch { status.fail(it) }
            }
            null
        } catch (ex: Exception) {
            ex.printStackTrace()
            ex.handle(logger) {
                CoroutineScope(Dispatchers.Default).launch { status.fail(it) }
            }
            null
        }
    }
}